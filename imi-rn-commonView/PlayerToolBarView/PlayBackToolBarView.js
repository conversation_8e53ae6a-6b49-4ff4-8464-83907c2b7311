/**
 * LivePlayerToolBarView.js 直播的toolbar
 *
 * @property {string} qualityTitle - 清晰度文案
 * @property {func} qualityPress - 清晰度点击事件
 * @property {boolean} qualityDisabled - 清晰度是否可点击
 *
 * @property {func} mutePress - 静音点击事件
 * @property {boolean} muteDisabled - 静音是否可点击
 * @property {boolean} mute - 是否静音
 *
 * @property {func} screenshotPress - 截图点击事件
 * @property {boolean} screenshotDisabled - 是否可点击
 *
 * @property {func} recordPress - 录像点击事件
 * @property {boolean} recordDisabled - 是否可点击
 * @property {array} recording - 是否录像中
 *
 * @Deprecated @property {func} warningPress - 报警点击事件
 * @Deprecated @property {boolean} warningDisabled - 是否可点击
 *
 * @property {func} fullscreenPress - 全屏点击事件
 * @property {boolean} fullscreenDisabled - 是否可点击
 *
 * @property {array} moreItems - 更多自定义按钮 参考[{item:{isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},insertIndex:0},]
 *
 * 示例：
 * <LivePlayerToolBarView
 *  qualityData={this.state.qualityData}
 *  qualityIndex={this.state.qualityIndex}
 *  fullscreenPress={this._onPressFullScreen}
 *  screenshotPress={this._onPressScreenshot}
 *  recordPress={this._onPressRecord}
 *  recording={this.state.recording} />
 *
 * <AUTHOR>
 * @date 2020/12/28
 */

import React, {Component} from 'react';
import PlayerToolBarView from './PlayerToolBarView';
import PropTypes from 'prop-types';
import {LetDevice} from '../../imilab-rn-sdk';

export default class PlayBackToolBarView extends Component {
  static propTypes = {
    playPress: PropTypes.func,
    playDisabled: PropTypes.bool,
    play: PropTypes.bool,

    speedTitle: PropTypes.array,
    speedPress: PropTypes.func,
    speedDisabled: PropTypes.bool,
    speedIndex: PropTypes.number,

    mutePress: PropTypes.func,
    muteDisabled: PropTypes.bool,
    mute: PropTypes.bool,

    screenshotPress: PropTypes.func,
    screenshotDisabled: PropTypes.bool,

    recordPress: PropTypes.func,
    recordDisabled: PropTypes.bool,
    recording: PropTypes.bool,

    /* warningPress: PropTypes.func,
         warningDisabled: PropTypes.bool,*/

    fullscreenPress: PropTypes.func,
    fullscreenDisabled: PropTypes.bool,

    moreItems: PropTypes.array,
    accessibilityLabel: PropTypes.array,
    isShowSpeedItem: PropTypes.bool,
  };

  static defaultProps = {
    playPress: null,
    playDisabled: false,
    play: true,

    mutePress: null,
    muteDisabled: false,
    mute: true,

    speedTitle: ['1X', '2X', '4X', '8X', '16X'],
    speedPress: null,
    speedDisabled: false,
    speedIndex: 0,

    screenshotPress: null,
    screenshotDisabled: false,

    recordPress: null,
    recordDisabled: false,
    recording: false,

    /* warningPress: null,
         warningDisabled: false,*/

    fullscreenPress: null,
    fullscreenDisabled: false,

    moreItems: [],
    accessibilityLabel: [
      'cloud_storage_1X',
      'cloud_storage_2X',
      'cloud_storage_4X',
      'cloud_storage_8X',
      'cloud_storage_16X',
    ],
  };

  constructor(props, context) {
    super(props, context);
    this.state = {};
  }

  render() {
    let items = [
      {
        isText: false,
        data: [require('./res/player_toolbar_playing.png'), require('./res/player_toolbar_pause.png')],
        onPress: this.props.playPress,
        disabled: this.props.playDisabled,
        dataIndex: this.props.play ? 0 : 1,
        accessibilityLabel: ['cloud_storage_playing_on', 'cloud_storage_playing_off'],
      },
      {
        isText: false,
        data: [require('./res/player_toolbar_mute.png'), require('./res/player_toolbar_volume.png')],
        onPress: this.props.mutePress,
        disabled: this.props.muteDisabled,
        dataIndex: this.props.mute ? 0 : 1,
        accessibilityLabel: ['cloud_storage_mute_off', 'cloud_storage_mute_on'],
      },
      {
        isText: true,
        data: this.props.speedTitle,
        onPress: this.props.speedPress,
        disabled: this.props.speedDisabled,
        dataIndex: this.props.speedIndex,
        accessibilityLabel: this.props.accessibilityLabel,
      },
      {
        isText: false,
        data: [require('./res/player_toolbar_screenshot.png')],
        onPress: this.props.screenshotPress,
        disabled: this.props.screenshotDisabled,
        dataIndex: 0,
        accessibilityLabel: ['cloud_storage_screenshots'],
        key: 'shot',
      },
      {
        isText: false,
        data: [require('./res/player_toolbar_recording.png'), require('./res/player_toolbar_record.png')],
        onPress: this.props.recordPress,
        disabled: this.props.recordDisabled,
        dataIndex: this.props.recording ? 0 : 1,
        accessibilityLabel: ['cloud_storage_record_screen_on', 'cloud_storage_record_screen_off'],
        key: 'record',
      },
      /*{
            isText: false,
            data: [require("./res/player_toolbar_warning.png")],
            onPress: this.props.warningPress,
            disabled: this.props.warningDisabled,
            dataIndex: 0,
        },*/ {
        isText: false,
        data: [require('./res/player_toolbar_fullscreen.png')],
        onPress: this.props.fullscreenPress,
        disabled: this.props.fullscreenDisabled,
        dataIndex: 0,
        accessibilityLabel: ['cloud_storage_full_screen'],
      },
    ];
    if (this.props.moreItems.length > 0) {
      this.props.moreItems.forEach((item, index) => {
        if (item.insertIndex < items.length) {
          items.splice(item.insertIndex, 0, item.item);
        } else {
          items.push(item.item);
        }
      });
    }
    if (LetDevice.model === 'a1l4Z7lJ1ns' || LetDevice.model === 'a1yMb5JVWDa' || LetDevice.model === 'a1J6gvKPIGI') {
      //510智能门目前不支持倍速 026,021e01,036不支持倍速播放
      items.splice(2, 1);
    }
    if (!this.props.isShowSpeedItem) {
      // 026,021e01,036 回看不支持倍速播放  云存支持
      items.splice(2, 1);
    }
    // 云存储不支持录像
    if (this.props.ignoreRecord) {
      // 获取当前所在位置
      let currentIndex = -1;
      Array.isArray(items) && items.map((res, index) => {
        if (res.key === 'record') {
          currentIndex = index
        }
      })
      currentIndex !== -1 && items.splice(currentIndex, 1);
    }
    // 不支持截图
    if (this.props.ignoreShot) {
      // 获取当前所在位置
      let currentIndex = -1;
      Array.isArray(items) && items.map((res, index) => {
        if (res.key === 'shot') {
          currentIndex = index
        }
      })
      currentIndex !== -1 && items.splice(currentIndex, 1);
    }
    // if (LetDevice.model === "a1l4Z7lJ1ns" || LetDevice.model == "a1FKrifIRwH" || LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8") {//510智能门目前不支持倍速 026,021e01,036不支持倍速播放
    //     items.splice(2, 1)
    // }
    return <PlayerToolBarView items={items} />;
  }
}
