/*
 * 作者：sunhongda
 * 文件：Utils.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

/**
 *  常用工具类
 */
import React from 'react';
import {DeviceEventEmitter, Dimensions, Keyboard, Platform, Text} from 'react-native';
import {showToast} from '../../imilab-design-ui/src/widgets/IMIToast';

const {width, height} = Dimensions.get('window'); //正常形态下，即竖屏时的宽高
const IPHONE14_H = 844
const IPHONE14_W = 393
const IPHONE14_MAX = 932
const IPHONE14_PLUS = 926
const IPHONE14_MINI = 852
const { height: D_HEIGHT, width: D_WIDTH } = Dimensions.get('window');

//此方法在横屏模式下检测会错误
// const isIphoneX = (Platform.OS === 'ios' && (Number(((height / width) + "").substr(0, 4)) * 100) === 216);
//20220718@byh 使用这个方法代替
const isIphoneX = (Platform.OS === 'ios' && ((Number(((height / width) + "").substr(0, 4)) * 100) === 216 ||
    (Number(((width / height) + "").substr(0, 4)) * 100) === 216));

const iphone16Promax = (Platform.OS === 'ios' && ((Number(((height / width) + "").substr(0, 4)) * 100) === 217 ||
(Number(((width / height) + "").substr(0, 4)) * 100) === 217));

/**
 * 是否debug
 * @returns {boolean}
 */
export function isDebug() {
    return __DEV__;
}

/**
 * 若文本为空时，showEmptyTip
 * @param content
 * @param tip
 * @returns {boolean}
 */
export function emptyTip(content, tip) {
    let empty = isEmpty(content) || content === false;
    empty && showToast(tip);
    return empty;
}

/**
 * 判断两个对象是否内容相等
 * @param a
 * @param b
 * @returns {boolean}
 */
export function isObjectValueEqual(a, b) {
    // 判断两个对象是否指向同一内存，指向同一内存返回true
    // if (a === b) return true;

    // 获取两个对象键值数组
    let aProps = Object.getOwnPropertyNames(a);
    let bProps = Object.getOwnPropertyNames(b);
    // 判断两个对象键值数组长度是否一致，不一致返回false
    if (aProps.length !== bProps.length) return false;
    // 遍历对象的键值
    for (let prop in a) {
        // 判断a的键值，在b中是否存在，不存在，返回false
        if (b.hasOwnProperty(prop)) {
            // 判断a的键值是否为对象，是则递归，不是对象直接判断键值是否相等，不相等返回false
            if (typeof a[prop] === 'object') {
                if (!isObjectValueEqual(a[prop], b[prop])) return false
            } else if (a[prop] !== b[prop]) {
                return false
            }
        } else {
            return false
        }
    }
    return true
}

export function selfOr(self, another = null) {//返回自己或者另一个对象
    if (Array.isArray(self)) {
        return !isEmpty(self) ? self : [];
    } else {
        return !isEmpty(self) ? self : another;
    }
}

export function toStr(target) {//返回字符串
    return typeof target === 'object' ? JSON.stringify(target) : target;
}

export function isEmpty(obj) {
    if (obj === undefined || obj == null) {
        return true;
    }
    //判断是否是空对象
    /* if (Object.prototype.toString.call(obj) === '[Object Object]') {
         return Object.getOwnPropertyNames(obj).length > 0;
     }*/
    //判断是否空数组
    if (Array.isArray(obj) && obj.length === 0) {//数组
        return true;
    } else {
        if (typeof obj === 'string' && obj.trim() === '') {
            return true;
        }//字符串
    }
    return false;
}

export function isIos() {//判断平台为IOS
    return Platform.OS === 'ios';
}

export function isIphoneXSeries() {

    //return (isIos() && (Number(((height / width) + "").substr(0, 4)) * 100) === 216); //横屏切为竖屏后,长宽值对调
    return isIphoneX;
}

export function isIphone14ProMax(){
    // 250728增加iphone16promax，16promax宽高比例为21.7 NKGT-133
    return Platform.OS === 'ios' && ((D_HEIGHT === IPHONE14_MINI || D_HEIGHT === IPHONE14_MAX) || iphone16Promax)
}

export function isTraditionalScreen() {
    let {width, height} = Dimensions.get('window');
    return (height / width == 16 / 9);
}

export function getScreenWidth() {
    let {width, height} = Dimensions.get('window');
    let wd = width > height ? height : width;
    return wd;
}

export function getScreenHeight() {
    let {width, height} = Dimensions.get('window');
    let hg = width < height ? height : width;
    return hg;
}

//把Utc秒数时间戳转化为当地时间时间戳
export function convertUtcToLocalTimeStamp(utcTimeStamp) {
    let moment = require('moment');
    let localTimeStamp = utcTimeStamp + moment().utcOffset() * 60; //Utc时间戳补上时区偏移量
    if (localTimeStamp < 0) { //比如美国的-8时区设置23:12，因为涉及到减法，所以分钟数要向上取整，否则会少一分钟
        localTimeStamp = 86399 + localTimeStamp + 60; //因此要加60秒
    } else if (localTimeStamp > 86399) { //比如中国的+8时区 设置2点12
        localTimeStamp = localTimeStamp - 86399
    }
    return localTimeStamp;
}


//把Utc秒数时间戳转化为当地时间'HH:mm'的格式
export function convertUtcToLocalTime(utcTimeStamp) {
    let localTimeStamp = convertUtcToLocalTimeStamp(utcTimeStamp); //Utc时间戳补上时区偏移量

    return parseInt(localTimeStamp / 3600).toString().padStart(2, '0')
        + ":" + parseInt(localTimeStamp % 3600 / 60).toString().padStart(2, '0');
}


//把当地时间时间秒数转化为utc时间秒数
export function convertLocalToUtcTimeStamp(localTimeStamp) {
    let moment = require('moment');
    let utcTimeStamp = localTimeStamp - moment().utcOffset() * 60; //Utc时间戳补上时区偏移量
    if (utcTimeStamp < 0) {
        utcTimeStamp = 86399 + utcTimeStamp+60;
    } else if (utcTimeStamp > 86399) {
        utcTimeStamp = utcTimeStamp - 86399 - 60
    }
    return utcTimeStamp;
}


//把当地秒数时间戳转化为Utc时间'HH:mm'的格式
export function convertLocalToUtcTime(localTimeStamp) {
    let utcTimeStamp = convertLocalToUtcTimeStamp(localTimeStamp); //当地时间戳削去时区偏移量

    return parseInt(utcTimeStamp / 3600).toString().padStart(2, '0')
        + ":" + parseInt(utcTimeStamp % 3600 / 60).toString().padStart(2, '0');
}


export function convertToTimeString(localTimeStamp) {
    return parseInt(localTimeStamp / 3600).toString().padStart(2, '0')
        + ":" + parseInt(localTimeStamp % 3600 / 60).toString().padStart(2, '0');
}



export function isAndroid() {//判断平台为Android
    return Platform.OS === 'android';
}

export function objHasKey(obj) {//判断一个对象是否有属性
    if (isEmpty(obj)) {
        return false;
    }
    return Object.keys(obj).length > 0;
}

export function numFormat(numStr) {//若数字小于0，则拼接0
    return (Number(numStr) < 10 ? ('0' + numStr) : numStr);
}

export function oneOfKeys(obj, ...keys) {//从对象中轮次取一组key直到取到的value为非空
    let result = null;
    for (let keyStr of keys) {
        if (obj[keyStr]) {
            result = obj[keyStr];
            break;
        }
    }
    return result;
}

export function objToArray(obj) {//对象转数组
    return Object.keys(obj).map(function (key) {
        return obj[key];
    });
}

export function rnAddListeners(events) {//键盘多事件及回调
    let eventsFunc = [];
    Object.keys(events).forEach(key => {//添加事件监听
        eventsFunc.push(Keyboard.addListener(key, (event) => events[key](event)));
    });
    return eventsFunc;
}

export function rnAddListener(eventName, callback) {//事件及回调
    return DeviceEventEmitter.addListener(eventName, (event) => callback && callback(event));
}

export function rnCleanListener(...listeners) {//移除监听器
    return listeners && listeners.map(listener => listener && listener.remove());
}

export function isFunc(func) {//判断输入的是否是function
    return func && typeof func === 'function';
}


export function fileName(uri) {
    if (isEmpty(uri)) {
        return uri;
    }
    return uri.replace(/^(.+\/)(\w+\.\w+)$/, '$2');
}

export function netWorkException(code) {
    return code === -1;
}

/**
 * 创建一个标准返回参数的格式
 * @param code code
 * @param message msg
 * @returns {*|boolean}
 */
export function rnCallbackParams(code, message) {
    return {"code": code, "message": message};
}

/**
 * 格式化原生接口Promise.catch的error
 * @param error
 * @returns {{ code: string, message: string}}
 */
export function parseError(error) {
    // return {code: error.code, message: error.domain, userInfo: error.userInfo}
    return {code: error.userInfo.code, message: error.userInfo.message};
}

/**
 * 是否iPhoneX系列
 * @returns {boolean}
 */
export function isPhoneX() {
    return isIphoneX;
}


export default class Utils {
    static parseError = parseError;
    static parseCallbackError = rnCallbackParams;

}

export const setCustomText = () => {
    const TextRender = Text.render;

    let customStyle = {};
    // 重点，Fix Android 样式问题
    if (Platform.OS === "android") {
        customStyle = {
            fontFamily: ""
        };
    }

    Text.render = function render(props, ...rest) {
        const mergedProps = { ...props, style: [customStyle, props.style] };
        return TextRender.apply(this, [mergedProps, ...rest]);
    };
};
