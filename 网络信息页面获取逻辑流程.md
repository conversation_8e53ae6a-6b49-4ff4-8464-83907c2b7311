# 网络信息页面信息获取逻辑流程

## 概述
网络信息页面主要用于显示设备的网络连接状态和相关参数，包括WiFi信息、信号强度、IP地址、MAC地址等。项目中有两个版本的网络信息页面实现：
- `projects/com.chuangmi.camera.v2/src/setting/NetworkInfoPage.js` (v2版本)
- `projects/com.chuangmi.camera/src/setting/NetInfo.js` (通用版本)

## 主要流程图

```mermaid
graph TD
    A[页面加载 componentDidMount] --> B[初始化网络监听]
    B --> C[获取本地存储的网络模式]
    C --> D[调用getAllValue/getInfo获取网络信息]
    D --> E{设备是否在线?}
    
    E -->|在线| F[调用LetDevice.getSingleProperty获取实时数据]
    E -->|离线| G[调用云端缓存接口获取历史数据]
    
    F --> H[解析设备返回的网络数据]
    G --> I[解析云端缓存的网络数据]
    
    H --> J[计算信号强度等级]
    I --> J
    
    J --> K[更新页面状态显示]
    K --> L[渲染网络信息UI]
    
    M[NetInfo监听网络变化] --> N[更新当前网络状态]
    N --> O[判断连接模式 LAN/WAN]
```

## 详细流程说明

### 1. 页面初始化阶段

#### 1.1 组件挂载 (componentDidMount)
```javascript
componentDidMount() {
    // 1. 注册网络状态监听器
    this.unsubscribe = NetInfos.addEventListener(state => {
        this.setState({
            currentNetInfo: state.details,
        })
    })
    
    // 2. 获取网络信息
    this.getInfo() // 或 this.getAllValue()
    
    // 3. 获取本地存储的网络模式
    IMIStorage.load({
        key: LetDevice.deviceID+'liveNetMode',
        autoSync: true,
        syncInBackground: true,
    }).then(res => {
        this.setState({wifi_mode:res.liveNetMode})
    }).catch(_=> {
        this.setState({wifi_mode:LIVE_NETWORK_MODE.RELAY})
    });
}
```

### 2. 网络信息获取阶段

#### 2.1 设备在线时的数据获取
```javascript
// 使用实时属性获取
LetDevice.getSingleProperty('1').then(res => {
    this.setState({
        netInfo: res.value,
        loading: false
    })
})
```

#### 2.2 设备离线时的数据获取
```javascript
// 使用云端缓存接口
const params1 = {
    Path: '/v1.0/imilab-01/device/control/property/getByCached',
    ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 1,
        method: 'sync',
    },
    Method: 'POST',
};
LetIMIIotRequest.sendUserServerRequest(params1, true, true)
```

#### 2.3 v2版本的全量属性获取
```javascript
// 获取所有设备属性
LetDevice.updateAllPropertyCloud().then((data) => {
    let dataObject = JSON.parse(data);
    // 解析各种网络参数
})
```

### 3. 数据解析和处理阶段

#### 3.1 网络参数解析
| 参数名称 | 数据源字段 | 说明 |
|---------|-----------|------|
| WiFi名称 | WIFI_SSID.value / wifi_ssid | 当前连接的WiFi网络名称 |
| WiFi强度 | WiFI_SNR.value / wifi_signal_percent | 信号强度百分比 |
| RSSI值 | WiFI_RSSI.value / rssi | 接收信号强度指示 |
| IP地址 | WIFI_IP.value / ip_addr | 设备IP地址 |
| MAC地址 | MAC.value / mac_addr | 设备MAC地址 |
| 网络模式 | WIFI_Channel.value | 网络连接模式 |

#### 3.2 信号强度计算算法
```javascript
calculateSignalLevel(rssi, numLevels) {
    let MIN_RSSI = -100;  // 最小信号强度
    let MAX_RSSI = -55;   // 最大信号强度
    
    if (rssi <= MIN_RSSI) {
        return 0;  // 无信号
    } else if (rssi >= MAX_RSSI) {
        return numLevels - 1;  // 满信号
    } else {
        // 线性映射到信号等级
        let inputRange = (MAX_RSSI - MIN_RSSI);
        let outputRange = (numLevels - 1);
        let range = ((rssi - MIN_RSSI) * outputRange / inputRange);
        
        // 分档处理
        if(range >= 1 && range < 2) return 1;
        if(range >= 2 && range < 3) return 2;
        if(range >= 3 && range < 4) return 3;
        if(range >= 4 && range < 5) return 4;
        if(range >= 5 && range < 6) return 5;
    }
    return numLevels - 1;
}
```

#### 3.3 WiFi强度百分比计算 (v2版本)
```javascript
if (dataObject.WiFI_SNR) {
    if(dataObject.WiFI_SNR.value > 70){
        stateProps.wifi_strength = "100%";
    }else if(dataObject.WiFI_SNR.value < 0){
        stateProps.wifi_strength = "0%";
    }else {
        let value = (dataObject.WiFI_SNR.value / 70 * 100).toFixed(2);
        stateProps.wifi_strength = value + "%";
    }
}
```

### 4. 网络连接模式判断

#### 4.1 LAN/WAN连接判断逻辑
```javascript
// 判断是否为局域网连接
isLocalIP(deviceIP, phoneIP, deviceMask, phoneMask) {
    if (!deviceIP || !phoneIP || !deviceMask || !phoneMask) {
        return false;
    }
    
    const deviceNetwork = this.calculateNetworkAddress(deviceIP, deviceMask);
    const phoneNetwork = this.calculateNetworkAddress(phoneIP, phoneMask);
    
    return deviceNetwork === phoneNetwork;
}
```

### 5. UI渲染阶段

#### 5.1 信号强度图标显示
```javascript
// 信号强度图标数组
let WifiImageArray = [
    require('../../resources/images/ic_wifi_signal_0.png'),  // 无信号
    require('../../resources/images/ic_wifi_signal_2.png'),  // 弱信号
    require('../../resources/images/ic_wifi_signal_2.png'),
    require('../../resources/images/ic_wifi_signal_5.png'),  // 强信号
    require('../../resources/images/ic_wifi_signal_5.png'),
    require('../../resources/images/ic_wifi_signal_5.png')
];

// 根据信号等级显示对应图标
<ImageButton 
    style={{width: 70, height: 70}} 
    source={WifiImageArray[LetDevice.isOnline && !error ? wifi_signal_level : 0]}
/>
```

#### 5.2 网络状态文字描述
```javascript
let WifiSignalArray = [
    stringsTo('wifi_signal_0'),  // "新WiFi无信号"
    stringsTo('wifi_signal_2'),  // "设备网络状态较差"
    stringsTo('wifi_signal_2'),
    stringsTo('wifi_signal_5'),  // "设备网络状态良好"
    stringsTo('wifi_signal_5'),
    stringsTo('wifi_signal_5'),
];
```

## 关键技术点

### 1. 网络状态监听
- 使用 `@react-native-community/netinfo` 监听手机网络状态变化
- 实时更新当前网络信息用于LAN/WAN判断

### 2. 设备状态适配
- 在线设备：使用实时属性获取最新数据
- 离线设备：使用云端缓存获取历史数据

### 3. 数据容错处理
- 网络请求超时处理
- 数据解析异常处理
- 默认值设置

### 4. 性能优化
- 组件卸载时清理监听器
- 使用loading状态提升用户体验
- 缓存网络模式到本地存储

## 常量定义

### 网络模式常量
```javascript
export const LIVE_NETWORK_MODE = {
    RELAY: "RELAY",    // 中继模式
    LOCAL: "LOCAL",    // 本地模式  
    SRFLX: "SRFLX"     // SRFLX模式
}
```

### 信号强度阈值
- MIN_RSSI: -100 (最小信号强度)
- MAX_RSSI: -55 (最大信号强度)
- SNR阈值: 70 (用于百分比计算)

## 错误处理机制

1. **网络请求失败**: 显示加载失败提示，设置错误状态
2. **数据解析异常**: 使用默认值，避免页面崩溃
3. **超时处理**: 设置15秒超时，防止长时间等待
4. **设备离线**: 自动切换到缓存数据获取模式

这个流程确保了网络信息页面能够在各种网络环境和设备状态下稳定运行，为用户提供准确的网络状态信息。
