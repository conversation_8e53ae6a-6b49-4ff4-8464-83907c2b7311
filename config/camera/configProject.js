/*
 * 作者：sunhongda
 * 文件：configProject.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import {Category, RN_ROUTE_PAGE_TAG} from "../configConstant";
import {stringsTo} from "../../globalization/Localize";
import {CameraMethod} from "../../imilab-rn-sdk/components/camera/CameraMethod";

import {
    LetDevice,
    IMIGotoPage,
} from 'imilab-rn-sdk';

/**
 * 摄像机普通插件常用配置项
 * @type {{}}
 */
export const configCameraProjectDefault = {
    "category": Category.Camera,
    "storage": {
        "isSkipTestCameraSpeed": false
    },
    "packagePath": "",
    "minSdApiLevel": 1,
    "versionCode": 1,
    "notSupportMethod": {
        "properties": [
            [CameraMethod.IMI_ImageFlipState],
            [CameraMethod.IMI_AlarmSwitch],
        ],
        "events": [
            "ReportLog"
        ],
        "services": [
            "StreamType"
        ]
    },
    "fun": {},
    "components": {}

};


const IPC031_settingParams =
    {
        showDelDev: true,
        defaultStyleRenderItemArray: [
            {
                title: [stringsTo('popo_setting_camera_text')],
                dependentOnLine:true, //是否是在线才能用、离线禁止时候用的功能
                onPress: () => {
                    navigation.push("CameraSettingPage");
                }
            },
            {
                title: [stringsTo('alarmSettingText')],
                dependentOnLine:true, //是否是在线才能用、离线禁止时候用的功能
                onPress: () => {
                    navigation.push("HomeKeepSetting");
                }
            }, {
                title: [stringsTo('playBackText')],
                onPress: () => {
                    navigation.push('PlayBackVideoPage',);
                }
            }, {
                title: [stringsTo('cameraTestTipsTitle2')],
                dependentOnLine:true, //是否是在线才能用、离线禁止时候用的功能
                onPress: () => {
                    navigation.navigate('SignalTestPage',{pageOnSettings:true});
                }
            },
           /* {
                title: [stringsTo('help_callback')],
                //dependentOnLine:true, //是否是在线才能用、离线禁止时候用的功能
                onPress: () => {
                    //IMIGotoPage.starNativeFeedbackPage(LetDevice.deviceID);
                   IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/CommonHelp");
                  // IMIGotoPage.startOtaPage(LetDevice.deviceID);
                }
            },*/
        ]
    }
;

const Camera_settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showDayNightMode: true,//夜视模式
        showImageFlipState: true,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度
        showSoundLightAlarm: true,//声光报警
        showOneKeyAlarm: true,//一键警告
        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        showTrackSwitch: false,//人形追踪
        showSleep:false,//休眠功能
        showCorrectPos:false,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:false,//是否使用异响侦测
    }
;

const Camera_038settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: false,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:true,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showAllPanoramara:true,//是否显示全景绘图
        showPresetSleep:true,//固定休眠位置
        versionCode: 17,//长按首页标题出现的版本号
        hideDetect: true,//038看家助手只显示人形追踪，人形、移动、异响开关都不显示
        showRNAlbum:true,
        //以前038a01标清使用的是sd_360p-->quality_360
        showCustomQuality:[{title:stringsTo('quality_360'),index:1,accessibilityLabel:"home_page_clarity_show_360"},
            {title:stringsTo('quality_25k_v2'),index:2,accessibilityLabel:"home_page_clarity_show_2.5"}],
        languageConfig:["zh","en"],
        cloudDownload:true,//是否显示云存下载图标
    }
;
const Camera_056settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: false,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度
        versionCode: 38,
        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showPanoramicView:false,//是否不显示云台滑杆 显示中间显示通话
        showFencesSwitch:false,//围栏侦测
        showPresetSleep:true,//固定休眠位置
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showSynchronous056:true,//是否显示同步056
        showAllPanoramara:true,//是否显示全景绘图
        showPlayBackGridWithAlarmEvent:true,//回看根据看家事件宫格展示
        playBackWithLast:true,//回看跳到最后播放
        showPlaybackEvent:false,//是否显示回看视频的事件并用色块区分显示
        sdDataReverseVersion:'056101_2.2.0_0125',//SD卡数据倒序返回
        showCustomQuality:[{title:stringsTo('sd_360p'),index:1,accessibilityLabel:"home_page_clarity_show_360"},
            {title:stringsTo('hd_1080p'),index:2,accessibilityLabel:"home_page_clarity_show_2.5"}],
        playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持
    };
const Camera_059settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: false,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        cloudDownload:true,//云存下载功能
        showSleep:true,//休眠
        showCorrectPos:false,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showPanoramicView:true,//是否不显示云台滑杆 显示中间显示通话
        showFencesSwitch:true,//围栏侦测(也影响到看家的围栏事件)
        showPresetSleep:false,//固定休眠位置
        showSynchronous056:true,//是否显示同步056
        showAllPanoramara:false,//是否显示全景绘图
        showCustomQuality:[{title:stringsTo('quality_360'),index:1,accessibilityLabel:"home_page_clarity_show_360"},
            {title:stringsTo('quality_1080'),index:2,accessibilityLabel:"home_page_clarity_show_1080"}],
    };
const Camera_062settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showKeyAreaEvent:true, //重点区域侦测事件
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: true,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度
        cloudDownload:true,//云存下载功能
        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        versionCode: 23,
        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showPanoramicView:false,//是否不显示云台滑杆 显示中间显示通话
        showFencesSwitch:false,//围栏侦测(也影响到看家的围栏事件)
        showPresetSleep:false,//固定休眠位置
        showSynchronous056:true,//是否显示同步056
        showSDError:true, //插件主页提示SD卡异常
        showAllPanoramara:false,//是否显示全景绘图
        showRNAlbum:true,//是否使用RN相册
        hideStatusLight:true,//是否隐藏状态灯
        hidePictureCorrection:true,//是否畸变纠正
        showSoundBroadcast:false,
        showCustomQuality:[{title:stringsTo('resolution_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo('resolution_qhd'),index:2,accessibilityLabel:"home_page_clarity_show_qhd"}],
        showPlaybackEvent:true,//是否显示回看视频的事件并用色块区分显示
        showLiveToolbarBottomTitle:true, //IPC062直播页视频工具栏icon下添加文案的特殊需求，其他项目请无视
        showTopRightSettingTitle:true,   //IPC062客户的特殊需求给导航栏设置按钮顶部加上"设置"二字
        playBackWithLast:true,//回看跳到最后播放
        returnReversePlaybackList:true,//固件端返回按时间逆序的回看视频
        playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持
    };
const Camera_029settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: false,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showCruise:true,//是否显示智能巡航
        showNoHuman:true,//是否无人侦测
        showPresetSleep:true,
        showSoundBroadcast:true,//支持智能语音播报
        showAllPanoramara:true,//是否显示全景绘图
        versionCode: 20,
        showRNAlbum:true,
        showCustomQuality:[{title:stringsTo('sd_360p'),index:1,accessibilityLabel:"home_page_clarity_show_360"},
            {title:stringsTo('quality_1440p'),index:2,accessibilityLabel:"home_page_clarity_show_2.5"}],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 029项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通过一端没有声音
        audioParams: 3
    };
const Camera_052settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showDayNightMode: true,//夜视模式
        showImageFlipState: true,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度
        showSoundLightAlarm: true,//声光报警
        showOneKeyAlarm: true,//一键警告
        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:false,//异响事件
        showTrackSwitch: false,//人形追踪
        showSleep:false,//休眠功能
        showCorrectPos:false,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:false,//是否使用异响侦测
        showRNAlbum:true,
        cloudDownload:true,//云存下载功能
        versionCode: 16,
        showPanoramicView:true,//是否不显示云台滑杆 显示中间显示通话
        showCallAndAlarmView:true,//显示两个图标 通话和报警
        showCustomQuality:[{title:stringsTo('quality_sd_new'),index:1},
            {title:stringsTo('quality_uhd'),index:2}],
        isPOE: true,
    }
;

const Camera_026settingParams =
    {
        showPeopleSwitch: false,//人形侦测
        showMoveSwitch: true,//移动侦测
        showCrySwitch:true,//哭声侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: false,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: true,//图片翻转
        showMotionDetectSensitivity: false,//灵敏度

        showPeopleEvent: false,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:false,//异响事件
        showCryEvent:true,//哭声事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:false,//是否使用异响侦测
        showPanoramicView:false,//是否不显示云台滑杆 显示中间显示通话
        showFencesSwitch:false,//围栏侦测(也影响到看家的围栏事件)
        showPresetSleep:false,//固定休眠位置
        showAllPanoramara:false,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showMoveTracking:true,//026 移动追踪
        showRNAlbum:true,
        cloudDownload:true,//云存下载功能
        lensCorrect:[468.0 / 1920.0, 46.0 / 1088.0], // 畸变矫正参数配置
        versionCode: 5,
        showCustomQuality:[{title:stringsTo('quality_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo('quality_fhd'),index:2,accessibilityLabel:"home_page_clarity_show_fhd"}],
    };
const Camera_021settingParams =
    {
        versionCode: 5,
        isCustomPlugin: true, // 是否是自定义插件
    };
const Camera_036settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: true,//图片翻转
        showMotionDetectSensitivity: false,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:false,//是否无人侦测
        showPresetSleep:false,//固定位置休眠
        showSoundBroadcast:false,//支持智能语音播报
        showAllPanoramara:false,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        versionCode: 11,
        showCustomQuality:[{title:stringsTo('quality_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo('quality_fhd'),index:2,accessibilityLabel:"home_page_clarity_show_fhd"}],
        audioParams: 2
    };
const Camera_060settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        showAreaEvent:true,//重点区域事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:true,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:true,//是否无人侦测
        showPresetSleep:true,
        showSoundBroadcast:true,//支持智能语音播报
        showAllPanoramara:true,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showArea:false,//重点区域功能
        showAngle:true,//是否有常用角度功能
        showPTZ:true,//是否显示云台
        supportLensCorrect:true,//固件是否支持畸变纠正
        showMoveRoom:true,//是否显示移动房间功能
        showImageFlip:true,//是否显示画面翻转
        sdDataReverseVersion:'060101_2.3.2_0159',//SD卡数据倒序返回,其他项目暂时是正序
        versionCode: 99,
        showCustomQuality:[{title:stringsTo('quality_common_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd",width:864,height:480},
            {title:stringsTo('quality_common_3K'),index:2,accessibilityLabel:"home_page_clarity_show_3k",width:2880,height:1620}],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 060也是aac 16k
        // 项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通话一端没有声音
        audioParams: 3,
        returnReversePlaybackList:true,//固件端返回按时间逆序的回看视频
        playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持

        cameraNumber:'1',
        videoCodec: '1'
    };

const Camera_113settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: false,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        showAreaEvent:true,//重点区域事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:true,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:true,//是否无人侦测
        showPresetSleep:true,
        showSoundBroadcast:true,//支持智能语音播报
        showAllPanoramara:false,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showArea:false,//重点区域功能
        showAngle:true,//是否有常用角度功能
        showPTZ:true,//是否显示云台
        supportLensCorrect:true,//固件是否支持畸变纠正
        showMoveRoom:true,//是否显示移动房间功能
        showImageFlip:true,//是否显示画面翻转
        sdDataReverseVersion:'060101_2.3.2_0159',//SD卡数据倒序返回,其他项目暂时是正序
        versionCode: 13,
        showCustomQuality:[{title:stringsTo('quality_auto'),index:0,modeIndex:0,accessibilityLabel:"home_page_clarity_show_auto",width:2304,height:1296},
            {title:stringsTo('quality_360'),index:1,modeIndex:1,accessibilityLabel:"home_page_clarity_show_sd",width:640,height:360},
            {title:stringsTo('quality_2k'),index:2,modeIndex:5,accessibilityLabel:"home_page_clarity_show_2k",width:2304,height:1296}],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 060也是aac 16k
        // 项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通话一端没有声音
        audioParams: 3,
        returnReversePlaybackList:true,//固件端返回按时间逆序的回看视频
        playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持

        cameraNumber:'1',
        videoCodec: '1',
        clarityIndex:5,
    };

const Camera_113settingParams_outdoor =
    {
        showSoundLightAlarm: true,//声光报警
        isOutdoor: true,//是否室外机
        versionCode: 19,
        lightEffectArray: [2],
        showAlarmLoudSwitch:false,//异响事件 大声侦测
 videoCodec: '1'
    }

const Camera_113settingParams_indoor =
    {
        showSoundLightAlarm: false,//声光报警
        isOutdoor: false,//是否室外机
        hideStatusLight: true,//隐藏状态灯开关
        versionCode: 14,
    }

const Camera_113DualSettingParams_indoor =
    {
            showSoundLightAlarm: false,//声光报警
            isOutdoor: false,//是否室外机
            showImageFlip: true,//图片翻转
            hideStatusLight: true,//隐藏状态灯开关
            versionCode: 13,
    }

const Camera_117settingParams_indoor =
  {
    showSoundLightAlarm: false,//声光报警
    isOutdoor: false,//是否室外机
      versionCode: 11,
    videoCodec: '0', //H264
  }

const Camera_113DualSettingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: true,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:true,//异响事件
        showAreaEvent:true,//重点区域事件

        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:true,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:true,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:true,//是否无人侦测
        showPresetSleep:true,
        showSoundBroadcast:true,//支持智能语音播报
        showAllPanoramara:false,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showArea:false,//重点区域功能
        showAngle:true,//是否有常用角度功能
        showPTZ:true,//是否显示云台
        supportLensCorrect:true,//固件是否支持畸变纠正
        showMoveRoom:true,//是否显示移动房间功能
        sdDataReverseVersion:'060101_2.3.2_0159',//SD卡数据倒序返回,其他项目暂时是正序
        versionCode: 12,
        showCustomQuality: [
            {
                title: stringsTo('quality_auto'),
                index: 0,
                modeIndex: 0,
                accessibilityLabel: 'home_page_clarity_show_auto',
            },
            {
                title: stringsTo('quality_360'),
                index: 1,
                modeIndex: 1,
                accessibilityLabel: "home_page_clarity_show_sd",
                width: 640,
                height: 360
            },
            {
                title: stringsTo('quality_2k'),
                index: 2,
                modeIndex: 5,
                accessibilityLabel: "home_page_clarity_show_3k",
                width: 2880,
                height: 1620
            }],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 060也是aac 16k
        // 项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通话一端没有声音
        audioParams: 3,
        returnReversePlaybackList:true,//固件端返回按时间逆序的回看视频
        playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持

        cameraNumber:'2',
        videoCodec: '1'
    };

const Camera_113DualSettingParams_outdoor =
    {
        showSoundLightAlarm: true,//声光报警
        isOutdoor: true,//是否室外机
        lightEffectArray: [2],
    }

const Camera_068settingParams =
    {
            showPeopleSwitch: true,//人形侦测
            showMoveSwitch: true,//移动侦测
            showSoundLightAlarm: true,//声光报警
            showOneKeyAlarm: false,//一键警告
            showTrackSwitch: true,//人形追踪
            showDayNightMode: false,//夜视模式
            isOutdoor: true,//是否室外机

            showImageFlipState: false,//图片翻转
            showMotionDetectSensitivity: true,//灵敏度

            showPeopleEvent: true,//人形
            showMoveEvent:true,//移动
            showAlarmLoudSwitch:false,//异响事件 大声侦测
            showAreaEvent:true,//重点区域事件

            showSleep:true,//休眠
            showCorrectPos:true,//云台校准
            showLightFullColor:true,//微光全彩
            showHideSelect:true,//是否编辑时隐藏
            showUseCloudEvent:true,//是否使用云存储筛选事件
            showLoudSwitch:true,//是否使用异响侦测
            showCruise:false,//是否显示智能巡航
            showNoHuman:true,//是否无人侦测
            showPresetSleep:true,
            showSoundBroadcast:true,//支持智能语音播报
            showAllPanoramara:false,//是否显示全景绘图
            showSDError:true,//显示SD卡异常
            showRNAlbum:true,//是否使用RN相册
            cloudDownload:true,//云存下载功能
            showArea:false,//重点区域功能
            showAngle:true,//是否有常用角度功能
            showPTZ:true,//是否显示云台
            supportLensCorrect:true,//固件是否支持畸变纠正
            showMoveRoom:true,//是否显示移动房间功能
            showImageFlip:false,//是否显示画面翻转
            sdDataReverseVersion:'060101_2.3.2_0159',//SD卡数据倒序返回,其他项目暂时是正序
            versionCode: 21,
            showCustomQuality:[
                {
                    title: stringsTo('quality_auto'),
                    index: 0,
                    modeIndex: 0,
                    accessibilityLabel: 'home_page_clarity_show_auto',
                },
                {
                    title: stringsTo('quality_low'),
                    index: 1,
                    modeIndex: 2,
                    accessibilityLabel: 'home_page_clarity_show_low',
                },
                {
                    title: stringsTo('quality_common_3K'),
                    index: 2,
                    modeIndex: 7,
                    accessibilityLabel: 'home_page_clarity_show_fhd',
                },
            ],
            //配置直播流相关参数，
            // MONO_16K_G711A: 1,
            // MONO_8K_G711A: 2,
            // MONO_16K_AAC_LC: 3,
            // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
            // 060也是aac 16k
            // 项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通话一端没有声音
            audioParams: 3,
            returnReversePlaybackList:true,//固件端返回按时间逆序的回看视频
            playBackPlayFinishCallback:true,//回看支持播放完成回调onPlayCompletion,需固件端支持
            showMotorVehicle:true,//机动车侦测
            showNonMotorVehicle:true,//非机动车侦测
            lightEffectArray: [2],
            cameraNumber:'2',
            videoCodec: '0'

    };

const Camera_065settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: true,//声光报警
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: true,//人形追踪
        showDayNightMode: false,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:false,//异响事件
        showAreaEvent:false,//重点区域事件
        showVehicleEvent:true,//机动车事件
        showBikeEvent:true,//非机动车事件


        showSleep:true,//休眠
        showCorrectPos:true,//云台校准
        showLightFullColor:true,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:false,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:false,//是否无人侦测
        showPresetSleep:true,
        showSoundBroadcast:false,//支持智能语音播报
        showAllPanoramara:true,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showArea:false,//重点区域功能
        showAngle:true,//是否有常用角度功能
        showPTZ:true,//是否显示云台
        showStreamingSpotlight:true,//直播一键照明
        showStreamingAlarm:true,//直播一键报警
        showMotorVehicle:true,//机动车侦测
        showNonMotorVehicle:true,//非机动车侦测
        supportLensCorrect:true,//固件是否支持畸变纠正
        showMoveRoom:true,//是否显示移动房间功能
        showImageFlip:false,//是否显示画面翻转
        sdDataReverseVersion:'065101_2.3.2_0166',//SD卡数据倒序返回,其他项目暂时是正序
        versionCode: 1,
        showCustomQuality:[{title:stringsTo('quality_common_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd",width:864,height:480},
            {title:stringsTo('quality_common_3K'),index:2,accessibilityLabel:"home_page_clarity_show_3k",width:2880,height:1620}],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 060也是aac 16k
        // 项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通话一端没有声音
        audioParams: 3
    };

const Camera_040_A03_A04settingParams =
    {
        showPeopleSwitch: true,//人形侦测
        showMoveSwitch: true,//移动侦测
        showSoundLightAlarm: true,//声光报警,包含声光、智能语音播报
        showOneKeyAlarm: false,//一键警告
        showTrackSwitch: false,//人形追踪
        showDayNightMode: true,//夜视模式

        showImageFlipState: false,//图片翻转
        showMotionDetectSensitivity: true,//灵敏度

        showPeopleEvent: true,//人形
        showMoveEvent:true,//移动
        showAlarmLoudSwitch:false,//异响事件
        showAreaEvent:true,//重点区域事件

        showSleep:true,//休眠
        showCorrectPos:false,//云台校准
        showLightFullColor:false,//微光全彩
        showHideSelect:true,//是否编辑时隐藏
        showUseCloudEvent:true,//是否使用云存储筛选事件
        showLoudSwitch:false,//是否使用异响侦测
        showCruise:false,//是否显示智能巡航
        showNoHuman:false,//是否无人侦测
        showFencesSwitch:true,//围栏侦测(也影响到看家的围栏事件)
        showPresetSleep:false,//固定角度休眠
        showSoundBroadcast:false,//支持智能语音播报
        showAllPanoramara:false,//是否显示全景绘图
        showSDError:true,//显示SD卡异常
        showRNAlbum:true,//是否使用RN相册
        cloudDownload:true,//云存下载功能
        showArea:true,//重点区域功能
        showAngle:false,//是否有常用角度功能
        showPTZ:false,//是否不显示云台滑杆 显示中间显示通话
        showAlarm:true,//是否首页有报警功能
        supportLensCorrect:false,//固件是否支持畸变纠正
        productNotSupportLensCorrect: true,//产品不支持畸变纠正，其他项目默认false支持，默认支持，040A03去掉畸变纠正
        showInstallNotice:true,//是否展示安装需知
        showMoveRoom:false,//是否显示移动房间功能
        useNewSDPlan:true,//使用新T卡方案，即T卡插件设备后，必须先格式化，才能正常录制视频
        versionCode: 10,
        showCustomQuality:[{title:stringsTo('quality_common_sd'),index:1,accessibilityLabel:"home_page_clarity_show_sd",width:864,height:480},
            {title:stringsTo('quality_2k'),index:2,accessibilityLabel:"home_page_clarity_show_2k",width:2304,height:1296}],
        //配置直播流相关参数，
        // MONO_16K_G711A: 1,
        // MONO_8K_G711A: 2,
        // MONO_16K_AAC_LC: 3,
        // MONO_8K_AAC_LC: 4, 其他项目可以根据项目需要自己配置，默认不配置audioParams
        // 029项目APP端要求配置此参数，原因是有时候取缓存音频格式APP端取不到，导致通过一端没有声音
        audioParams: 3
    };

export const configProject = {
    // IPC026摄像头 a1FKrifIRwH
    "a1FKrifIRwH": {
        ...configCameraProjectDefault,...Camera_026settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepOldNativeSetting"},
            [RN_ROUTE_PAGE_TAG.msgTrans]: {indexName: "MsgTrans"},
            [RN_ROUTE_PAGE_TAG.playback]: {indexName: "PlayBackPage"},
        }
    },
    // IPC021摄像头
    "a1Ikkj5vsiK": {
        ...configCameraProjectDefault,...Camera_021settingParams, "components": {
            //自定义插件，智能巡航
            [RN_ROUTE_PAGE_TAG.customCruise]: {indexName: "IntelligentCruise"}
        }
    },

    //IPC036摄像头
    "a1znn6t1et8": {
        ...configCameraProjectDefault,...Camera_036settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepOldNativeSetting"}
        }
    },

    //IPC031摄像头
    "a1MZkl613tF": {
        ...configCameraProjectDefault, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "IPC031MainPageNew"}
        }
    },

    //IPC041摄像头
    "a11ne46UCon": {
        ...configCameraProjectDefault, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "IPC031MainPageNew"}
        }
    },


    //510智能门
    "a1NWkuD9PVb": {
        ...configCameraProjectDefault, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainLivePage"},
            [RN_ROUTE_PAGE_TAG.bellcall]: {indexName: "VoiceCallPage"}
        }
    },
    //网关智能门
    "a1l4Z7lJ1ns": {
        ...configCameraProjectDefault, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainLivePage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmPage"},
            [RN_ROUTE_PAGE_TAG.bellcall]: {indexName: "VoiceCallPage"}
        }
    },

    //网关智能门
    "a1yMb5JVWDa": {
        ...configCameraProjectDefault, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainLivePage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmPage"},
            [RN_ROUTE_PAGE_TAG.bellcall]: {indexName: "VoiceCallPage"}
        }
    },

    //IPC038a01摄像头
    "a1kWMNz9FH1": {
        ...configCameraProjectDefault,...Camera_038settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC052摄像头
    "a1Od0SjKPGt": {
        ...configCameraProjectDefault,...Camera_052settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC056摄像头
    "a1Godgpvr3D": {
        ...configCameraProjectDefault,...Camera_056settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC029c01->a1QRbHvcYBd 021e01->a1Ikkj5vsiK
    "a1QRbHvcYBd":{
        ...configCameraProjectDefault,...Camera_029settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC059摄像头
    "a1zcQKoHQ83": {
        ...configCameraProjectDefault,...Camera_059settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC060A01
    "imilab.ipc.060":{
        //Camera_040_A03_A04settingParams Camera_060settingParams
        ...configCameraProjectDefault,...Camera_060settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
            // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC060B01
    "a15iIr2TPeG":{
        ...configCameraProjectDefault,...Camera_060settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
            // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //IPC062摄像头
    "a1MSKK9lmbs": {
        ...configCameraProjectDefault,...Camera_062settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "IMICommSettingPage",
                routeParams: IPC031_settingParams,
            },
            //CameraPlayerPage
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "CameraPlayerPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    "a1j4NMOCQ85":{
        ...configCameraProjectDefault,...Camera_065settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
            // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
    //040-A03~A05
    "a1XvULslRkB":{
        ...configCameraProjectDefault,...Camera_040_A03_A04settingParams, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
                indexName: "CommonSettingPage",
                routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
            // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
        }
    },
        //117室内单摄
        "imilab.ipc.117aae1":{
          ...configCameraProjectDefault,...Camera_113settingParams,...Camera_117settingParams_indoor, "components": {
            [RN_ROUTE_PAGE_TAG.setting]: {
              indexName: "CommonSettingPage",
              routeParams: IPC031_settingParams,
            },
            [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
            [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
            // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
            [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
            [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
          }
        },
        //113室内单摄
        "imilab.ipc.113amc1":{
                ...configCameraProjectDefault,...Camera_113settingParams,...Camera_113settingParams_indoor, "components": {
                        [RN_ROUTE_PAGE_TAG.setting]: {
                                indexName: "CommonSettingPage",
                                routeParams: IPC031_settingParams,
                        },
                        [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
                        [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
                        // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
                        [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
                        [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
                }
        },
        //113室外单摄
        "imilab.ipc.113omc1":{
                ...configCameraProjectDefault,...Camera_113settingParams,...Camera_113settingParams_outdoor, "components": {
                        [RN_ROUTE_PAGE_TAG.setting]: {
                                indexName: "CommonSettingPage",
                                routeParams: IPC031_settingParams,
                        },
                        [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
                        [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
                        // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
                        [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
                        [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
                }
        },


        //113室外双摄
        "imilab.ipc.113pmc1": {
                ...configCameraProjectDefault, ...Camera_113DualSettingParams, ...Camera_113DualSettingParams_outdoor, "components": {
                        [RN_ROUTE_PAGE_TAG.setting]: {
                                indexName: "CommonSettingPage",
                                routeParams: IPC031_settingParams,
                        },
                        [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
                        [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
                        // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
                        [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
                        [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
                }
        },

        //113室内双摄
        "imilab.ipc.113bmc1": {
                ...configCameraProjectDefault, ...Camera_113DualSettingParams,...Camera_113DualSettingParams_indoor,"components": {
                        [RN_ROUTE_PAGE_TAG.setting]: {
                                indexName: "CommonSettingPage",
                                routeParams: IPC031_settingParams,
                        },
                        [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
                        [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
                        // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
                        [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
                        [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
                }
        },

        //068室外双摄
        "imilab.ipc.068":{
                ...configCameraProjectDefault,...Camera_068settingParams, "components": {
                        [RN_ROUTE_PAGE_TAG.setting]: {
                                indexName: "CommonSettingPage",
                                routeParams: IPC031_settingParams,
                        },
                        [RN_ROUTE_PAGE_TAG.launcher]: {indexName: "MainPage"},
                        [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "MainPage"},
                        // [RN_ROUTE_PAGE_TAG.alarm]: {indexName: "AlarmListPage"},
                        [RN_ROUTE_PAGE_TAG.storageServices]: {indexName: "StorageServicesPage"},
                        [RN_ROUTE_PAGE_TAG.alarmSetting]: {indexName: "HouseKeepSetting"}
                }
        },



};

