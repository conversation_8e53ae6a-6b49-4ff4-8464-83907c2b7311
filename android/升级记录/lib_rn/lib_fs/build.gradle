apply plugin: 'com.android.library'
apply from: rootProject.file('rn_maven_push.gradle')
def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 26)
    buildToolsVersion safeExtGet('buildToolsVersion', '26.0.3')

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 19)
        targetSdkVersion safeExtGet('targetSdkVersion', 26)
        versionCode 1
        versionName "1.0"
    }
    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation "com.facebook.react:react-android:0.74.3"
}
