import React, {Component} from 'react';
import {
    Image,
    View,
    Dimensions,
    TouchableWithoutFeedback,
    ImageBackground,
    TouchableOpacity,
    Text,
    StyleSheet, DeviceEventEmitter
} from 'react-native';

const {width} = Dimensions.get('window');
let SCREEN_WIDTH = width;
let cur_direction = -1;//当前云台转动方位
let ptz_pressed = false; //当前是否在操作云台
let ptz_pressedPanoramic = false; //当前是否在操作全景云台

import {colors, RNLine, showLoading, showToast,RoundedButtonView} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import HomePageLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import ImageButton from "../../../../imi-rn-commonView/ImageButton/ImageButton"
import WaveView from '../../../../imilab-design-ui/src/widgets/WaveView'
import {
    LetDevice,
    IMIGotoPage,
    BaseDeviceComponent,
    imiAlarmEventCloudApi, aliAlarmEventCloudApi,
} from 'imilab-rn-sdk';
import JoystickControlView from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView";
import Toast from "react-native-root-toast";
import PanoramicView from "./PanoramicView";
import AlarmListPage from "../alarmList/AlarmListPage";
import { XImage,XText,XView} from 'react-native-easy-app';
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import {GotoPageInNative, IMIStorage, LetIProperties} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";

const EventName = "EventName-PanoramaCompoundFinishE";
const lensCorrect_x = 880.0 / 2560.0;
const lensCorrect_y = 64.0 / 1440.0;
let audioNeedRecovery = false; //通话结束是否需要恢复监听状态
let isCheckingPermission = false;

export default class CameraPlayerPage extends BaseDeviceComponent {
    constructor(props, context) {
        super(props, context);
        this.state = {
            currentStatus:HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isSleep: false,//设备是否休眠
            spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
            spotlightBrightness: 0,
            isPanoramic:false,
            sdCardStatus:0,
            isDataUsage:false,
            vipState:-1,
            panoramicType:3,
            overAllImgStoreUrl:null,
            leftValue:1,
            rightValue:90,
            lensCorrect:false,
            isMoveStatus:false,//全屏长按触摸判断
            step:0,
            isOnline:LetDevice.isOnline,
            stateType:undefined,
        }
        this.isForegroundPage = true;
    }

    componentDidMount() {
        // this.IMIVideoView.prepare();
        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            this.isForegroundPage = true;
            if (this.state.isOnline){
                this.getNetWork();
            }
            this.getSameData();
            this.getSDCardStatus();
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();
            this.isForegroundPage = false;
            this.IMIVideoView && this.IMIVideoView.stop();
        });

        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {

            this._dealWithErrorOrPause();
        });

        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(()=>{
            if (!this.isForegroundPage)return;
            this.getSameData();
            if (this.state.isOnline){
                this.getNetWork();
            }
        });
        // Subscribe
        this.unsubscribe = NetInfo.addEventListener(state => {
            if (this.state.stateType !== state.type){
                this.setState({stateType:state.type});
                this.getNetWork();
            }

            console.log("Is connected?", state.isConnected);
        });


        this.getPanoramaProperty();

        LetDevice.registerDeviceEventChangeListener((data)=>{
            console.log('registerDeviceEventChangeListener-----',data);
            let {iotId,identifier,value} = JSON.parse(data);
            if (iotId == LetDevice.deviceID){
                if (identifier=='PanoramaCompoundFinishE'){
                    let {isSuccess} = value;
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    DeviceEventEmitter.emit('PanoramaCompoundFinishE', isSuccess);
                    if (parseInt(isSuccess)){
                        this.getPanoramaProperty();
                        showToast(stringsTo('panoramicSuccess'));
                        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                            showLoading(false);
                            this.setState({panoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
                        }).catch(error=>{
                            showLoading(false);
                        });
                    }else {
                        showToast(stringsTo('panoramicError'));
                        this.setState({panoramicType:3});
                    }
                }
                // if (identifier == 'PTZUpdateCoordinates'){
                //     let {coordinates} = value;
                //     if (coordinates.substr(0,3)=='-1,'){
                //         showToast(stringsTo('direction_end_009'));
                //     }
                //     // alert(coordinates);
                //
                // }
            }
        });
        this.devicePropertyListener = LetIProperties.addPropertyChangeListener((event)=>{
            console.log('event-----------------',event);
            let data = typeof(event)=='object'?event:JSON.parse(event);
            if (data.SleepStatus!=undefined){
                if (data.SleepStatus == "0"|| data.SleepStatus==0){
                    this.setState({isSleep:true});
                    this.IMIVideoView&&this.IMIVideoView.stop();
                }else {
                    this.setState({isSleep:false});
                    this.IMIVideoView&&this.IMIVideoView.prepare();
                }
            }
            if (data.iotId == LetDevice.deviceID){
                if (data.items){
                    if (data.items.SleepStatus){
                        if (data.items.SleepStatus.value == "0"|| data.items.SleepStatus.value==0){
                            this.setState({isSleep:true});
                            this.IMIVideoView&&this.IMIVideoView.stop();
                        }else {
                            this.setState({isSleep:false});
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                        }
                    }
                }
            }

        });
        this.deviceInfoListener = LetDevice.addInfoChangeListener((info)=>{
            let data = typeof(info)=='object'?info:JSON.parse(info);
            console.log('info----------'+JSON.stringify(data));
            if (data.key == 'isOnline'){
                this.setState({isOnline:data.value});
                if (data.value == false){
                    this.IMIVideoView&&this.IMIVideoView.stop();
                }else {
                    this.IMIVideoView&&this.IMIVideoView.prepare();
                }
            }
            if (data.iotId == LetDevice.deviceID) {
                if (data.thingType == 'DEVICE'){
                    if (data.status){
                        this.setState({isOnline:data.status.value==1?true:false});
                        if (data.status.value == false){
                            this.IMIVideoView&&this.IMIVideoView.stop();
                        }else {
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                        }
                    }
                }
            }

        });
        GotoPageInNative.addStarNativeGoToPage(this.props.navigation);
        showToast('version  10');
    }

    getPanoramaProperty(){
        LetDevice.updateAllPropertyCloud().then((data)=>{
            let dataObject = JSON.parse(data);
            // let dataObject = data;
            let stateProps = {};
            //侦测时间
            if (dataObject.PanoramStartMotorPositon) {
                let ary = dataObject.PanoramStartMotorPositon.value.split(',');
                stateProps.leftValue = parseInt(ary[0].substr(1));
            }
            if (dataObject.PanoramEndMotorPositon) {
                let ary = dataObject.PanoramEndMotorPositon.value.split(',');
                stateProps.rightValue = parseInt(ary[0].substr(1));
            }
            this.setState(stateProps);
        }).catch(error=>{

        });
    }

    getSameData(){
        LetDevice.getPropertyCloud('SleepStatus').then((data) =>{ //0休眠 1关闭
            console.log('设备休眠--------SleepStatus' + data,typeof(data));
            if(data=="0"||data==0){
                this.setState({isSleep:true});
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
        imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
            let data = JSON.parse(res);
            this.setState({vipState:data.state});
        }).catch(error=>{
            this.setState({vipState:-1});
        });
        IMIStorage.load({
            key: LetDevice.deviceID+'DistortionCorrection',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({lensCorrect: res.lensCorrect});
        }).catch(_=> {
            this.setState({lensCorrect: false});
        });
        IMIStorage.load({
            key: LetDevice.deviceID+'cameraGuideForZoomed',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({step: res.cameraGuideForZoomed});
        }).catch(_=> {
            this.setState({step: 1});
        });
    }

    getNetWork(){
        IMIStorage.load({
            key: LetDevice.deviceID+'isDataUsageWarning',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage){
                NetInfo.fetch().then(state => {
                    if (state.type=='wifi'){
                        this.IMIVideoView.prepare();
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                        if (state.type == 'cellular'){
                            showToast(stringsTo('isDataUsageTip'));
                        }
                        this.homePageLivePlayerComponent.setPauseView(true);
                        this.setState({currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE});
                    }
                });
            }else {
                this.IMIVideoView.prepare();
            }
        }).catch(_=> {
            this.setState({isDataUsage: false});
            this.IMIVideoView.prepare();
        });
    }

    componentWillUnmount() {
        // if (isAndroid()) {
        //     this.IMIVideoView&&this.IMIVideoView.destroy();
        // }
        LetDevice.removeDeviceEventChangeListener();
         this._subscribe_focus && this._subscribe_focus();
         this._subscribe_blur && this._subscribe_blur();
        this._enterBackground&&this._enterBackground.remove();
        this._enterForeground&&this._enterForeground.remove();
        this.devicePropertyListener&&this.devicePropertyListener.remove();
        this.deviceInfoListener&&this.deviceInfoListener.remove();
        this.timer&&clearTimeout(this.timer);
        GotoPageInNative.removeStarNativeGoToPage();
        // Unsubscribe
        this.unsubscribe();
    }


    _onPressBack = (isFullScreen) => {
        if (isFullScreen) {
            Orientation.lockToPortrait();
            this.props.navigation.setOptions({tabBarVisible: true});
        } else {
            if (!this._canStepInCall())return;
            IMIGotoPage.exit();
        }
    };

    _onClickSleepButton = () =>{ //注意：0是休眠 1是关闭休眠
        let params = {
            SleepStatus: this.state.isSleep ? 1 : 0,
        };
        if (this._isShareUser()) return;
        if(!this._canStepInCall())  return;
        // if (this.state.panoramicType==1||this.state.panoramicType==0){
        //     showToast(stringsTo('Panoramic_loading'));
        //     return ;
        // }
        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            console.log("设置休眠成功-------",data);
            this.setState({isSleep: !this.state.isSleep},()=>{
                if(this.state.isSleep){
                    this.setState({isPanoramic:false});
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    this.IMIVideoView.stop();
                }else{
                    // setTimeout(()=>{
                    //     this.IMIVideoView.prepare();
                    // },200);
                }
            });


        }).catch((err) => {
            console.log("设置休眠失败-------",err);
        });

    };

    //判断当前是否可以操作
    _canStepIn(){
        if(this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return true;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }

    //判断是否通话中、录像中
    _canStepInCall(){
        if (this.state.isCalling)
        {
            showToast(stringsTo('imi_speaking_block'));
            return false;
        }
        if (this.homePageLivePlayerComponent.getRecordStatus())
        {
            showToast(stringsTo('screen_recording'));
            return false;
        }
        return true;
    }

    _doCall() {

        if (this.state.isCalling) {
            this.IMIVideoView.stopSpeak();
            audioNeedRecovery&&this.homePageLivePlayerComponent.setMute(true)
        } else {
            if(this.homePageLivePlayerComponent.getMute()){ //通话前监听是关闭的，则打开监听，并且结束通话后需要再次恢复到静音状态
                audioNeedRecovery = true;
                this.homePageLivePlayerComponent.setMute(false);
            }


            this.IMIVideoView.startSpeak();
        }
        this.setState({isCalling: !this.state.isCalling});
    }

    //顶部导航栏
    _renderNavigationBar(bps, isFullScreen) {
        return (
            <NavigationBar
                type={NavigationBar.TYPE.DARK} backgroundColor={"transparent"}
                title={LetDevice.devNickName}
                subtitle={bps >= 0 ? `${bps}KB/S` : undefined}
                left={[{
                    key: NavigationBar.ICON.BACK,
                    onPress: () => this._onPressBack(isFullScreen)
                }]}
                right={[
                    {
                        key: NavigationBar.ICON.MORE,
                        onPress: _ => {
                            if (!this.state.isOnline){
                                showToast(stringsTo('device_offline'));
                                return true;
                            }
                            if (!this._canStepInCall())return;
                            navigation.push("IMICommSettingPage",
                                {
                                    "showDelDev": false,
                                    "defaultStyleRenderItemArray": [{
                                        title: [stringsTo('popo_setting_camera_text')],
                                        onPress:  ()=> {
                                            this.props.navigation.push("IMICameraSettingVC");
                                        }
                                    },
                                        {
                                            title: [stringsTo('alarmSettingText')],
                                            onPress: ()=> {
                                                if (this._isShareUser())return;
                                                this.props.navigation.push("HouseKeepSetting");
                                            }
                                        }, {
                                            title: [stringsTo('popo_setting_storage_text')],
                                            onPress: () => {
                                                if (this._isShareUser())return;
                                                if (this.state.sdCardStatus == 0){
                                                    showToast(stringsTo('storage_no_sdcard_please_buy'));
                                                } else if (this.state.sdCardStatus == 4){
                                                    showToast(stringsTo('sdcard_error_out'));
                                                } else {
                                                    this.props.navigation.push('SdCardSettingPage');
                                                }
                                                // IMIGotoPage.starNativeFileManagerSettingPage(LetDevice.deviceID)
                                            }
                                        }]
                                });
                        }
                    }
                ]}/>
        );
    }
    renderVideoSubView(isFullScreen,showFullScreenTools){  //竖屏时没有要显示的控件，竖屏显示拨号键、云台
        return (<View style={{width: "100%",
            height: "100%",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute"}} pointerEvents={'box-none'}>
            {this._renderCallViewFullScreen(isFullScreen,showFullScreenTools)}
            {this._rendLandscapeDirectionView(isFullScreen,showFullScreenTools)}
            {this._sleepView()}
            {this._deviceOffLineView()}
        </View>);
    }

    //横屏时的拨号键
    _renderCallViewFullScreen(isFullScreen,showFullScreenTools) {
        if (!isFullScreen || !showFullScreenTools) return null;
        return (<View style={{position: 'absolute', right: 30}}>
            <ImageButton
                style={{width: 44, height: 44}}
                source={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}
                highlightedSource={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}
                onPress={() => {
                    if(!this._canStepIn())  return;
                    isCheckingPermission = true;
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                        if (status === 0) {
                            this._doCall();
                            isCheckingPermission = false;
                        } else if (status === -1) {
                            isCheckingPermission = false;
                            showToast(stringsTo('audio_permission_denied'))
                        }
                    })

                }}
            />

        </View>);
    }

    /*横屏云台View*/
    _rendLandscapeDirectionView(isFullScreen,showFullScreenTools) {
        if(isFullScreen&&showFullScreenTools){
        return (<View
            style={{
                position: "absolute", left: 15, bottom: 15
            }}>

            <JoystickControlView
                onStart={() => {
                }}
                onMove={(type) => {
                    if(!this._canStepIn())  return;
                    ptz_pressed = false;
                    this.setState({isMoveStatus:true});
                    clearInterval(this.setPTZIntervalID);
                    this._doDirection(type);
                }}
                onLoosen={() => {
                    ptz_pressed = false;
                    this.setState({isMoveStatus:false});
                    this.homePageLivePlayerComponent._onPressFullScreenTools();
                    clearInterval(this.setPTZIntervalID);
                    this._getMotorPositonStatus();
                }}
                isFullscreen={true}
                diameterPan={160}
                diameterMid={36}/>
        </View>);}
    }

    /*绘制摇杆式云台View*/
    renderPtzControlView(){
        return (<View
            style={{flexGrow: 1,
                width: "100%",
                flexDirection: "column",
                display: "flex",
                // marginTop: 20,
                paddingRight: 14,paddingLeft: 14,
                alignItems: "center",
                justifyContent: "center"}}>
            {this.state.isPanoramic?<PanoramicView
                Type={this.state.panoramicType}
                accessibilityLabel={"show_all_panorama"}
                onTouch={(pointX) => {
                    this.selectPositionX = pointX;
                    ptz_pressedPanoramic = false;
                    // console.log(" _setPanoramaRotateAngle h=",this.selectPositionX,",v=",this.selectPositionY);
                    this._doDirectionPanorama(this.selectPositionX,50)
                }} onLoosen={() => {}}
                startPanoramic={()=>{
                    if(!this._canStepIn())  return;
                    LetDevice.sendDeviceServerRequest("PanoramaEnableComposition",{}).then((data) => {
                        this.setState({panoramicType:1});
                        this._panoramicTimeout();
                    }).catch((error) => {
                    });
                }}
                endPanoramic={()=>{
                    // if (this.state.panoramicType == 1){
                    //     showToast(stringsTo('panoramicing_tip'));
                    //     return;
                    // }
                    this.setState({isPanoramic:false});}}
                selectPositionX={this.selectPositionX} minSelectPositionLeft={0} maxSelectPositionRight={SCREEN_WIDTH-28} leftValue={this.state.leftValue} rightValue={this.state.rightValue} imgStoreUrl={this.state.overAllImgStoreUrl}/>:<JoystickControlView
                onMove={ (type) => {
                    if(!this._canStepIn())  return;
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    this._doDirection(type);
                } }
                onLoosen={ () => {
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    this._getMotorPositonStatus();
                } }
                isFullscreen={ false}
                diameterPan={ 206 }
                diameterMid={ 40 }/>}
            {
                !this.state.isPanoramic?<XImage style={{
                    position: "absolute", right: 15, bottom: 15
                }} iconSize={50} icon={this.state.isPanoramic?require('../../resources/images/icon_holder.png'): require('../../resources/images/icon_panoramic.png')}
                                                onPress={()=>{
                                                    if (this._isShareUser()) return;
                                                    if(!this._canStepIn())  return;
                                                    let isPanoramic = !this.state.isPanoramic;
                                                    if (isPanoramic){
                                                        showLoading(stringsTo('commLoadingText'),true);
                                                        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                                                            showLoading(false);
                                                            console.log('-------------'+res.url);
                                                            // parseInt(res.status)==1||parseInt(res.status)==0?3:
                                                            let  isEnabledOpen = parseInt(res.status)==2?true:false;
                                                            this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url,isPanoramic:isPanoramic});
                                                        }).catch(error=>{
                                                            showLoading(false);
                                                            this.setState({isPanoramic:isPanoramic});
                                                        });
                                                    }else {
                                                        if (this.state.panoramicType == 1){
                                                            showToast(stringsTo('panoramicing_tip'));
                                                            return;
                                                        }
                                                        this.setState({isPanoramic:isPanoramic});
                                                    }

                                                }}
                />:null
            }

        </View>);
    }

    //
    _panoramicTimeout(){
        this.timer = setTimeout(() => {
                let times = 0;
                this.intervalID = setInterval(() => {
                    times = times + 1;
                    if(times > 6){//rcp20秒后才开始拉取图片，每10秒拉一次6次即1分钟+20秒后还拉不到图片，认为失败了
                        this.intervalID&&clearInterval(this.intervalID);//清除
                        this._getMergePhotoMeta(true);
                        return;
                    }
                    //获取全景图信息
                    this._getMergePhotoMeta(false);
                }, 10000);
            },
            20000
        );
    }

    _getMergePhotoMeta(isLast){
        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
            console.log('-------------'+res.url);
            if (!isLast&&parseInt(res.status)==2){
                this.setState({panoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
            if (isLast){
                if (parseInt(res.status)!=2) {
                    showToast(stringsTo('panoramicError'));
                }
                let  isEnabledOpen = parseInt(res.status)==2?true:false;
                //parseInt(res.status)==1||parseInt(res.status)==0?3: 根据后台状态显示
                this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
        }).catch(error=>{
            if (isLast){
                this.setState({panoramicType:3});
            }
        });
    }


    /*控制云台转动*/
    _doDirection(m_direction){

        ptz_pressed = true;

        this._checkDirection(m_direction);
        if(m_direction !== 5) {
            // 每100毫秒一次操作
            clearInterval(this.setPTZIntervalID);
            this.setPTZIntervalID = setInterval(() => {
                if (!this._checkDirection(m_direction)) {
                    clearInterval(this.setPTZIntervalID);
                }
            }, 100);
        }else{
            ptz_pressed = false;
        }

    }

    /*判断当前是否可操作*/
    _checkDirection(m_direction){

        console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);

        if(ptz_pressed){
            this.sendDirectionCmd(m_direction);
        }

        return ptz_pressed;
    }

    /*发送控制云台的指令*/
    sendDirectionCmd(m_direction) {
        let paramsJSON = {ActionType:m_direction,Step:0};
        LetDevice.sendDeviceServerRequest("PTZActionControl", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------' +m_direction+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    /*控制云台转动*/
    _doDirectionPanorama(h,v){
        if (!this._checkDirectionPanorama(h,v)) {
            ptz_pressedPanoramic = true;
            this._checkDirectionPanorama(h,v);
        }
    }

    /*判断当前是否可操作*/
    _checkDirectionPanorama(h,v){

        console.log("_checkDirection m_direction="+h+",ptz_pressed="+ptz_pressed);

        if(ptz_pressedPanoramic){
            this._setPanoramaRotateAngle(h,v);
        }

        return ptz_pressedPanoramic;
    }
    _setPanoramaRotateAngle(h,v){

        let paramsJSON = {position:"["+h+','+v+"]"};

        LetDevice.sendDeviceServerRequest("PanoramSlide", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------'+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    // 获取转动状态
    _getMotorPositonStatus(){
        LetDevice.getPropertyCloud('MotorPositon').then((data) =>{ //0休眠 1关闭
            console.log('MotorPositon--------' + data,typeof(data));
            // alert(data);
            if (data.substr(0,3)=='-1,'){
                showToast(stringsTo('direction_end_009'));
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status){

        if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        }else if(status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE||status==HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR){
            this._dealWithErrorOrPause();
        }else if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            this.setState({isOnline:true});
        }
        if (status != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) this.setState({currentStatus: status});

    }

    _dealWithErrorOrPause(){
        // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();

        //直播流出错或暂停时，停止通话
        if (this.state.isCalling && this.IMIVideoView) {
            this.setState({isCalling: false});
            this.IMIVideoView.stopSpeak();
        }
        !isCheckingPermission&&this.IMIVideoView && this.IMIVideoView.stop();

    }

    //通话时的波纹动画
    _renderCallWaveView(){
        if (this.state.fullScreen) {
            return null;
        }

        if (!this.state.isCalling) {
            return null;
        }

        return (<View style={{
                width: SCREEN_WIDTH,
                bottom: 83,
                position: "absolute"
            }}>
                <WaveView
                    waveHeight={36}
                    waveWidth={SCREEN_WIDTH}/>
            </View>
        );

    }
    //休眠提示
    _sleepView() {
        if (!this.state.isOnline)return;
        if (!this.state.isSleep) return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       backgroundColor: "#000",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('power_off')}
                />

                <RoundedButtonView buttonText={stringsTo('wake_up')}
                                   buttonStyle={{
                                       margin: 14,
                                       width: 110,
                                       height: 40
                                   }}
                                   onPress={() => {
                                       LetDevice.propertyOn("SleepStatus").then(()=>{
                                           this.setState({isSleep: 0},()=>{
                                               this.IMIVideoView.prepare();
                                           });
                                       }).catch(err=>{
                                           this.setState({isSleep:1});
                                           showToast(I18n.t('operationFailed'));
                                       });
                                   }}/>
            </View>
        );
    }

    //离线提示
    _deviceOffLineView() {
        if (this.state.isOnline) return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('device_offline')}
                />
            </View>
        );
    }

    _isShareUser(){
        if (LetDevice.isShareUser){
            showToast(stringsTo('shareUser_tip'));
            return true;
        }
        return false;
    }

    /*底部tab功能栏*/
    _renderBottomLayout(){
        return (

            <View style={styles.bottomLayout}>
                <ImageBackground style={{display: "flex", width: "100%", height: SCREEN_WIDTH*76/360.0, flexDirection: "row", flexWrap: 'nowrap', alignItems:"center"}}
                                 source={require('../../resources/images/bottom_tab.png')} >
                    {/*看家*/}
                    <TouchableOpacity
                        style={styles.bottomLayoutItem}
                        onPress={()=>{
                            if (this._isShareUser()) return;
                            if(!this._canStepInCall())  return;
                            this.props.navigation.push('AlarmListPage');}}
                    >
                        <Image
                            style={{ width: 26,height: 26 }}
                            source={require('../../resources/images/icon_alarm.png')}
                        />
                        <XText
                            style={{ margin: 2, textAlign:'center',fontSize: 9,color: this.state.isRecording ? "#C0C0C0C0" : "#333333" }}//设计稿改动
                            ellipsizeMode="tail"
                            numberOfLines={2}

                            text={stringsTo('bottom_house_keeping')}
                        />
                    </TouchableOpacity>

                    {/*回看*/}
                    <TouchableOpacity
                        style={styles.bottomLayoutItem}
                        onPress={()=>{
                            if (this.state.sdCardStatus == 0){
                                showToast(stringsTo('storage_no_sdcard_please_buy'));
                                return;
                            } else if (this.state.sdCardStatus == 4){
                                showToast(stringsTo('sdcard_error_out'));
                                return;
                            }
                            if(!this._canStepInCall())  return;
                            if (this.state.isSleep){
                                showToast(stringsTo('power_off'));
                                return ;
                            }
                            if (!this.state.isOnline){
                                showToast(stringsTo('device_offline'));
                                return ;
                            }
                            this.props.navigation.push('PlayBackPage');
                        }}
                    >
                        <Image
                            style={{ width: 30,height: 30 }}
                            source={require('../../resources/images/icon_playback.png')}
                        />
                        <Text
                            style={{ margin: 5,fontSize: 10,color : "#333333" }}
                            ellipsizeMode="tail"
                            numberOfLines={2}
                        >
                            {stringsTo('playBackText')}
                        </Text>
                    </TouchableOpacity>

                    {/*通话*/}
                    <TouchableOpacity
                        style={[styles.bottomLayoutItem, {marginTop: 5}]}
                        onPress={() => {
                            if(!this._canStepIn())  return;
                            isCheckingPermission = true;
                            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                                if (status === 0) {
                                    this._doCall();
                                    isCheckingPermission = false;
                                } else if (status === -1) {
                                    isCheckingPermission = false;
                                    showToast(stringsTo('audio_permission_denied'))
                                }
                            })
                        }}
                    >
                        <Image
                            style={{width: 56, height: 56}}
                            source={this.state.isCalling?require("../../resources/images/icon_hangup.png"):require("../../resources/images/icon_call.png")}
                        />

                    </TouchableOpacity>

                    {/*云存*/}
                    {
                        <TouchableOpacity
                            style={styles.bottomLayoutItem}
                            onPress={() => {
                                if (this._isShareUser()) return;
                                if(!this._canStepInCall())  return;
                                if (this.state.vipState == -1){
                                    showLoading(stringsTo('commLoadingText'),true);
                                    imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
                                        let data = JSON.parse(res);
                                        showLoading(false);
                                        this.setState({vipState:data.state});
                                        this.goToClound();
                                    }).catch(error=>{
                                        showLoading(false);
                                        this.setState({vipState:0});
                                        this.goToClound();
                                    });
                                } else this.goToClound();
                            }}
                        >
                            <Image
                                resizeMode='contain'
                                style={{width: 30, height: 30}}
                                source={require('../../resources/images/icon_cloud.png')}
                            />
                            <Text
                                style={{margin: 5, fontSize: 10, color: "#333333"}}
                                ellipsizeMode="tail"
                                numberOfLines={1}
                            >
                                {stringsTo('bottom_cloud_storage')}
                            </Text>
                        </TouchableOpacity>
                    }

                    {/*快捷*/}
                    {
                        <TouchableOpacity
                            style={styles.bottomLayoutItem}
                            onPress={()=>{
                                if(!this._canStepInCall())  return;
                                IMIGotoPage.startAlbumPage(LetDevice.deviceID)
                            }}
                        >
                            <Image
                                style={{width: 30, height: 30}}
                                source={require('../../resources/images/icon_alarm.png')}
                            />
                            <Text
                                style={{margin: 5, fontSize: 10, color: "#333333"}}
                                ellipsizeMode="tail"
                                numberOfLines={2}
                            >
                                {stringsTo('bottom_video_album')}
                            </Text>
                        </TouchableOpacity>
                    }

                </ImageBackground>
            </View>

        )
    }

    goToClound(){
        if (this.state.vipState==0){
            IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
        } else {
            IMIGotoPage.startAliMoveDetectionPage();
        }
    }

    render() {
        let sleepModeIndex = this.state.isSleep ? 1 : 0;
        let sleepBtn = {
            isText: false,
            data: [require("../../resources/images/icon_sleep_off.png"),require("../../resources/images/icon_sleep_on.png")],
            onPress: this._onClickSleepButton,
            disabled: false,
            dataIndex: sleepModeIndex
        };

        //固件不支持 自动 0 {title:'高清',index:2},
        return (
            <XView style={{flex:1}}>
                <HomePageLivePlayerComponent {...this.props}
                                             ref={component => (this.homePageLivePlayerComponent = component)}
                                             videoRef={ref => this.IMIVideoView = ref}
                                             qualityData={[{title:stringsTo('quality_360'),index:1},{title:stringsTo('quality_25k_v2'),index:2}]}
                                             navBar={(bps, isFullScreen) => this._renderNavigationBar(bps, isFullScreen)}
                                             toolBarMoreItems={[{item: sleepBtn, insertIndex: 0}]}
                                             videoSubView={(isFullScreen,showFullScreenTools) => this.renderVideoSubView(isFullScreen,showFullScreenTools)}
                                             onLivePlayerStatusChange={(status)=>this._onLivePlayerStatusChangeListener(status)}
                                             lensCorrect={{use: this.state.lensCorrect, x: lensCorrect_x, y: lensCorrect_y}}
                                             isSleepStatus={this.state.isSleep}
                                             isMove={this.state.isMoveStatus}
                                             albumName={LetDevice.deviceID}
                                             isOnLine={this.state.isOnline}
                                             onCheckPermissionStatusChange={(value)=> isCheckingPermission = value}
                >
                    {this._renderCallWaveView()}
                    {this.renderPtzControlView()}
                    {this._renderBottomLayout()}
                    {!this.state.fullScreen ? <RNLine style={{height: 1}}/> : null}

                </HomePageLivePlayerComponent>
                {this.state.step?<XView style={{position: "absolute",top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)'}} onPress={()=>{
                    if (this.state.step==1){
                        this.setState({step:2})
                    }
                    if (this.state.step==2){
                        this.setState({step:0})
                        IMIStorage.save({
                            key: LetDevice.deviceID+'cameraGuideForZoomed',
                            data: {
                                cameraGuideForZoomed: 0
                            },
                            expires: null,
                        });
                    }
                }}>
                    {this.state.step==1?<XView style={{height:"50%",justifyContent:'center',alignItems:'center'}}>
                        <XImage iconSize={50} icon={require('../../resources/images/icon_zoom.png')}/>
                        <XText style={{marginTop:35,color:colors.white,textAlign:'center'}} text={stringsTo('camera_guide_for_zoomed_str')}/>
                    </XView>:null}
                    {this.state.step==2?
                        <XView style={{position: "absolute", right: 15, bottom: SCREEN_WIDTH*70/360.0,left:20,height:80,alignItems:'flex-end'}}>
                            <XText style={{color:colors.white,textAlign:'center'}} text={stringsTo('camera_guide_for_panoramic_str')}/>
                            <XImage style={{marginTop:14}} iconSize={50} icon={require('../../resources/images/icon_panoramic.png')}
                            />
                        </XView>:null}
                </XView>:null}
            </XView>


        );
    }

    getSDCardStatus(){
        LetDevice.getPropertyCloud('StorageStatus').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log('getPropertyCloud---------' + value + typeof (value));
            value = parseInt(value);
            this.setState({sdCardStatus: value});
            if (value == 0) { //未插卡
            } else if (value == 4) { //存储卡已弹出\n请重新插拔存储卡!
            } else  { //正在格式化
              }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: SCREEN_WIDTH*60/360.0,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center",
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        marginTop: 15,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
});
