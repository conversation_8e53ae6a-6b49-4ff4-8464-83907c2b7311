import {TouchableOpacity, Text, View, StyleSheet, Image, ScrollView, Modal} from 'react-native';
import {React, useEffect, useRef, useState} from 'react';
import {LetDevice} from '../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {cloudDeviceService} from '../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import IMCloudServerVideoView from '../../../imilab-rn-sdk/native/camera-kit/IMCloudServerVideoView';
import {PlayerClass} from '../../../imilab-rn-sdk/native/camera-kit/IMIVideoView';
import IMIFile from '../../../imilab-rn-sdk/native/local-kit/IMIFile';
//import {toStr} from 'imilab-rn-sdk';
const deviceID = LetDevice.deviceID;

const getJsonData = value => {
  let data = {};
  try {
    data = JSON.parse(value);
  } catch (error) {
    data = {};
  }
  return data;
};

const CLoudService = () => {
  const [list, setList] = useState([]);
  const [gainFreeVisible, setGainFreeVisible] = useState({
    visible: false,
    plantId: '',
  });

  const [videoVisible, setVideoVisible] = useState({
    visible: false,
    item: {},
  });

  useEffect(() => {
    //console.log(toStr);

    getList();
  }, []);

  /* vip状态 */
  const getVipStatus = async () => {
    const data = await cloudDeviceService.getVipStatus(deviceID).catch(e => {
      console.log(e);
    });
    if (data) {
      console.log(data);
      alert('成功' + data);
    }
  };
  /* 视频列表 */
  const getList = async () => {
    const now = new Date();
    const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const midnightMilliseconds = midnight.getTime();
    const data = await cloudDeviceService.getPlayList(deviceID, `${midnightMilliseconds}`, `${now.getTime()}`, '20');
    if (data) {
      const list = JSON.parse(data)?.cloudStoragePlayList;
      console.log(list);

      setList(list);
    }
  };
  //获取云存订单列表
  const getStorageOrders = async () => {
    const data = await cloudDeviceService.getStorageOrders(deviceID, '1', '10').catch(e => {
      console.log(e);
    });
    if (data) {
      console.log(data);
      alert('成功' + data);
    }
  };

  //获取免费云存套餐信息
  const getStorageFree = async () => {
    const data = await cloudDeviceService.getStorageFree(deviceID, LetDevice.model).catch(e => {
      console.log(e);
    });
    if (data) {
      console.log(getJsonData(data));
      const res = getJsonData(data);
      if (res?.able) {
        controlGainFreeVisible(getJsonData(data)?.plan?.id);
      } else {
        alert('没有次数');
      }
    }
  };
  //领取免费云存套餐
  const gainFreeStorage = () => {
    cloudDeviceService
      .gainFreeStorage(deviceID, LetDevice.model, `${gainFreeVisible?.plantId}`)
      .catch(e => {
        console.log(2222222, e);
      })
      .then(e => {
        console.log(e);
        alert('成功领取');
        controlGainFreeVisible();
      });
  };
  const controlGainFreeVisible = plantId => {
    setGainFreeVisible({visible: !gainFreeVisible.visible, plantId});
  };

  const controlVideoVisible = item => {
    setVideoVisible({visible: !videoVisible.visible, item});
  };
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.buttonStyle} onPress={getVipStatus}>
        <Text> 获取vip状态</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.buttonStyle} onPress={getStorageOrders}>
        <Text> 获取云存订单列表</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.buttonStyle} onPress={getStorageFree}>
        <Text> 获取免费云存套餐信息</Text>
      </TouchableOpacity>
      <ScrollView style={styles.scrollView}>
        <View style={styles.imageContainer}>
          {list.map(item => {
            return (
              <DownImage
                key={item.segmentId}
                imgUrl={getJsonData(item.imgUrl)}
                eventData={item}
                controlVideoVisible={controlVideoVisible}
              />
            );
          })}
        </View>
      </ScrollView>
      <Modal
        animationType="slide"
        transparent={true}
        visible={gainFreeVisible.visible}
        onRequestClose={() => {
          setGainFreeVisible({visible: false});
        }}>
        <View style={styles.modalView}>
          <TouchableOpacity style={styles.buttonStyle} onPress={gainFreeStorage}>
            <Text> 领取免费云存套餐</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={controlGainFreeVisible}>
            <Text> 关闭</Text>
          </TouchableOpacity>
        </View>
      </Modal>
      <VideoModal videoItem={videoVisible.item} onControl={controlVideoVisible} visible={videoVisible.visible} />
    </View>
  );
};

const DownImage = ({imgUrl, eventData, controlVideoVisible}) => {
  const [url, setUrl] = useState('');
  useEffect(() => {
    const getImageUrl = async () => {
      /* const url = await cloudDeviceService
        .getM3U8Url(deviceID, imgUrl?.segmentId)
        .catch(e => {
          console.log(333333, e);
        });
      if (url) {
        // console.log(url);
      } */
      const data = await cloudDeviceService
        .downLoadImage(
          deviceID,
          imgUrl?.segmentId,
          imgUrl?.publicKeyVersion,
          getJsonData(eventData?.imgSignedDownloadUrl).imgPath,
        )
        .catch(e => {
          console.log(22222, e);
        });

      if (data) {
        setUrl(data);
      }
    };
    getImageUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const openVideoModal = () => {
    controlVideoVisible(eventData);
  };
  return (
    <View style={styles.imgTable}>
      <TouchableOpacity onPress={openVideoModal}>
        <Image style={styles.imgs} source={{uri: `file://${url}`}} />
        <Text>{eventData?.eventDescription}</Text>
      </TouchableOpacity>
    </View>
  );
};

const VideoModal = ({videoItem, onControl, visible}) => {
  const videoRef = useRef();

  const [volume, setVolume] = useState(0); //音量
  const [mute, setMute] = useState(false); //是否静音
  const [scaleRatio, setScaleRatio] = useState(1.0); // 縮放比例
  const [renderAspectRatio, setRenderAspectRatio] = useState(1); //缩放系数
  const [seekTo, setSeekTo] = useState(1); //设置快进
  const [speed, setSpeed] = useState(undefined); //设置播放器倍速

  const downloadVideo = () => {
    cloudDeviceService
      .downloadVideo(
        deviceID,
        videoItem?.segmentId,
        videoItem?.publicKeyVersion,
        '/storage/emulated/0/Android/data/com.argusak.demo/cache/thingCloudService/image',
        `${new Date().getTime()}.mp4`,
      )
      .then(() => {
        alert('下载成功');
        console.log('下载成功');
      })
      .catch(e => {
        console.log(e);
      })
      .finally(() => {
        alert('下载成功');
      });
  };

  const _assignRoot = component => {
    if (!videoRef.current) {
      videoRef.current = component;
      videoRef.current?.prepare();
    }
  };

  const _onPrepared = data => {
    console.log(`_onPrepared code : ${data}  `);
    videoRef.current.start();
  };

  const _onEventChange = event => {
    console.log(`_onEventChange  ------------------- : ${event.extra.bps} `);
  };
  const _onErrorChange = data => {
    console.log(`_onErrorChange code : ${data} `);
  };
  const _onRecord = eventData => {
    console.log(` _onRecord  eventData : ${eventData} + data ${JSON.parse(eventData)}`);
  };
  const _onModeChange = modeData => {
    console.log(` _onModeChange : ${modeData} + data ${JSON.parse(modeData)}`);
  };
  const _onIRecordState = data => {
    console.log(` _onIRecordState : ${data} + data ${JSON.parse(data)}`);
  };
  const _onCommCallback = data => {
    console.log(` _onCommCallback : ${data} + data ${JSON.parse(data)}`);
  };
  const _onPlayCompletion = data => {
    console.log(` _onPlayCompletion : ${data} + data ${JSON.parse(data)}`);
  };
  const _onVideoViewClick = data => {
    console.log(` _onVideoViewClick : ${data} + data ${JSON.parse(data)}`);
  };
  const _onVideoZoomScale = data => {
    console.log(` _onVideoZoomScale : ${data} + data ${JSON.parse(data)}`);
  };

  const onSetMust = () => {
    setMute(!mute);
  };

  const onSetVolume = () => {
    setVolume(1.0);
  };

  const onSetScaleRatio = () => {
    setScaleRatio(2.0);
  };

  const onSetRenderAspectRatio = () => {
    setRenderAspectRatio(2);
  };

  const onSetSeekTo = () => {
    setSeekTo(2);
  };

  const onSetSpeed = () => {
    setSpeed(2);
  };

  const onClose = () => {
    videoRef.current?.destroy();
    onControl();
  };

  const start = () => {
    videoRef.current?.start();
  };

  const stop = () => {
    videoRef.current?.stop();
  };

  const resume = () => {
    videoRef.current?.resume();
  };

  const pause = () => {
    videoRef.current?.pause();
  };

  const startRecord = () => {
    videoRef.current?.startRecord(`${IMIFile.storageBasePath}/tmp/${new Date().getTime()}.mp3`);
  };

  const stopRecord = () => {
    videoRef.current?.stopRecord();
  };

  const snap = () => {
    const snapShotPath = `${IMIFile.storageBasePath}/tmp/${new Date().getTime()}.jpg`;
    videoRef.current
      ?.snap(snapShotPath)
      .then(e => {
        IMIFile.saveImageToPhotosAlbum(snapShotPath, LetDevice.deviceID)
          .then(_ => {
            alert('保存成功');
          })
          .catch(code => {
            console.log(code);

            alert('code' + JSON.stringify(code));
          });
      })
      .catch(e => {
        console.log(e);
      });
  };

  const snapWithSize = () => {
    videoRef.current?.snapWithSize(`${IMIFile.storageBasePath}/tmp/${new Date().getTime()}.jpg`, 320, 180);
  };
  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.modalView}>
        <View style={styles.test111111} />
        <IMCloudServerVideoView
          style={{height: 180, width: 320}}
          ref={_assignRoot}
          playerClass={PlayerClass.HLS}
          mute={mute}
          dataSource={{
            productId: LetDevice.model,
            iotId: LetDevice.deviceID,
            segmentId: videoItem?.segmentId,
            playerClass: PlayerClass.HLS,
          }}
          seekTo={seekTo}
          speed={speed}
          renderAspectRatio={renderAspectRatio}
          volume={volume}
          scaleRatio={scaleRatio}
          onPrepared={_onPrepared}
          onEventChange={_onEventChange}
          onErrorChange={_onErrorChange}
          onRecordTimeChange={_onRecord}
          onModeChange={_onModeChange}
          onIRecordState={_onIRecordState}
          onCommCallback={_onCommCallback}
          onPlayCompletion={_onPlayCompletion}
          onVideoViewClick={_onVideoViewClick}
          onVideoZoomScale={_onVideoZoomScale}
        />
        <View style={styles.funTable}>
          <TouchableOpacity style={styles.buttonStyle} onPress={start}>
            <Text> 开始播放</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={stop}>
            <Text> 结束播放</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={resume}>
            <Text>恢复播放</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={pause}>
            <Text>暂停播放</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle}>
            <Text>销毁播放器</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={startRecord}>
            <Text>开始录音</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={stopRecord}>
            <Text>结束录音</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={snap}>
            <Text>截图</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={snapWithSize}>
            <Text>以指定的分辨率截图</Text>
          </TouchableOpacity>
        </View>
        <ScrollView>
          <TouchableOpacity style={styles.buttonStyle} onPress={downloadVideo}>
            <Text> 下载当前视频</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={onSetVolume}>
            <Text> 设置音量大小</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={onSetMust}>
            <Text> 设置静音{mute ? '开' : '关'}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.buttonStyle} onPress={onSetScaleRatio}>
            <Text> 设置缩放系数</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={onSetRenderAspectRatio}>
            <Text> 设置播放器是否充满全屏,以及视频显示比例</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={onSetSeekTo}>
            <Text> 设置快进</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyleBlue} onPress={onSetSpeed}>
            <Text> 设置播放器倍速</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buttonStyle} onPress={onClose}>
            <Text> 关闭</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default CLoudService;
const styles = StyleSheet.create({
  container: {
    paddingTop: 154,
    alignItems: 'center',
    flex: 1,
  },

  buttonStyle: {
    paddingTop: 20,
    backgroundColor: 'red',
  },
  buttonStyleBlue: {
    paddingTop: 20,
    backgroundColor: 'blue',
  },
  scrollView: {
    width: '100%',
  },
  imgTable: {
    width: 100,
    alignItems: 'center',
  },
  imgs: {
    width: 100,
    height: 100,
  },
  imageContainer: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    marginTop: 150,
    backgroundColor: '#666',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  test111111: {
    width: 100,
    height: 20,
  },
  funTable: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
    marginTop: 10,
  },
});
