import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  SafeAreaView,
  Dimensions,
  BackHandler,
  Image,
  DeviceEventEmitter,
  Text,
} from 'react-native';

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';

import {
  colors,
  showLoading,
  showToast,
  MultiSelectableNavigationBar,
  IMIDesignEmptyView,
  IMIImageView,
  MessageDialog,
} from '../../../../imilab-design-ui';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {LetDevice, BaseDeviceComponent} from '../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import iconEmpty from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/police_pic_empty_p.png';
import IMIToast from '../../../../imilab-design-ui/src/widgets/IMIToast';
import {RRCLoading, RRCToast} from '../../../../imilab-design-ui/src/widgets/overlayer';
import {isAndroid, isIos, isIphoneXSeries} from '../../../../imilab-rn-sdk/utils/Utils';
import Orientation from 'react-native-orientation';

import moment from 'moment';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
// let windowWidth = Dimensions.get('window').width;
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;

export default class CameraListPage extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
      fullScreen: false, //Orientation.getInitialOrientation() !== 'PORTRAIT',
      /**
       *  [
       *      {
       *          title:"",
       *          data:[
       *              {
       *                  mediaType:obj.mediaType
       *                  duration:obj.duration
       *                  modificationDate:obj.modificationDate
       *                  url:obj.url
       *                  select: false
       *               }
       *               ]
       *        }
       *  ]
       */
      dataList: [],
      alarmDialog: false,
      length: 0,

      selectedTitle: stringsTo('bottom_video_album'),
      isSelectAll: false,
      isSelectMode: false,
      disableSelect: true,
      flatListHeight: 0,
      showDeleteTip: false, //删除图片重复提醒
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      Orientation.lockToPortrait();
    }
  }

  componentWillUnmount() {
    if (isAndroid()) {
      Orientation.lockToPortrait();
    }
    this._subscribe_focus && this._subscribe_focus();
    this._onResumeListener && this._onResumeListener.remove();
    this.subscriptionDeleteBack && this.subscriptionDeleteBack.remove();
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
      this.backHandler && this.backHandler.remove();
    }
  }

  componentDidMount() {
    console.log('jeffSHUCHECK', '_saveVideoToLocal saveVideoToPhotosAlbum');
    if (isAndroid()) {
      Orientation.lockToPortrait();
    }
    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      //app页面回到插件
      NavigationBar.setBarStyle('dark-content'); // 修改从云存界面返回状态栏显示白色字体问题
    });

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      NavigationBar.setBarStyle('dark-content'); // 修改从云存界面返回状态栏显示白色字体问题
      /*Orientation.lockToPortrait();
            this.setState({fullScreen:false});*/
    });

    console.log('SHUCHECK', '开始请求数据');
    this._queryData();

    this.subscriptionDeleteBack = DeviceEventEmitter.addListener('BackToDelete', param => {
      //监听其他页面返回是删除
      console.log('jeff back delete');
      this._queryData();
    });
    if (isAndroid()) {
      this.backHandler = BackHandler.addEventListener('hardwareBackPress', this.onBackHandler.bind(this));
    }
  }
  onBackHandler() {
    if (this.state.isSelectMode) {
      this.setNavigation({
        isSelectAll: false,
        isSelectMode: false,
        callback: () => {
          this._allChoice(false); //退出选择模式也需要全部不选
        },
      });
      return true;
    } else {
      this.props.navigation.goBack();
      return true;
    }
  }
  setNavigation({
    isSelectAll = this.state.isSelectAll,
    isSelectMode = this.state.isSelectMode,
    disableSelect = this.state.disableSelect,
    callback = () => {},
  }) {
    let title = stringsTo('bottom_video_album'); //TODO:显示数量
    this.setState(
      {
        disableSelect: disableSelect,
        isSelectAll: isSelectAll,
        isSelectMode: isSelectMode,
        selectedTitle: title,
      },
      callback,
    );
  }

  /**
   * 通过时间戳获取日期
   * @param timestamp
   * @private
   */
  _getYearMonthDay(timestamp) {
    let date = new Date(timestamp * 1000);
    let month = (date.getMonth() + 1 < 10 ? '0' : '') + (date.getMonth() + 1);
    let year = date.getFullYear();
    let day = (date.getDate() < 10 ? '0' : '') + date.getDate();
    return year + '-' + month + '-' + day;
  }
  _queryData(flag) {
    !flag && showLoading(stringsTo('commLoadingText'), true);
    IMIFile.getSourceFromPhotosDidAlbum(LetDevice.deviceID)
      .then(r => {
        console.log('[[[[[', r);
        let jsonArray = isIos() ? r : r.data;
        if (jsonArray.length === 0) {
          this.setState({
            disableSelect: true,
            dataList: [],
          });
          showLoading(false);
          return;
        }
        this.setState({
          disableSelect: false,
        });
        //排下序，让新的文件在前面
        jsonArray.sort((a, b) => {
          return b.modificationDate - a.modificationDate;
        });
        let tempArray = new Map();
        for (let i = 0; i < jsonArray.length; i++) {
          let obj = jsonArray[i];
          let title = this._getYearMonthDay(obj.modificationDate);
          if (tempArray.get(title) == null) {
            tempArray.set(title, []);
          }
          tempArray.get(title).push({
            mediaType: obj.mediaType, //媒体类型 1照片,2视频
            duration: obj.duration, // 如果是视频 视频的时长mm
            modificationDate: obj.modificationDate, // 文件创建的时间
            url: obj.url, // 文件的路径
            videoIcon: obj.videoIcon, // 文件的路径
            select: false, //是否选中
          });
        }
        let regroupDataList = [];
        tempArray.forEach((value, key) => {
          let tempObj = {
            title: key,
            data: value,
          };
          regroupDataList.push(tempObj);
        });
        this._changeDataList(regroupDataList);

        console.log('SHUCHECK', '_saveVideoToLocal _queryDataList', JSON.stringify(regroupDataList));
      })
      .catch(error => {
        showLoading(false);
        console.log('SHUCHECK', '_saveVideoToLocal error', JSON.stringify(error));
      });
  }

  /**
   * 统一数据更改方法
   * 在该方法中作唯一状态改变
   * @param newDataList
   * @private
   */
  _changeDataList(newDataList = this.state.dataList) {
    let {amount, selectCount, isAllSelect} = this._getSelectCount(newDataList);
    let disableSelect = amount === 0;
    let isSelectMode = disableSelect ? false : this.state.isSelectMode;
    let isSelectAll = disableSelect ? false : isAllSelect;
    let selectedTitle = this.state.selectedTitle;
    if (isSelectMode) {
      if (selectCount === 0) {
        selectedTitle = stringsTo('select_title_1');
      } else {
        selectedTitle = I18n.t('select_title_3', {code: selectCount});
      }
    } else {
      selectedTitle = stringsTo('bottom_video_album');
    }
    this.setState({
      dataList: newDataList,
      isSelectMode: isSelectMode,
      isSelectAll: isSelectAll,
      selectedTitle: selectedTitle,
      disableSelect: disableSelect,
    });
    showLoading(false);
  }
  /**
   * 单选
   * @param section
   * @param index
   * @param isSelect
   * @private
   */
  _singleChoice(section, index, isSelect) {
    let data = section.data;
    data[index].select = isSelect; //这里修改的就是this.state.dataList中的数据，只需要重新setState
    let tempList = [...this.state.dataList];
    this._changeDataList(tempList);
  }

  /**
   * 选中当天所有的
   * @param section
   * @param isSelect
   * @private
   */
  _multipleChoice(section, isSelect) {
    let data = section.data;
    for (let i = 0; i < data.length; i++) {
      data[i].select = isSelect;
    }
    let tempList = [...this.state.dataList];
    this._changeDataList(tempList);
  }

  /**
   * 全选
   * @param isSelect
   * @private
   */
  _allChoice(isSelect) {
    let tempList = [...this.state.dataList];
    tempList.forEach((value, index) => {
      value.data.forEach(item => {
        item.select = isSelect;
        return item;
      });
      return value;
    });
    this._changeDataList(tempList);
  }

  /**
   * 获取选中的数量
   * @param dataList
   * @private
   */
  _getSelectCount(dataList = this.state.dataList) {
    let counter = 0;
    let selectCounter = 0;
    let isAllSelect = true;
    for (let i = 0; i < dataList.length; i++) {
      let data = dataList[i].data;
      for (let j = 0; j < data.length; j++) {
        if (data[j].select) {
          selectCounter++;
        } else {
          isAllSelect = false;
        }
        counter++;
      }
    }
    return {amount: counter, selectCount: selectCounter, isAllSelect: isAllSelect};
  }

  /**
   * 删除相册文件
   * @private
   */
  _deleteAlbumFiles() {
    RRCLoading.show(stringsTo('delete_title_loading'));
    let dataList = this.state.dataList;
    console.log('删除图片数据', dataList);
    let deleteList = [];
    for (let i = 0; i < dataList.length; i++) {
      let data = dataList[i].data;
      for (let j = 0; j < data.length; j++) {
        if (data[j].select) {
          console.log('删除选中图片', i, j, data);
          deleteList.push(data[j].url);
        }
      }
    }

    console.log('shucheck', `_deleteAlbumFiles  deleteList : ${JSON.stringify(deleteList)}`);
    IMIFile.deletePhotosFromAlbum(deleteList)
      .then(res => {
        console.log('shucheck', `_deleteAlbumFiles -----> deletePhotosFromAlbum ====res==== ${JSON.stringify(res)}`);
        // this._deleteItemsFormDataList();
        this.delayTime = setTimeout(() => {
          RRCLoading.hide();
          IMIToast.showToast(stringsTo('delete_success'))
          this.setNavigation({
            isSelectAll: false,
            isSelectMode: false,
            callback: () => {
              // this._allChoice(false); //退出选择模式也需要全部不选
              this._queryData(true);
              console.log('延时2s去重新请求');
            },
          });
          this.delayTime && clearTimeout(this.delayTime);
        }, 2000);

        IMILogUtil.uploadClickEventForCount('SettingsGalleryDeleteFiles');
        // RRCLoading.hide();
      })
      .catch(err => {
        console.log('shucheck', `_deleteAlbumFiles -----> deletePhotosFromAlbum ====err==== ${JSON.stringify(err)}`);
        IMIToast.showToast(stringsTo('delete_failed'));
        RRCLoading.hide();
      });
  }

  /**
   * 删除被选中的数据
   * @private
   */
  _deleteItemsFormDataList() {
    let tempList = [...this.state.dataList];
    let tempMap = new Map();
    for (let i = 0; i < tempList.length; i++) {
      let data = tempList[i].data;
      let tempDataArray = [];
      for (let j = 0; j < data.length; j++) {
        if (!data[j].select) {
          //如果没有被选中那就是需要留下的数据
          tempDataArray.push(data[j]);
        }
      }
      if (tempDataArray != null && tempDataArray.length !== 0) {
        tempMap.set(tempList[i].title, tempDataArray);
      }
    }
    let regroupDataList = [];
    tempMap.forEach((value, key) => {
      let tempObj = {
        title: key,
        data: value,
      };
      regroupDataList.push(tempObj);
    });
    console.log('shucheck', `_deleteItemsFormDataList ----> regroupDataList: ${JSON.stringify(regroupDataList)}`);
    this._changeDataList(regroupDataList);
    this.delayTime = setTimeout(() => {
      RRCLoading.hide();
      this.setNavigation({
        isSelectAll: false,
        isSelectMode: false,
        callback: () => {
          this._allChoice(false); //退出选择模式也需要全部不选
          this._queryData();
          console.log('延时2s去重新请求');
        },
      });
      this.delayTime && clearTimeout(this.delayTime);
    }, 2000);
  }
  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: 'white'}}>
        <MultiSelectableNavigationBar
          title={this.state.selectedTitle}
          isSelectAll={this.state.isSelectAll} // 是否全选
          isSelectMode={this.state.isSelectMode} // 是否处于选择模式
          disableSelect={this.state.disableSelect}
          onEnterSelectMode={() => {
            this.setNavigation({
              isSelectAll: false,
              isSelectMode: true,
              callback: () => {
                this._changeDataList();
              },
            });
          }} // 进入选择模式
          onExitSelectMode={() => {
            this.setNavigation({
              isSelectAll: false,
              isSelectMode: false,
              callback: () => {
                this._allChoice(false); //退出选择模式也需要全部不选
              },
            });
          }} // 退出选择模式
          onSelectAll={() => {
            this.setNavigation({
              isSelectAll: true,
              callback: () => {
                this._allChoice(true);
              },
            });
          }} // 全选
          onUnselectAll={() => {
            this.setNavigation({
              isSelectAll: false,
              callback: () => {
                this._allChoice(false);
              },
            });
          }} // 取消全选
          onBackPress={() => {
            this.props.navigation.goBack();
          }} // 返回
        />
        {/*<XView raw={true} style={{flex: 1, bottom: 14}}>  */}
        <XView raw={true} style={{flex: 1}}>
          <XFlatList
            raw={true}
            style={{
              backgroundColor: colors.white,
            }}
            sections={this.state.dataList}
            keyExtractor={(item, index) => item + index}
            renderSectionHeader={this._renderSectionHeader}
            onRefresh={() => {
              console.log('shucheck', 'onRefresh ----> _queryData');
              this._queryData();
            }}
            refreshStatus={{
              PreLoading: {text: I18n.t('housekeeping_no_event')}, //不传值默认是first loading，在当天视频为空且续费成功回来后必现
              RefreshingData: {text: I18n.t('commLoadingText')},
              NoData: {text: I18n.t('housekeeping_no_event')},
              LoadFailure: {text: I18n.t('commLoadingFailText')},
              //列表底部提示文案
              LoadingMoreData: {moreText: I18n.t('commLoadingMoreDataText')},
              NoMoreData: {moreText: I18n.t('commNoMoreDataText')},
              LoadMoreFailure: {moreText: I18n.t('commLoadingClickText')},
              NetException: {moreText: I18n.t('commLoadingFailText')},
            }}
            renderEmptyViewFunc={(status, isEmpty) => this._renderEmptyView(status, isEmpty)}
            onLayout={e => {
              let height = e.nativeEvent.layout.height;
              if (this.state.flatListHeight < height) {
                this.setState({flatListHeight: height});
              }
            }}
            stickySectionHeadersEnabled={false}
            numColumns={3}
            ref={refreshList => (this.refreshList = refreshList)}
            contentContainerStyle={styles.listViewStyle}
            renderItem={({item, index, section}) => this._renderItem(item, index, section)}
            // getItemLayout={(data, index) => (
            //     { length: ((windowWidth) / 3 - 15)*0.56, offset: ((windowWidth) / 3 - 15)*0.56 * index, index}
            // )}
          />
          {this._renderDeleteView()}
        </XView>
        <MessageDialog
          title={stringsTo('delete_alert')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showDeleteTip: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteAlertTip',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okDeleteAlertTip',
              callback: _ => {
                this.setState({showDeleteTip: false});
                //TODO:执行删除逻辑
                this._deleteAlbumFiles();
              },
            },
          ]}
        />
      </SafeAreaView>
    );
  }

  _renderSectionHeader = item => {
    let dataArray = item.section.data;
    let isSelected = true;
    for (let i = 0; i < dataArray.length; i++) {
      if (!dataArray[i].select) {
        isSelected = false; //如果内部数据有一个未选中就设置为整体未选中
      }
    }
    return (
      <View
        style={{
          paddingLeft: 14,
          flexDirection: 'row',
          marginTop: 15,
          alignItems: 'center',
          height: 20,
          width: Dimensions.get('window').width,
        }}>
        {this.state.isSelectMode ? (
          <TouchableOpacity
            onPress={() => {
              //TODO:点击了选中当天所有的item,执行全选的逻辑
              this._multipleChoice(item.section, !isSelected);
            }}>
            <XImage
              raw={true}
              style={{width: 20, height: 20, marginRight: 10}}
              icon={
                isSelected
                  ? require('../../resources/images/icon_cruise_select.png')
                  : require('../../resources/images/icon_select_not_choose.png')
              }
            />
          </TouchableOpacity>
        ) : null}
        <Text style={{color: '#7F7F7F'}}>{item.section.title}</Text>
      </View>
    );
  };

  _renderEmptyView(status, isEmpty) {
    let icon = isEmpty ? iconEmpty : null;
    return (
      <IMIDesignEmptyView
        rootStyle={{height: this.state.flatListHeight, borderTopWidth: 0}}
        defaultIcon={icon}
        defaultText={status.text}
      />
    );
  }

  _renderItem = (item, index, section) => {
    let {mediaType, url, select, videoIcon} = item;
    let showUrl = url;
    if (mediaType == 2 && isIos()) {
      showUrl = videoIcon;
    }
    return (
      <XView
        raw={true}
        key={index}
        style={styles.itemParent}
        onLongPress={() => {
          if (!this.state.isSelectMode) {
            this.setNavigation({
              isSelectAll: false,
              isSelectMode: true,
              callback: () => {
                // this._changeDataList();
                this._singleChoice(section, index, true);
              },
            });
          }
        }}
        onPress={() => {
          //TODO:选中
          if (this.state.isSelectMode) {
            this._singleChoice(section, index, !select);
          } else {
            //TODO:跳转到预览界面
            switch (mediaType) {
              case 1: {
                // this.props.navigation.push('ImagePreView', {mediaData: item});
                this.props.navigation.push('PhotoView', {
                  imageUrl: item.url,
                  isFullScreen: false,
                  mediaData: item
                });
                break;
              }
              case 2: {
                this.props.navigation.push('VideoPreView', {mediaData: item});
                break;
              }
            }
          }
        }}>
        <IMIImageView style={styles.itemIcon} source={{uri: showUrl}} />
        {mediaType === 2 ? (
          <XImage
            raw={true}
            style={{position: 'absolute', top: 20, right: 5, width: 20, height: 20}}
            icon={require('../../resources/images/icon_play.png')}
          />
        ) : null}
        {this.state.isSelectMode ? (
          <XImage
            raw={true}
            style={{position: 'absolute', bottom: 5, right: 5, width: 20, height: 20}}
            icon={
              select
                ? require('../../resources/images/icon_select_s.png')
                : require('../../resources/images/icon_select_not_choose.png')
            }
          />
        ) : null}
      </XView>
    );
  };
  _renderDeleteView() {
    if (this.state.isSelectMode) {
      // let enable = this._getSelectCount().selectCount > 0;
      // return(<XView raw={true} style={{bottom:0,height:64,width:'100%',backgroundColor:'#FFFFFF',flexDirection: 'column'}}>
      //     <View style={{height:2,backgroundColor: "#F1F1F1"}} />
      //     <XView raw={true} style = {{height:62,width:'100%', backgroundColor:'#FFFFFF',alignItems: 'center',justifyContent: 'center',flexDirection: 'row'}}>
      //         <XText raw={true} style={{fontSize:12,color: !enable ? "#C1C1C1":'#E74D4D'}} iconStyle={!enable ? {tintColor: "#C1C1C1"} : null} iconSize={30} icon={require('../../resources/images/play_back_delete.png')} text={stringsTo('delete_title')} onPress={()=>{
      //             if (enable){
      //                 this.setState({showDeleteTip: true});
      //             }else {
      //                 showToast(stringsTo('select_tip'));
      //             }
      //         }}/>
      //     </XView>
      // </XView>)

      return (
        <View style={{bottom: 0, height: 64, width: '100%', backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
          <View style={{height: 2, backgroundColor: '#F1F1F1'}} />
          <View
            style={{
              height: 62,
              width: '100%',
              backgroundColor: '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <TouchableOpacity
              onPress={() => {
                if (this._getSelectCount().selectCount > 0) {
                  this.setState({showDeleteTip: true});
                } else {
                  showToast(stringsTo('select_tip'));
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center'}}
              accessibilityLabel={'img_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../resources/images/play_back_delete.png')} />
              <Text
                style={{fontSize: 16, color: '#E74D4D'}} //设计稿改动
              >
                {stringsTo('delete_title')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }

  static getFileNameFormPath(absolutePath) {
    return absolutePath.substr(absolutePath.lastIndexOf('/') + 1);
  }

  static getFileNameFormTimes(time, type) {
    let timeStr = moment(new Date(time * 1000)).format('yyyyMMDD_HHmmss') ;
    let fileName = `PIC_IMI_${timeStr}.jpg`;
    if (type === 2) {
      fileName = `VIDEO_IMI_${timeStr}.mp4`;
    }
    return fileName;
  }
}

const styles = StyleSheet.create({
  listViewStyle: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingBottom: 40
    // justifyContent: "space-around",
  },
  itemParent: {
    // marginLeft: isAndroid()? 10: 15,
    // marginTop: 15,
    // width: (windowWidth - 4*15) / 3+15 ,
    // height: ((windowWidth - 4*15) / 3 )*0.56,
    // justifyContent:'center',

    // alignItems:'center',
    paddingLeft: isAndroid() ? 14 : 12,
    paddingTop: 14,
  },
  itemIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 3,
    height: ((windowWidth - 4 * 14) / 3) * 0.56,
  },
});
