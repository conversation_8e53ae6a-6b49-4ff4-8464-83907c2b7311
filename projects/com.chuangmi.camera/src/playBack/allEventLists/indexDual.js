import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Dimensions, Image, BackHandler, Text, TouchableHighlight, SectionList, RefreshControl} from 'react-native';
import {XText, XView, XFlatList, XImage} from 'react-native-easy-app';
import IMIFile from '../../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import IMIPermission from '../../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import IMIToast from '../../../../../imilab-design-ui/src/widgets/IMIToast';
import {colors, IMIImageView, showLoading, showToast, imiThemeManager} from '../../../../../imilab-design-ui';
import {aliAlarmEventCloudApi, LetDevice, LetIProperties} from '../../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../../globalization/Localize';
import IMP2pClient from '../../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import {Calendar} from 'react-native-calendars';
import NavigationBar from '../../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ImageButton from '../../../../../imi-rn-commonView/ImageButton/ImageButton';
import ModalView from '../../../../../imi-rn-commonView/ModalView/ModalView';
import IMILog from '../../../../../imilab-rn-sdk/native/local-kit/IMILog';
import moment from 'moment';
import {
    byteArrayToInt4,
    byteArrayToLong8,
  } from '../../utils/GenericUtils';
import { getAllBackListDetail } from '../dataUtils'

import ChoiceItemNew from "../../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItemNew";
import MessageDialog from "../../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog";
import CalendarShow from '../../cloudStorage/CalendarShow';

const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
let windowHeight = height > width ? height : width;
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const playbackSelectOptions = [
    {key: 'key_dual', label: stringsTo('playback_view_select_dual'), icon: require('../../../resources/images/playback/icon_double_camera_black.png'), icon_select: require('../../../resources/images/playback/icon_double_camera_primary.png'), value: 2},
    {key: 'key_qiang', label: stringsTo('playback_view_select_qiang'), icon: require('../../../resources/images/playback/icon_fixed_camera_black.png'), icon_select: require('../../../resources/images/playback/icon_fixed_camera_primary.png'), value: 0},
    {key: 'key_qiu', label: stringsTo('playback_view_select_qiu'), icon: require('../../../resources/images/playback/icon_ptz_camera_black.png'), icon_select: require('../../../resources/images/playback/icon_ptz_camera_primary.png'), value: 1},
];
export class PlayBackDualAllList extends React.Component {

  constructor(props) {
    super(props);
    const {allDate, newData, needCheck, timeList, index, dateTime, localPicList} = this.dataComputed();
    this.state = {
      allDate: allDate,
      dataListAll: newData,
      dataList: newData.slice(0, index),
      isShowCalendar: false,
      dateTime: '',
      timeList: timeList,
      timeListData: [],
      timeListCameraId: [],
      isEdit: false,
      numAry: [],
      isSelectedAll: false,
      showDeleteTip: false,
      dateTimeDotSelect: dateTime,
      sdCardStatus: -1,
      storageAllCapacity: 0,
      storageUsedCapacity: 0,
      isShowDualSelect: false,
      dualSelectOption: 2,
    };
    this.needCheck = needCheck;
    this.localPic = localPicList;
    this.onP2pSendStateListener = null;
    this.areadyClick = false;
    this.currentIndex = 0;
    this.lastIndex = 0;
    this.isForegroundPage = true;
    this.downloadFlag = false;
    this.firstLoad = true;
    this.currentImgLength = 0;
    this.goNext = false;
  }

  checkDateStatus = (inputDate) => {
    const today = moment().startOf("day"); // 获取今天的开始时间
    const yesterday = moment().subtract(1, "days").startOf("day"); // 获取昨天的开始时间
    const inputMoment = moment(inputDate).startOf("day");

    if (inputMoment.isSame(today)) {
      return " | " + stringsTo('todayTitle');
    } else if (inputMoment.isSame(yesterday)) {
      return " | " + stringsTo('preDayTitle');
    } else {
      return "";
    }
  }

  dataComputed() {
    const dataList = getAllBackListDetail();
    const newData = [];
    const needCheck = [];
    const localPicList = {};
    const timeList = {};
    const allDate = [];
    const dateTime = {}
    Object.keys(dataList).map(res => {
      const children = []
      Object.keys(dataList[res]).map(res1 => {
        let camera0List = [];
        let camera1List = [];
        dataList[res][res1].forEach((item, index) => {
          if (item.camera_id === 0) {
            camera0List.push(item);
          } else {
            camera1List.push(item);
          }
        });
        if (camera0List.length > 0) {
          children.push({
            date: res,
            time: res1,
            children: camera0List,
            timestamp: camera0List[camera0List.length - 1].timestamp,
            cameraId: 0,
          });
        }
        if (camera1List.length > 0) {
          children.push({
            date: res,
            time: res1,
            children: camera1List,
            timestamp: camera1List[camera1List.length - 1].timestamp,
            cameraId: 1,
          });
        }
        if (camera0List.length > 0) {
          const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera0List[camera0List.length - 1].camera_id}_${camera0List[camera0List.length - 1].timestamp}.jpg`
          needCheck.push(saveFilePath);
          localPicList[camera0List[camera0List.length - 1].timestamp] = camera0List[camera0List.length - 1].pic_loc
          timeList[camera0List[camera0List.length - 1].timestamp] = saveFilePath
        }
        if (camera1List.length > 0) {
          const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera1List[camera1List.length - 1].camera_id}_${camera1List[camera1List.length - 1].timestamp}.jpg`
          needCheck.push(saveFilePath);
          localPicList[camera1List[camera1List.length - 1].timestamp] = camera1List[camera1List.length - 1].pic_loc
          timeList[camera1List[camera1List.length - 1].timestamp] = saveFilePath
        }
      });
      allDate.push(res);
      dateTime[res] = {
        marked: true,
        selectedColor: imiThemeManager.theme.primaryColor,
      };
      newData.push({
        date: res,
        children: children.sort((a, b) => {
          return b.timestamp - a.timestamp
        }),
        title: res,
        data: children.sort((a, b) => {
          return b.timestamp - a.timestamp
        }),
      })
    })
    let length = 0;
    let index = newData.length;
    let isFirst = true;
    let maxLenth = 3 * (windowHeight / ((windowWidth - 4 * 14) / 3 / 16 * 9));
    maxLenth < 40 ? maxLenth = 40 : maxLenth
    newData.map((res, index1) => {
      length += res.children.length;
      if (length > maxLenth && isFirst) {
        index = index1;
        isFirst = false;
      }
    })
    IMILog.logI('获取的图片地址', JSON.stringify(timeList));
    return {
      allDate, newData, needCheck, timeList, index, dateTime, localPicList
    }
  }

  componentDidMount() {
    this.firstLoad = true;
    // this.refreshList && this.refreshList.refreshPreLoad(false);
    // this.refreshList && this.refreshList.refreshLoaded(true, false, this.state.dataList.length === this.state.dataListAll.length, false);
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', this.backAction);
    this.loadImg(this.needCheck);
    this.currentIndex = 0;
    this.lastIndex = 0;
    this.getSdStatus()

    this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
      if (!this.isForegroundPage) {
        return
      }
      if (e.iotId != LetDevice.deviceID) {
        return
      }
      this.waitingData && clearInterval(this.waitingData);
      if (e.code === 0 && e.data) {
        this.queryThreeMonthData(e.data)
      }
    });

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.isForegroundPage = true;
      this.areadyClick = false
      this.downloadFlag = false;
      if (this.firstLoad && !this.goNext) {
        return
      }
      this.setState({
        refreshing: true
      })
      this.getSdStatus()
      this.goNext = false;
      const {allDate, newData, needCheck, timeList, index, localPicList, dateTime} = this.dataComputed();
      this.setState({
        allDate: allDate,
        dataListAll: newData,
        dataList: newData.slice(0, index),
        timeList: timeList,
        dateTimeDotSelect: dateTime,
      })
      // this.refreshList && this.refreshList.refreshLoaded(true, false, index === newData.length, false);
      this.needCheck = needCheck;
      this.localPic = localPicList;
      this.loadImg(this.needCheck);
      setTimeout(() => {
        this.setState({
          refreshing: false
        })
      }, 200)
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      this.isForegroundPage = false;
      this.waitingData && clearInterval(this.waitingData);
      this.setState({
        allDate: [],
        dataListAll: [],
        dataList: [],
      })
    });

    //监听Action获取SD卡总容量、已用容量、可循环录制的时长（由属性改为Action非常不合理）
    this.deviceActionListener = LetIProperties.addActionResultListener(result => {
      try {
        let item = JSON.parse(result);
        let stateProps = {};
        if (`${item?.thingid}` === `${10016}`) {
          if (isNaN(item?.value?.value)) {
            return;
          }
          stateProps.storageUsedCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
        }
        if (`${item?.thingid}` === `${10014}`) {
          if (isNaN(item?.value?.value)) {
            return;
          }
          stateProps.storageAllCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
        }
        this.setState(stateProps);
      } catch (error) {
        // 处理错误情况
        console.error("JSON解析失败:", error, result);
      }

    });
  }

  getSdStatus() {
    Promise.all([
      LetDevice.sendAction(true, LetDevice.deviceID, '10016', JSON.stringify({msg_id: 10016})),
      LetDevice.getSingleProperty('10013'),
      LetDevice.sendAction(true, LetDevice.deviceID, '10014', JSON.stringify({msg_id: 10014}))
    ])
      .then(data => {
        let sdCardStatus = -1;
        let storageUsedCapacity = 0;
        let storageAllCapacity = 0;
        data?.forEach(item => {
          if (`${item?.thingId}` === `${10013}`) {
            // 0代表有卡，1没卡
            sdCardStatus = item?.value?.value;
          }

          //下面的两个属性改为动作了，从监听中获取数据，很不合理
          /* if (`${item?.thingId}` === `${10016}`) {
             if (isNaN(item?.value?.value)) {
               return;
             }
             storageUsedCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
           }
           if (`${item?.thingId}` === `${10014}`) {
             if (isNaN(item?.value?.value)) {
               return;
             }
             storageAllCapacity = (item?.value?.value / 1024 / 1024).toFixed(2);
           }*/
          this.setState({
            sdCardStatus,
            /*storageUsedCapacity,
            storageAllCapacity*/
          });
        });
      })
      .catch(() => {
      });
  }

  loadImg(needCheck) {
    IMP2pClient.checkPlaybackFile(needCheck).then(res => {
      const lists = [];
      const listCameraId = [];
      const timeList = this.state.timeList;
      Array.isArray(res) && res.map(item => {
        const timestamp = item.split('_').slice(-1)[0].slice(0, -4)
        lists.push(timestamp)
        const cameraId = item.split('/').pop().split('_')[0]
        listCameraId.push(cameraId)
        delete timeList[timestamp]
      })
      this.setState({
        timeListData: lists,
        timeListCameraId: listCameraId,
      }, () => {
        lists.length > 0 && this.sendData(0);
      })
    })
  }

  sendData(index) {
    this.currentImgLength = 0;
    this.state.timeListData[index] && IMP2pClient.operationFile([String(this.state.timeListCameraId[index])], ['3'], [[String(this.state.timeListData[index])]], [[String(this.localPic[this.state.timeListData[index]])]]);
    // 十秒后未获取到数据就重发
    this.waitingData = setInterval(() => {
      if (!this.isForegroundPage) {
        this.waitingData && clearInterval(this.waitingData);
        return
      }
      this.currentIndex++
      this.currentImgLength = 0;
      this.state.timeListData[this.currentIndex] && IMP2pClient.operationFile([String(this.state.timeListCameraId[this.currentIndex])], ['3'], [[String(this.state.timeListData[this.currentIndex])]], [[String(this.localPic[this.state.timeListData[this.currentIndex]])]]);
    }, 10000)
  }

  uint8ArrayToBase64(uint8Array) {
    const rawData = new Uint8Array(uint8Array);
    const base64String = btoa(String.fromCharCode.apply(null, rawData));
    return base64String;
  }

  queryThreeMonthData(data) {
    // console.log(data)
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
      uint8Arraypart[i] = rawData.charCodeAt(i);
    }

    if (byteArrayToInt4(uint8Arraypart, 4) == 1) {
      // 不处理下载包
      // this.downloadTime && clearTimeout(this.downloadTime);
      // this.downloadFlag = false;
      // this.downloadVideo(uint8Arraypart)
      // return;
    }

    if (byteArrayToInt4(uint8Arraypart, 4) !== 3) {
      return;
    }
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToLong8(uint8Array, 47); // 开始时间utc , 单位s
    const total_file_len = byteArrayToInt4(uint8Array, 55);
    // console.log('111111111111=======', timestamp, this.currentIndex)
    this.currentIndex = this.currentIndex + 1;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera_id}_${timestamp}.jpg`;
    // console.log(uint8Array.length === cur_pack_len)
    let imgFinish = false;
    this.currentImgLength += uint8Array.slice(84).length;
    if (this.currentImgLength >= total_file_len) {
      imgFinish = true
    }
    IMP2pClient.downloadPlaybackImage(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, imgFinish ? '0' : '-1').then(res => {
      const timeList = this.state.timeList || {};
      timeList[timestamp] = res;
      // const timeListData = this.state.timeListData;
      // timeListData.splice(timeListData.indexOf(timestamp), 1)
      this.setState({
        timeList
      })
    }).catch(e => {
      console.log(11, e);
    });
    console.log(this.state.timeListData[this.currentIndex])
    if (this.currentIndex < this.state.timeListData.length) {
      this.sendData(this.currentIndex)
    }
  }

  componentWillUnmount() {
    this.onP2pSendStateListener && this.onP2pSendStateListener.remove();
    this.waitingData && clearInterval(this.waitingData);
    this._subscribe_blur && this._subscribe_blur();
    this.backHandler && this.backHandler.remove();
    this.deviceActionListener && this.deviceActionListener.remove();
  }

  /**
   * 获取当前数据           **  可以进行子类重写 **
   * @param isPullDown
   * @private
   */
  _queryDataList = () => {
    const {allDate, newData, needCheck, timeList, index, localPicList, dateTime} = this.dataComputed();
    this.setState({
      allDate: allDate,
      dataListAll: newData,
      dataList: newData.slice(0, index),
      timeList: timeList,
      dateTime: '',
      dateTimeDotSelect: dateTime,
    })
    this.needCheck = needCheck;
    this.localPic = localPicList;
    this.loadImg(this.needCheck);
    this.setState({
      refreshing: false,
    })
  };
  // 下载
  _downloadPress = () => {
    //需要检测权限
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            showLoading(stringsTo('alarm_download_downloading'), true);
            this.downloadFlag = true;
            IMP2pClient.operationFile(['0'], ['1'], [[String(this.state.numAry[0])]], [[String(this.localPic[this.state.numAry[0]])]]);
            this.downloadTime = setTimeout(() => {
              this.downloadFlag = false;
              IMIToast.showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
              showLoading(false);
            }, 10000)
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
      }
    });
  };

  downloadVideo(uint8Arraypart) {
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToLong8(uint8Array, 47); // 开始时间utc , 单位s
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera_id}_${timestamp}.mp4`;
    console.log('===========', cur_pack_len, uint8Array.length === cur_pack_len)
    // this.downloadVideo = uint8Array.slice(84);
    // IMP2pClient.downloadPlaybackFile(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath).then(res => {
    //   // 推送到相册
    //   IMIFile.saveVideoToPhotosAlbum(`${saveFilePath}/concat.mp4`, LetDevice.devNickName)
    //     .then(_ => {
    //       IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
    //       showLoading(false);
    //     })
    //     .catch(error => {
    //       showToast(stringsTo('video_download_fail'));
    //       showLoading(false);
    //     });
    // }).catch(e => {
    //     console.log(11, e);
    // });
  }

  getDownloadUrl() {
    showLoading(stringsTo('alarm_download_downloading'), true);
    this.intervalTimer && clearInterval(this.intervalTimer);
    const saveFileName = `VIDEO_IMI_${new Date().getTime()}`;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${saveFileName}.mp4`;
    const {segmentId, publicKeyVersion} = this.state.currentSegmentId;
    aliAlarmEventCloudApi
      .downloadVideo(LetDevice.deviceID, segmentId, publicKeyVersion, saveFilePath, saveFileName)
      .then(_ => {
        // 推送到相册
        IMIFile.saveVideoToPhotosAlbum(`${saveFilePath}/concat.mp4`, LetDevice.devNickName)
          .then(_ => {
            IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
            showLoading(false);
          })
          .catch(error => {
            showToast(stringsTo('video_download_fail'));
            showLoading(false);
          });
      })
      .catch(error => {
        showToast(stringsTo('video_download_fail'));
        showLoading(false);
      });
  }

  // 返回
  _onPressBack = () => {
    if (this.state.isFullScreen) {
      this._exitFullScreen();
    } else {
      this.props.navigation.goBack();
    }
    return true;
  };

  backAction = () => {
    if (this.downloadFlag) {
      return true
    }
    this._onPressBack()
    return true
  }

  loadMore = () => {
    if (this.state.dataList.length < this.state.dataListAll.length) {
      const start = this.state.dataList.length;
      const end = start + 2 < this.state.dataListAll.length ? start + 2 : this.state.dataListAll.length
      this.setState({
        dataList: this.state.dataListAll.slice(0, end)
      })
      // this.refreshList && this.refreshList.refreshLoaded(true, false, end === this.state.dataListAll.length, false);
    }
  }

  // SD卡状态
  _renderSdStatus() {
    const status = {
      0: stringsTo('sdcard_status0'),
      1: stringsTo('sdcard_status1'),
      2: stringsTo('sdcard_status6'),
      3: stringsTo('sdcard_status3'),
      4: stringsTo('sdcard_status4'),
      5: stringsTo('sdcard_status5'),
      6: stringsTo('sdcard_status9'),
      7: stringsTo('sdcard_status10'),
      8: stringsTo('sdcard_status7'),
      9: stringsTo('sdcard_status2'),
    }
    return <TouchableHighlight onPress={this.handleSdCard.bind(this)} underlayColor="transparent">
      <View style={{
        margin: 14,
        paddingLeft: 10,
        paddingTop: 16,
        paddingBottom: 16,
        backgroundColor: '#f1f3f5',
        borderRadius: 8,
      }}>
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
          <View>
            <Text style={{
              fontSize: 14,
              color: "#000",
              marginBottom: 4,
              fontWeight: 'bold'
            }}>{stringsTo('sdcard_status_normal_new1')}: {status[this.state.sdCardStatus]}</Text>
            {this.state.sdCardStatus === 0 && <View>
              <Text
                style={{fontSize: 12}}>{I18n.t('sdCardRemain', {code: this.state.storageUsedCapacity})}, {I18n.t('sdCardTotal', {code: this.state.storageAllCapacity})}</Text>
            </View>}
          </View>
          <Image style={{width: 35, height: 35}} source={require('../../../resources/images/right_arrow.png')} />
        </View>
        {this.state.sdCardStatus === 0 && <View style={styles.progressContainer}>
          <View style={styles.outerBar}>
            <View
              style={[styles.innerBar, {width: `${(((this.state.storageUsedCapacity / this.state.storageAllCapacity) || 0) * 100)}%`}]} />
          </View>
        </View>}
      </View>
    </TouchableHighlight>
  }

  // 全部视频
  _renderTitle() {
    return <View style={{
      paddingLeft: 16,
      paddingTop: 16,
      paddingBottom: 6,
      paddingRight: 16,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <Text style={{fontSize: this.state.dateTime ? 14 : 20, color: '#000', fontWeight: '700'}}>
        {
          this.state.dateTime ? this.state.dateTime + this.checkDateStatus(this.state.dateTime)
            : stringsTo('play_back_text_all_title')
        }
      </Text>
      <View style={{flexDirection: 'row'}}>
        <ImageButton
          onPress={() => {
            this.setState({isShowDualSelect: !this.state.isShowDualSelect})
          }}
          style={{width: 30, height: 30, marginLeft: 10}}
          source={require('../../../resources/images/houseKeepingV2/filter_dual.png')}
          accessibilityLabel={'back_filter_dual'}
        />
        <ImageButton
          onPress={() => {
            this.setState({isShowCalendar: !this.state.isShowCalendar})
          }}
          style={{width: 30, height: 30}}
          source={require('../../../resources/images/houseKeepingV2/filter.png')}
          accessibilityLabel={'back_calendar'}
        />
      </View>
    </View>
  }


  handleSdCard() {
    this.goNext = true;
    this.props.navigation.push('SdCardNewPage');
  }

  groupData = (data, itemsPerRow) => {
    const groupedData = [];
    for (let i = 0; i < data.length; i += itemsPerRow) {
      groupedData.push(data.slice(i, i + itemsPerRow));
    }
    return groupedData;
  };

  _renderListView() {
    const dataListTemp = this.state.dateTime ? (!this.state.allDate.includes(this.state.dateTime) ? [{
      title: this.state.dateTime,
      data: [{}]
    }] : this.state.dataListAll.filter(res => {
      return res.date === this.state.dateTime
    })) : this.state.dataList;

    // 修改过滤逻辑以适应新的数据结构
    const dataList = this.state.dualSelectOption !== 2 ? dataListTemp.map(section => {
      // 对每个 section 的 children 进行过滤
      const filteredChildren = section.children.filter(child => {
        return child.cameraId === this.state.dualSelectOption;
      });

      // 返回新的 section 对象，包含过滤后的 children
      return {
        ...section,
        children: filteredChildren,
        data: filteredChildren
      };
    }).filter(section => section.children.length > 0) : dataListTemp;

    if (!this.isForegroundPage) {
      return null
    }

    if (this.state.allDate.length == 0) {
      return <View style={{...styles.itemContent, borderBottomWidth: 0}}>
        <View style={{height: 200, justifyContent: 'center', alignItems: 'center'}}>
          <Text>{stringsTo('housekeeping_no_event')}</Text>
        </View>
      </View>
    }


    return (
      <SectionList
        contentContainerStyle={{paddingBottom: 30}}
        sections={dataList.map((section) => ({
          ...section,
          data: this.groupData(section.data, 3), // 将数据分组
        }))}
        keyExtractor={(item, index) => item[0].date + item[0].time}
        stickySectionHeadersEnabled={true}
        refreshControl={
          <RefreshControl refreshing={this.state.refreshing} onRefresh={() => this._queryDataList()} /> // 下拉刷新
        }
        onEndReached={() => {
          // this.refreshList && this.refreshList.refreshPreLoad(false);
          this.loadMore()
        }} // 上拉加载更多
        onEndReachedThreshold={0.1} // 距离底部 50% 时触发
        renderSectionHeader={({section: {title}}) => {
          return this.state.dateTime ? null : <View style={styles.header}>
            <XText style={styles.itemTitle} text={title + this.checkDateStatus(title)}></XText>
          </View>
        }}
        renderItem={({item, index}) => {
          if (this.state.dateTime && !this.state.allDate.includes(this.state.dateTime)) {
            return <View style={{...styles.itemContent, borderBottomWidth: 0}}>
              <View style={{height: 200, justifyContent: 'center', alignItems: 'center'}}>
                <Text>{stringsTo('housekeeping_no_event')}</Text>
              </View>
            </View>
          }
          return (
            item[0].date === this.state.dateTime || !this.state.dateTime ?
              <View style={{flex: 1, flexDirection: 'row', flexWrap: 'wrap', gap: 14, paddingLeft: 14}}>
                {item.map(res => {
                  return this._renderItem(res, index, {date: item[0].date})
                })}
              </View> : null
          )
        }}
      />
    );
  }

  handleCurrent = (item, parent) => {
    if (this.state.isEdit) {
      const numAry = this.state.numAry;
      if (this.state.numAry.includes(item.timestamp)) {
        numAry.splice(this.state.numAry.indexOf(item.timestamp), 1)
      } else {
        numAry.push(item.timestamp)
      }
      this.setState({
        numAry
      })
      return
    }
    if (this.areadyClick) {
      return
    }
    this.areadyClick = true;
    this.firstLoad = false
    this.props.navigation.push('BackTimeListsDual', {
      firstKey: parent.date,
      secondKey: item.time,
      cameraId: item.cameraId,
    });
  }

  _renderItem = (item, index, parent) => {
    let isSelected = false;
    if (this.state.numAry.length > 0) {
      isSelected = this.state.numAry.includes(item.timestamp)
    } else {
      isSelected = this.state.isSelectedAll;
    }

    return (
      <XView
        key={parent.date + item.time + item.timestamp + item.cameraId}
        style={styles.itemParent}
        onPress={() => this.handleCurrent(item, parent)}
        accessibilityLabel={'back_item_view_' + item.time}>
        {/* <PathImage item={item} deviceID={LetDevice.deviceID} timeList={this.state.timeList} /> */}
        {<IMIImageView style={styles.itemIcon} source={{uri: `file://${this.state.timeList[item.timestamp]}`}} />}
        <View style={{display: 'flex', flexDirection: 'row', alignItems: 'center'}}>
          <Image style={styles.itemSmallIcon}
                 source={item.children[0].camera_id === 0 ?
                   require('../../../resources/images/playback/icon_qiang_small.png')
                   : require('../../../resources/images/playback/icon_qiu_small.png')} />
          <XText style={styles.itemTime} text={item.time} />
        </View>
        {this.state.isEdit ? (
          <XImage
            raw={true}
            style={{position: 'absolute', bottom: 20, right: 5, width: 20, height: 20}}
            icon={
              isSelected
                ? require('../../../resources/images/icon_select_s.png')
                : require('../../../resources/images/icon_select.png')
            }
          />
        ) : null}
      </XView>
    );
  };

  /**
   * 日历控件逻辑
   * @returns {*}
   * @constructor
   */
  getCalendarView() {
    // console.log(
    //   getDay(-30),
    //   calculateDaysDifference(new Date(), new Date(this.lastBackDay * 1000)),
    //   'new Date().toDateString()',
    // );
    // const currentDate = moment();
    // 往前推 30 天
    // const pastDate = currentDate.subtract(30, 'days');
    return (
      <ModalView
        style={[{justifyContent: 'center'}, this.state.isFullScreen ? {alignItems: 'center'} : {}]}
        visible={this.state.isShowCalendar}
        stylePosition={{bottom: 0}}
        contentIn={true}
        onClose={() => {
          this.setState({isShowCalendar: false});
        }}>
        <CalendarShow
          // minDate={pastDate.format('YYYY-MM-DD')}
          noControl={true}
          style={{width: '100%'}}
          onDayPress={day => {
            // if (!this.state.allDate.includes(day.dateString)) {
            //   IMIToast.showToast(stringsTo('noData'), IMIToast.TYPE.BOTTOM);
            //   return
            // }
            // this.refreshList && this.refreshList.refreshPreLoad(true);
            // this.refreshList && this.refreshList.refreshLoaded(true, true, true, false);
            this.setState({
              dateTime: day.dateString,
              isShowCalendar: false,
            })

          }}
          maxDate={new Date().toDateString()} //控制台报错接收是string，原来传入的是date
          onVisibleMonthsChange={month => {
            console.log('=', month)
          }}
          current={this.state.dateTime} //设置选中时间
          dateTimeDotSelect={{
            ...this.state.dateTimeDotSelect,
            [this.state.dateTime]: {
              ...this.state.dateTimeDotSelect[this.state.dateTime],
              selected: true,
              disableTouchEvent: true,
              selectedColor: imiThemeManager.theme.primaryColor,
            }
          }}
          theme={{
            arrowColor: '#000000', //左右箭头的颜色
            todayTextColor: imiThemeManager.theme.primaryColor,
            textMonthFontWeight: 'bold', //标题yyyy-MM的字重
            //textDayHeaderFontWeight: 'bold',//周几的字重 20220211@byh去掉字重及周文字大小减小1号，防止有些机型文字显示不全
            textSectionTitleColor: '#000000', //周几的颜色
            textDayHeaderFontSize: 12, //周大小 字体设置 英文会突出=
            // dotColor: 'red'
          }}
        />
      </ModalView>
    );
  }

  _renderDualSelectView() {
    return (
      <MessageDialog
        visible={this.state.isShowDualSelect}
        title={stringsTo('playback_view_select_title')}
        showButton={false}
        canDismiss={true}
        onDismiss={() => this.setState({isShowDualSelect: false})}>
        <View style={styles.dialogContent}>
          {playbackSelectOptions.map(option => {
            const checked = this.state.dualSelectOption === option.value;
            return (
              <ChoiceItemNew
                key={option.key}
                title={option.label}
                headIcon={checked ? option.icon_select : option.icon}
                backgroundColor={checked ? 'rgba(50,186,192,0.1)' : '#FFFFFF'}
                titleColor={checked ? '#12AA9C' : '#000000'}
                selectIcon={require('../../../resources/images/icon_select_s.png')}
                unselectIcon={'blank'}
                containerStyle={{paddingTop: 15, paddingBottom: 15, borderRadius: 0}}
                checked={checked}
                onValueChange={() => {
                  this.setState({isShowDualSelect: false})
                }}
                onPress={() => {
                  this.setState({dualSelectOption: option.value});
                }}
              />
            );
          })}
        </View>
      </MessageDialog>
    );
  }

  handleSelect() {
    this.setState({isEdit: true, numAry: [], isSelectedAll: false});
  }

  _renderEditView() {
    if (this.state.isEdit && this.state.numAry && this.state.numAry.length > 0) {
      return (
        <View style={{
          position: 'absolute',
          bottom: 0,
          height: 64,
          width: '100%',
          backgroundColor: '#FFFFFF',
          flexDirection: 'column'
        }}>
          <View style={{height: 2, backgroundColor: '#F1F1F1'}} />
          <View
            style={{
              height: 62,
              width: '100%',
              backgroundColor: '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length === 1) {
                  this._downloadPress()
                } else {
                  showToast(stringsTo('max_download_limit'))
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center', marginRight: 40}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}}
                     source={require('../../../resources/images/icon_alarm_down.png')} />
              <Text
                style={{fontSize: 16}} //设计稿改动
              >
                {stringsTo('downLoadTitle')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length > 0) {
                  this.setState({showDeleteTip: true});
                } else {
                  showToast(stringsTo('select_tip'));
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center'}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}}
                     source={require('../../../resources/images/play_back_delete.png')} />
              <Text
                style={{fontSize: 16, color: '#E74D4D'}} //设计稿改动
              >
                {stringsTo('delete_title')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }

  deletImageArray() {
    console.log(this.state.numAry.length)
  }

  render() {
    console.log(this.state.timeListData)
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        pointerEvents="box-none"
        style={{flex: 1, backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={this.state.isEdit ? I18n.t('select_title_3', {code: this.state.numAry.length}) : stringsTo('sdCardName')}
          left={!this.state.isEdit ? [
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'housekeeping_back',
            },
          ] : [{
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../../resources/images/houseKeepingV2/icon_angel_del.png'),
            onPress: () => {
              this.setState({isEdit: false, numAry: [], isSelectedAll: false});
            },
            accessibilityLabel: 'housekeeping_back',
          }]}
          right={!this.state.isEdit ? [
            // {
            //     key: NavigationBar.ICON.CUSTOM,
            //     // n_source:
            //     //   this.state.length == 0
            //     //     ? require('../../../resources/images/icon_edit_disabled.png')
            //     //     : require('../../../resources/images/icon_edit.png'),
            //     n_source:require('../../../resources/images/icon_edit.png'),
            //     onPress: _ => {
            //         this.setState({isEdit: true, numAry: [], isSelectedAll: false});
            //     },
            //     accessibilityLabel: 'back_editor',
            //   },
          ] : [{
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../../resources/images/houseKeepingV2/icon_angel_allSelect.png'),
            onPress: _ => {
              this.setState({
                isSelectedAll: !this.state.isSelectedAll,
                numAry: this.state.isSelectedAll ? [] : this.state.dataList.map(res => {
                  return res.timestamp
                })
              });
            },
            accessibilityLabel: 'back_editor',
          }]}
        />
        {this._renderSdStatus()}
        {this._renderTitle()}
        {this.getCalendarView()}
        {this._renderDualSelectView()}
        {this._renderListView()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
    itemContent: {
        paddingBottom: 20,
        paddingTop: 20,
        borderBottomColor: colors.page_bg,
        borderBottomWidth: 1,
    },
    itemTitle: {
      fontSize: 12,
      paddingLeft: 14,
      color: colors.black,
      fontWeight: 'bold'
    },
    itemParent: {
      // paddingLeft: 14,
      paddingTop: 14,
      backgroundColor: colors.white,
      // width: (windowWidth - 4 * 14) / 3,
    },
    itemTime: {
      fontSize: 12,
      color: colors.gray,
      marginTop: 4,
      borderRadius: 10,
    },
    itemSmallIcon: {
        width: 15,
        height: 15,
        marginTop: 5
    },
    itemIcon: {
      borderRadius: 4,
      width: (windowWidth - 4 * 14) / 3,
      height: (windowWidth - 4 * 14) / 3 / 16 * 9,
    },
    progressContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 10,
      paddingRight: 10
    },
    outerBar: {
      height: 6,
      width: '100%',
      backgroundColor: '#e0e0e0',
      borderRadius: 5,
      overflow: 'hidden',
    },
    innerBar: {
      height: '100%',
      backgroundColor: imiThemeManager.theme.primaryColor,
      borderRadius: 5,
    },
    header: {
      backgroundColor: '#fff',
      paddingBottom: 10,
      paddingTop: 10
    },
    dialogContent: {
        paddingVertical: 10,
    },
});
  
