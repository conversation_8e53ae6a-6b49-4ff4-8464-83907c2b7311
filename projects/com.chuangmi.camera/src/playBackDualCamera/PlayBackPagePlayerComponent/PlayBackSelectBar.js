
import React, {Component} from 'react';

import {
    TouchableOpacity,
    Text,
    View,
    StyleSheet,
    TouchableWithoutFeedback,
    FlatList,
    Dimensions,
} from "react-native";

import {imiThemeManager, colors} from '../../../../../imilab-design-ui';

import {DateUtils} from '../../../../../imilab-rn-sdk';

import {CalendarList, LocaleConfig} from 'react-native-calendars';

import PropTypes from "prop-types";

import {XText} from 'react-native-easy-app';
import * as RNLocalize from "react-native-localize";
import {stringsTo} from "../../../../../globalization/Localize";
import Orientation from "react-native-orientation";

import CenterSelectBar from "../../../../com.chuangmi.camera.v2/src/alarmList/CenterSelectBar";
import DateHasManager from "../../../../com.chuangmi.camera.v2/src/utils/DateHasManager";

const {width,height} = Dimensions.get('window');
let SCREEN_WIDTH = height>width?width:height;

//写死的
export const locales = RNLocalize.getLocales()||[{languageCode:'zh'}];
if (!locales[0]) {
    locales[0] = {
      languageCode: 'zh',
    };
  }
const systemLanguage = locales[0].languageCode == 'zh'||locales[0].languageCode == 'zh-Hant' ? 'cn' : locales[0].languageCode;  // 用户系统偏好语言

LocaleConfig.locales[systemLanguage] = stringsTo("calendarLocaleConfig");
LocaleConfig.defaultLocale = systemLanguage;

export default class PlayBackSelectBar extends Component {

    constructor(props) {
        super(props);

        this.state = {
            /** 获取当前时间 **/
            dateTime: this.getDataFormatTime(),
            hideTitleSelect: this.props.hideTitleSelect,
            dayData: [],
            curDate: new Date(),
            modeType: props.modeType,
        };
        this.topSelectBarRoot = undefined;

        this.eventCurrentTitles = props.eventCurrentTitles;

        this.devCurrentTitles = props.devCurrentTitles;
        console.log("AlarmTopSelectBar", this.devCurrentTitles);

        this.currentIndex = 0;
        this.dotArr = {};
    }


    render() {
        let data = [
            // [this.getSelectListView.bind(this, this._onDevItemPress, this._getDevCurrentTitle)],
            [this.props.isSevenDay?this._renderDay.bind(this):this.getCalendarView.bind(this)],
            [this.getSelectEventListView.bind(this)],
        ];

        console.log("AlarmListPlayerPage render dateTime " + this.state.dateTime);
        console.log(`tabType${this.props.tabType}  modeType:${this.props.modeType}`)
        //事件选择是否展示
        let showEvent = true;
        // if (this.props.tabType===2 && this.props.modeType === 1){
        if (this.props.tabType===2){
            //SD卡，不显示
            showEvent = false;
        }else if (this.props.tabType === 0 && this.props.modeType === 1){
            //云存视频，并且是时间轴形式，不显示
            showEvent = false;
        }
        console.log(`showEvent:${showEvent}`)
        return (
            <CenterSelectBar
                ref={this._assignTopSelectBarRoot}
                style={{flex: 1}}
                bgColor={"white"}
                tintColor={colors.gray}
                recording={this.props.recording}
                activityTintColor={imiThemeManager.theme.primaryColor}
                // arrowImg={}
                // checkImage={}
                titleStyle={{color: imiThemeManager.theme.primaryColor}}
                // maxHeight={300}
                gridSelect={this.props.modeType == 0}
                handler={(selection, row) => {
                    console.log("----row-----" + data[selection][row]);
                    this.setState({firstName: data[selection][row]})
                }}
                modeChange={(mode)=>{
                    //网格时间轴切换后的回调
                    this.props.modeChange && this.props.modeChange(mode);
                }}
                hideTitleSelect={this.props.hideTitleSelect}
                editMode={this.props.editMode}
                showEvent={showEvent}
                showGridLine={this.props.showGridLine}
                externalSelectText={this.props.initDate}
                data={data}
            >
                {/*<View style={{backgroundColor: "black", height: 1}}/>*/}

                {this.props.renderView && this.props.renderView()}

            </CenterSelectBar>
        );
    }

    setSelectDate(datetime){
        this.setState({dateTime:datetime})
        let m = datetime.substring(5, 7);
        let d = datetime.substring(8);
        let dayTitle = stringsTo('day') != "日"?(m + "/" + d):`${m}${stringsTo('month')}${d}${stringsTo('day')}`;
        this.topSelectBarRoot && this.topSelectBarRoot.justSetTabText(dayTitle)
    }

    setSelectEvent(eventTx){
        this.topSelectBarRoot && this.topSelectBarRoot.justSetTabText(eventTx,1)
    }
    getDataFormatTime() {
        return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
    }

    _assignTopSelectBarRoot = (component) => {
        this.topSelectBarRoot = component;
    };

    /**
     *获取事件选择特殊布局
     * @returns {*}
     */
    getSelectEventListView() {

        let norStyle = styles.eventItem;

        let clickStyle = [styles.eventItem, {
            backgroundColor: '#EDF0FC',
            color: imiThemeManager.theme.primaryColor
        }];

        return (
            <View style={{
                padding: 7,
                flexWrap: 'wrap',
                flexDirection: 'row',
                justifyContent: 'space-between'
            }}>
                {
                    this.props.eventCurrentTitles.map((content, index) => (
                        <TouchableOpacity
                            key={index}
                            activeOpacity={1}
                            style={{margin: 7}}
                            onPress={this._onEventItemPress.bind(this, content, index)}>
                            <View style={this.currentIndex === index ? styles.selectedEventItemBg: styles.eventItemBg}>
                                {/*<Text style={this.currentIndex === index ? styles.selectedEventItem : styles.eventItem}*/}
                                {/*       text={this._getEventCurrentTitle(index)}/>*/}
                                <Text style={this.currentIndex === index ? styles.selectedEventItem : styles.eventItem}>{this._getEventCurrentTitle(index)}</Text>

                            </View>



                        </TouchableOpacity>
                    ))
                }

            </View>
        );
    };

    /**
     * 事件选择监听按下
     * @param currentTitles
     * @param index
     */
    _onEventItemPress(currentTitles, index) {
        this.currentIndex = index;

        this.topSelectBarRoot.funOnPress(this._getEventCurrentTitle(index));

        this.props.onEventItemPress && this.props.onEventItemPress(this.props.eventCurrentTitles, index);
    }


    _getEventCurrentTitle(index) {
        return this.props.eventCurrentTitles[index][0];
    }

    /**
     * 方式选择监听按下
     * @param currentTitles
     * @param index
     */
    _onDevItemPress = (currentTitles, index) => {
        this.currentIndex = index;

        this.topSelectBarRoot.funOnPress(this._getDevCurrentTitle(index));

        this.props.onDevItemPress && this.props.onDevItemPress(this.props.onDevItemPress, index);
    };


    _getDevCurrentTitle = (index) => {
        console.log('_getDevCurrentTitle', this.props);
        return this.props.devCurrentTitles[index][0];
    };


    /***
     * 选择日期按钮按下
     * @private
     */
    _onDayPress() {
        console.log('_onDayPress',this.state.dateTime);

        let m = this.state.dateTime.substring(5, 7);
        let d = this.state.dateTime.substring(8);
        let dayTitle = stringsTo('day') != "日"?(m + "/" + d):`${m}${stringsTo('month')}${d}${stringsTo('day')}`;
        this.topSelectBarRoot.funOnPress(dayTitle);

        this.props.onDayPress && this.props.onDayPress(this.state.dateTime)
    }

    /**
     * 日历控件逻辑
     * @returns {*}
     * @constructor
     */
    getCalendarView() {
        console.log('this.props.dateTimeDotSelect', this.props.dateTimeDotSelect);
        this.dotArr = DateHasManager.sdDateList;
        console.log("getCalendarView",this.dotArr,this.state.dateTime);

        return (
            <CalendarList
        monthFormat={'yyyy' + stringsTo('year') + 'MM' + stringsTo('month')}
                // maxDate={new Date().toDateString()}
                // minDate={this.props.minDate?this.props.minDate:null}
                horizontal={true}
                // style = {{marginHorizontal:25,marginTop:64}}
                // width = {Dimensions.get('window').width - 50}
                // current={this.state.dateTime}
                calendarWidth={SCREEN_WIDTH}
                disabledByDefault={true}
                onDayPress={(day) => {
                    console.log('AlarmListPlayerPage' + day.dateString);
                    //刷新完成后调用确保取值正确
                    console.log("dot arr:",this.dotArr);
                    if (this.dotArr.hasOwnProperty(day.dateString)){
                        this.setState({dateTime: day.dateString}, () => {
                            this._onDayPress();
                        });

                    }
                }}
                // scrollEnabled={true}
                pagingEnabled={true}
                disableMonthChange={true}
                displayLoadingIndicator={false}
                onVisibleMonthsChange={(month) => {
                    console.log('AlarmListPlayerPage onDayPress pressed formatDate', month);
                    this.props.onVisibleMonthsChange && this.props.onVisibleMonthsChange(month)
                }}
                current={this.props.current?new Date(this.props.current):this.state.dateTime}  //设置选中时间
                // 允许滚动到过去的最大月份数。Default = 50
                pastScrollRange={this.props.pastScrollRange != undefined ? this.props.pastScrollRange : 1}
                // 允许滚动到未来的最大月份数。默认值 = 50
                futureScrollRange={this.props.futureScrollRange != undefined ? this.props.futureScrollRange : 1}// 允许滚动到未来的最大月份数。Default = 50 0再选择上月后无法滑动到本月
                // markedDates={dotArr}
                markedDates={{
                    ...this.dotArr,
                    [this.state.dateTime]: {
                        selected: true,
                        marked: false,
                        dotColor: imiThemeManager.theme.pageBg,
                        disableTouchEvent: true,
                        selectedColor: imiThemeManager.theme.primaryColor
                    }
                }}
                theme={{
                    todayTextColor: imiThemeManager.theme.primaryColor,
                    todayBackgroundColor:this.props.showTodayBackGroundColor ? '#D9E2F8':'transparent',
                    textDayHeaderFontSize: 12,  //星期的字体大小
                    textDayFontSize: 16,
                    textMonthFontSize: 16,
                    'stylesheet.calendar.header': {
                        monthText:{
                            marginTop:5
                        },
                        week: {
                            marginTop: 5,
                            flexDirection: 'row',
                            justifyContent: 'space-around',
                        }
                    }
                }}
            />
        );
    };

    getDays() {
        let now = new Date();
        let now_000 = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
        let add = now_000.getDay() == 6 ? 0 : now_000.getDay() + 1;
        let dayData = [];
        for (let i = 6 + add; i >= 0; i--) {
            let day = {};
            day.date = new Date(now_000.getTime() - i * (24 * 60 * 60 * 1000));
            dayData.push(day);
        }

        this.setState({ dayData: dayData,curDate: now_000 });
    }

    _renderWeekItem = ({ item, index }) => {
        // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height?Dimensions.get('window').height:Dimensions.get('window').width;
        let widthItem = SCREEN_WIDTH / 7;
        console.log('日期宽度均分---',widthItem);

        return (
            <View style={{ height: 50, width: widthItem }}>
                <Text allowFontScaling={false}
                      style={{
                          width: "100%",
                          lineHeight: 70,
                          height: "100%",
                          textAlignVertical: 'center',
                          textAlign: 'center',
                          color: "#7F7F7F" // shenyonggang@20200804 add for select color change
                      }}>
                    {item}
                </Text>
            </View>
        );
    }
    _renderDayItem = ({ item, index }) => {
        // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height?Dimensions.get('window').height:Dimensions.get('window').width;
        let widthItem = SCREEN_WIDTH / 7;
        console.log('日期宽度均分-riqi--',widthItem);
        let weekDay = this.state.dayData[this.state.dayData.length - 1];
        let week = weekDay.date;
        let isSelected = this.state.curDate.getDate() == item.date.getDate();
        let date = item.date;
        let dayTxt = (date.getDate() < 10 ? "0" : "") + date.getDate();
        if (index < week.getDay() && this.state.dayData.length != 7) {
            dayTxt = "";
        }
        // console.log("_renderDayItem",item);

        return (
            <TouchableOpacity style={{
                height: 70,
                width: widthItem,
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
            }} onPress={() => {
                if (dayTxt != "") {
                    this.setState({curDate: date,dateTime: DateUtils.dateFormat(date, "yyyy-MM-dd")}, () => {
                        this._onDayPress();
                    });
                }
            }}>

                {
                    isSelected ?
                        <View style={{
                            position: "absolute",
                            backgroundColor: '#1E5BA9',
                            width: widthItem,
                            height: widthItem,
                            borderRadius: widthItem / 2
                        }}/>
                        : null
                }

                <View style={{ height: 70, width: widthItem }}>
                    <Text allowFontScaling={false}
                          style={{
                              width: "100%",
                              lineHeight: 70,
                              height: "100%",
                              textAlignVertical: 'center',
                              textAlign: 'center',
                              color: isSelected ? "white" : "#7F7F7F" // shenyonggang@20200804 add for select color change
                          }}>
                        {dayTxt}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    }

    _renderDay() {
        let calendarLocaleConfig = stringsTo('calendarLocaleConfig');
        if (this.state.dayData.length == 0){
            this.getDays();
        }
        return (
            <View style={{  width: SCREEN_WIDTH }}>
                <View style={{ height: 50, width: SCREEN_WIDTH, backgroundColor: 'white' }}>
                    <FlatList
                        data={calendarLocaleConfig.dayNames}
                        horizontal={true}
                        renderItem={this._renderWeekItem}
                        keyExtractor={(item, index) => `key_${ index }`}
                    />
                </View>
                <View style={{ height: this.state.dayData.length == 7 ? 70 : 140, width: SCREEN_WIDTH, backgroundColor: 'white' }}>
                    <FlatList
                        data={this.state.dayData}
                        numColumns={7}
                        // horizontal={true}
                        renderItem={this._renderDayItem}
                        keyExtractor={(item, index) => `key_${ index }`}
                    />
                </View>

                <TouchableWithoutFeedback onPress={() => {

                }}>
                    <View style={{ flex: 1, opacity: 0.5, width: SCREEN_WIDTH, backgroundColor: 'black' }}/>
                </TouchableWithoutFeedback>
            </View>
        );
    }

    componentDidMount(){
        Orientation.lockToPortrait();
    }

}

PlayBackSelectBar.propTypes = {
    onEventItemPress: PropTypes.func,
    onDayPress: PropTypes.func,
    onDevItemPress: PropTypes.func,
    onVisibleMonthsChange: PropTypes.func,


    eventCurrentTitles: PropTypes.array,
    devCurrentTitles: PropTypes.array,
    dateTimeDotSelect: PropTypes.object,

    initData: PropTypes.array,

    renderView: PropTypes.func,
    hideTitleSelect: PropTypes.bool,
    showGridLine: PropTypes.bool,
    editMode: PropTypes.bool,
    showTodayBackGroundColor:PropTypes.bool,// 是否显示今天日期背景色
    isSevenDay: PropTypes.bool,//是否是7天循环云存储 默认为否
    minDate:PropTypes.string,
    pastScrollRange:PropTypes.number,
    futureScrollRange:PropTypes.number,
    modeChange:PropTypes.func,
    tabType:PropTypes.number,
    modeType:PropTypes.number,
    selectDay:PropTypes.string,//当前已经选择的天，横竖屏切换会重新绘制view导致选中一致问题
};

const styles = StyleSheet.create({
    eventItem: {
        textAlign: 'center',
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.gray,
        lineHeight:21
    },

    eventItemBg: {
        padding: 14,
        width: 159,
        borderRadius: 8,
        backgroundColor: '#F2F3F5',
    },

    selectedEventItem: {
        textAlign: 'center',
        fontSize: 14,
        fontWeight: 'bold',
        lineHeight:20,
        color: imiThemeManager.theme.primaryColor,
    },
    selectedEventItemBg: {
        padding: 14,
        width: 159,
        borderRadius: 8,
        backgroundColor: '#EDF0FC',
    },
});
