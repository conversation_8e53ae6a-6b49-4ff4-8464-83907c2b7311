/**
 * AlarmPagePlayerDualComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 *
 * 示例:
 * <AlarmPagePlayerDualComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </AlarmPagePlayerDualComponent>
 *
 * @author: yanmin
 * @date: 2020/12/28
 */

import React, {Component, useEffect, useState} from 'react';
import {
  View,
  Text,
  BackHandler,
  ActivityIndicator,
  StatusBar,
  StyleSheet,
  Dimensions,
  ScrollView,
  DeviceEventEmitter,
  TouchableOpacity,
  Image,
  PanResponder,
  Animated,
} from 'react-native';
import {
  AlarmType,
  aliAlarmEventCloudApi,
  DateUtils,
  IMIDownload,
  IMIGotoPage,
  LetDevice,
  IMIPackage,
  IMILog,
  LetIMIIotRequest,
} from '../../../../imilab-rn-sdk';
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from '../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {stringsTo, locales} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import Orientation from 'react-native-orientation';
import iconEmpty from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/police_pic_empty.png';
import PropTypes from 'prop-types';
import {isAndroid, isEmpty, isIos, isPhoneX, isIphoneXSeries, isIphone14ProMax} from '../../../../imilab-rn-sdk/utils/Utils';
import IMIToast from '../../../../imilab-design-ui/src/widgets/IMIToast';
import PlayBackToolBarView from '../../../../imi-rn-commonView/PlayerToolBarView/PlayBackToolBarView';
import {Calendar} from 'react-native-calendars';
import {
  colors,
  imiThemeManager,
  showLoading,
  RoundedButtonView,
  showToast,
  IMIImageView,
  IMIDesignEmptyViewNew,
} from '../../../../imilab-design-ui';
import ImageButton from '../../../../imi-rn-commonView/ImageButton/ImageButton';
import ModalView from '../../../../imi-rn-commonView/ModalView/ModalView';
import I18n from '../../../../globalization/Localize';

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';
import moment from 'moment';
import Toast from 'react-native-root-toast';
import IMIPermission from '../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import {CONST} from '../../../../imilab-design-ui/src/style/ThemeManager';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import {startUnix, timeFilter} from '../../../../imilab-rn-sdk/utils/DateUtils';
import PlayBackFullScreenToolBarView from '../../../../imi-rn-commonView/PlayerToolBarView/PlayBackFullScreenToolBarView';
import SelectEventTimeView from './selectEvent/SelectEventTimeView';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import ChoiceItem from '../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import VideoProgressView from './videoProgressView/VideoProgressView';
import NetInfo from '@react-native-community/netinfo';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import IMCloudServerVideoView from '../../../../imilab-rn-sdk/native/camera-kit/IMCloudServerVideoView';
import {cloudDeviceService} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import {assemblyDate, customJsonParse} from '../utils/GenericUtils';
import CalendarShow from './CalendarShow';
import CloudToolBarView from '../../../../imi-rn-commonView/PlayerToolBarView/CloudToolBarView';
import TouchableOpacityImage from '../../../../imi-rn-commonView/TouchableOpacityImage/TouchableOpacityImage';
import { getCloudList } from './utils';
import { SafeAreaInsetsContext } from 'react-native-safe-area-context';
import ChoiceItemNew from '../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItemNew';
import iconDotMove from '../../resources/images/pic_move.png';
import iconDotPeople from '../../resources/images/pic_person.png';
import iconDotSound from '../../resources/images/pic_sound.png';
import iconDotFenceEnter from '../../resources/images/pic_enter_fence.png';
import iconDotFenceLeave from '../../resources/images/pic_leave_fence.png';
import iconDotVehicleDetect from '../../resources/images/pic_detect_vehicle.png';
import iconDotApproachVehicle from '../../resources/images/pic_approach_vehicle.png';
import iconDotBikeDetect from '../../resources/images/pic_detect_bike.png';
import iconDotApproachBike from '../../resources/images/pic_approach_bike.png';


const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
const speedTitleAry = ['1X', '2X', '4X'];
const speedAccessibilityLabelTitleAry = [
  'play_back_clarity_show_1X',
  'play_back_clarity_show_2X',
  'play_back_clarity_show_4X',
  'play_back_clarity_show_/8X',
  'play_back_clarity_show_16X',
];
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
// let windowWidth = Dimensions.get('window').width;
const VOD_PLAYER_STATUS = {
  PREPARED: 'prepared', //准备完毕，调用start即可播放
  LOADING: 'loading',
  PLAYING: 'playing', //正在播放
  PAUSE: 'pause',
  ERROR: 'error',
};

let time; //相册名字
let backupIsPlay = false;

let lastClickSnapPhoto = 0; //上一次点击截图的时间
let dateDataArr = new Map();

let secondsDay = 24 * 3600;

let ms = 3000; //沟通固件多出3秒
let tempVideoList = []; //用于存储临时视频列表
let tempEventList = []; //用于存储根据视频列表开始时间与结束时间请求返回的事件列表
let tempRealVideoList = []; //用于存储筛选出来的视频有效数据
let reqEventType = 0; //当前筛选的事件类型，默认是0，表示全部事件
const EVENT_NAME = 'cloudStoragePageDownload';


const is720P = width < 360; //分辨率是否低于1080P,此标记用于适配低分辨率屏幕上的显示 #6991

let isCheckingPermission = false;

const LIST_LIMIT = 20; //每次拉取的视频个数
const EVENT_LIST_LIMIT = 10; //每次拉取事件视频数
const ALL = {eventTypeName: stringsTo('all_events_str'), eventType: 'housekeeping_event_all'};
const kWindowWidth = Math.min(width,height); // use this.winPortraitWidth instead
const kWindowHeight = Math.max(width,height);
export default class AlarmPagePlayerDualComponent extends Component {
  static VOD_PLAYER_STATUS = VOD_PLAYER_STATUS;

  constructor(props, context) {
    super(props, context);
    this.state = {
      bps: -1,
      isFullScreen: false, //是否全屏
      mute: true,
      recording: false, //录屏监听  true为录屏中
      recordDuration: 0, //录屏时长,小于6秒提示失败
      showFullScreenTools: false,

      isLoading: true, //加载中,进入显示加载中
      showErrorView: false, //错误view
      showPauseView: false, //暂停view
      errorCode: null, //错误code

      dataArray: [],
      currentDate: this.props.queryTime,
      speed: undefined, //倍速
      speedIndex: 0,
      snapshotVisible: false, //截图是否显示中
      screenShotPath: null,
      screenShotPathType: 0,
      isPlay: false, //是否播放中 true为播放中
      isShowCalendar: false,
      isClickPause: false,
      dateData: {}, //日期显示
      isPlayFinish: false, //是否播放结束
      dateText: '',
      showEventModal: false, //是否显示事件弹窗
      eventAlarmType: ALL.eventType,
      // pageStart:this.props.pageStartProp,
      pageStart: 0,
      dataList: [], //图片集合
      videoList: [], //视频结果集合
      videoListOld: [], //视频不足9个时
      videoListEvent: [], //筛选事件云存
      selectEventId: this.props.eventIdProp,
      selectEventAlarmType: ALL.eventType, //选择的事件类型
      isPlayFromBeginning: false,
      durationProgress: 0,
      deleteSegmentId: '',
      noVideo: false, //是否有视频文件，默认有视频文件，
      noVideoPic: '', //没有视频文件，在播放器上面，展示缩略图
      isFirstIn: true, //标记是否是第一次进入云存，请求数据，第一次进入云存，
      // 请求回数据后，需要自动播放第一条数据，其他情况不在自动播放第一条数据
      todayIsNoData: false, //标记今天没有云存数据，切换其他天，不管
      videoPage: 0, //视频
      eventVideoList: [], //显示用的视频事件集合
      nextValid: false, //视频是否加载完成
      selectClickDay: false, //今天是否选择其他日期
      netFileName: '',
      flatListHeight: 0,

      //deleteSegmentId: '',
      showDeleteTip: false,
      deleteFileIndex: -1,
      timeEnd: 0, //用于加载更多的结束时间，不要和每次查询的结束时间弄混淆。 因为现在的查询时间为倒叙，所以查询结束时间为每次查询结果的最后一个视频的时间 ，但是开始的时间为当天的凌晨时间
      videoItem: this.props.videoItem,
      eventTypeArr: [], //事件类型
      eventCurrentIndex: 0,
      showRTCView: true, //控制播放器是否加载，因为现在每次销毁都要重新加载
      seekTo: undefined, // 播放器播放进度
      switchVideoItemIng: false, // 正在切换视频中 ，切换中进度条不能变
      screenSelectionDialog:false,// 显示选择画面
      screenSelectionId:0,
      cameraReady0:false,// 摄像机0是否准备好
      cameraReady1:false,// 摄像机1是否准备好
      cameraNoVideo0:false,//摄像机0没数据
      cameraNoVideo1:false,//摄像机1没数据
      cameraIsFull0:false, //摄像机0全屏
      cameraIsFull1:false,//摄像机1全屏
      cameraShowErrorView0:false,//摄像机0异常
      cameraShowErrorView1:false,//摄像机1异常
      cameraErrorCode0: null, //摄像机0异常错误code
      cameraErrorCode1: null, //摄像机0异常错误code
    };
    let reqTime = this._getDataFormatTime();
    if (this.props.eventIdProp != '0') {
      let times = moment(this.props.pictureTime).format('YYYY-MM-DD');

      reqTime = times;
    }
    // console.log("time u:"+reqTime)
    this.reqTime = reqTime;
    this.tempPictureTime = this.props.pictureTime;
    this.homeFrom = false;
    this.selectBeginTime = 0;
    this.loadingTimes = 0; //每次加载为0
    this.netError = false; //点击重试,防止page+1
    // this.reqTime = this._getDataFormatTime();
    this.preProgress = 0; //下载进度可能出现比上一次小的情况，做个中间变量，下载时，下一次的下载进度如果比上一次的小，不在更新下载进度
    this.delCurPlayFile = false;
    this.isForegroundPage = false;
    this.tempMute = true;
    this.keepLoad = false; //标记是否继续加载后面的数据，true继续，false不继续
    this.isListLoading = false; // 列表的加载状态，只要是请求网络就是加载中
    this.leaveCloudStatus = false;
    this.isView = false;
    this.navigationBarRef=null;
    this.screenSelectionList=[
      {name:stringsTo('dual_camera_view'),selectIcon:require('../../resources/images/cloud_dual_select.png'),unSelectIcon:require('../../resources/images/cloud_dual_unSelect.png'),id:0},
       {name:stringsTo('fixed_camera_view'),selectIcon:require('../../resources/images/cloud_gun_ptz_select.png'),unSelectIcon:require('../../resources/images/cloud_gun_ptz_unSelect.png'),id:1},
        {name:stringsTo('ptz_camera_view'),selectIcon:require('../../resources/images/cloud_ptz_select.png'),unSelectIcon:require('../../resources/images/cloud_ptz_unSelect.png'),id:2}
    ];
    this.pan = new Animated.ValueXY();
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponderCapture: () => true,

      // 手指按下
      onPanResponderGrant: (evt, gestureState) => {
        // 启动 500ms 计时器，标记可以开始拖动
        this.isLongPress = false;
        this.clickTimeout = setTimeout(() => {
          this.isLongPress = true; // 超过 500ms，允许拖动
          this.needInitOffset = true;
          this._onCloseFullScreenTools()
        }, 100);
      },

      // 手指移动
      onPanResponderMove: (evt, gestureState) => {
        // 长按100ms内不移动
        if (!this.isLongPress) {
          return;
        }

        if (this.needInitOffset) {
          this.needInitOffset = false;
          this.offsetX = this.pan.x._value
          this.offsetY = this.pan.y._value
          this.pan.setOffset({
            x: this.pan.x._value,
            y: this.pan.y._value,
          });
        }
        // x: 值为gestureState.dx, 最小值(-窗口宽度+悬浮窗宽度-水平偏移量), 最大值(-水平偏移量)
        // y: 值为gestureState.dy, 最小值(-悬浮窗顶部边距-垂直偏移量), 最大值(屏幕高度-悬浮窗高度-悬浮窗上下边框高度-悬浮窗顶部边距-垂直偏移量)
        this.pan.setValue({ x: Math.max(Math.min(gestureState.dx, -this.offsetX), -Dimensions.get('window').width + 195 - this.offsetX),
          y: Math.min(Math.max(gestureState.dy, -60 - this.offsetY), Dimensions.get('screen').height - 175 - this.offsetY) });
      },

      // 手指抬起
      onPanResponderRelease: (evt, gestureState) => {
        // 清除计时器
        if (this.clickTimeout) {
          clearTimeout(this.clickTimeout);
          this.clickTimeout = null;
        }
        this.pan.flattenOffset();

        if (!this.isLongPress) {
          // 点击逻辑：500ms内抬起，执行画面切换
          if (this.state.isFullScreen) {
            this.setState({
              cameraIsFull0: !this.state.cameraIsFull0,
              cameraIsFull1: !this.state.cameraIsFull1,
            });
          }
        } else {
          this.isLongPress = false;
        }
      },

      // 手指移出屏幕等意外结束
      onPanResponderTerminate: () => {
        if (this.clickTimeout) {
          clearTimeout(this.clickTimeout);
          this.clickTimeout = null;
        }
      },
    });
  }

  static propTypes = {
    navBar: PropTypes.func,
    videoRef: PropTypes.func,
    navBarRight: PropTypes.array,

    toolBarMoreItems: PropTypes.array,
    fullScreenToolBarMoreItems: PropTypes.array,
    lensCorrect: PropTypes.shape({
      use: PropTypes.bool.isRequired,
      x: PropTypes.number,
      y: PropTypes.number,
    }),
    onVideoClick: PropTypes.func,
    onHlsPlayerStatusChange: PropTypes.func,
    fileNameStatusChangeListener: PropTypes.func,
    loadingView: PropTypes.func,
    pauseView: PropTypes.func,
    errorView: PropTypes.func,
    onCheckPermissionStatusChange: PropTypes.func, //回调是否处于权限检测状态中
    pageStartProp: PropTypes.number,
    eventAlarmTypeProp: PropTypes.number,
    eventIdProp: PropTypes.string,
    pictureTime: PropTypes.string,
    showAlarmView: PropTypes.bool, //看家是否打开
    netConnected: PropTypes.bool, //是否联网
    componentContainerRef: PropTypes.func, //回调组件容器的引用，方便调用者以此来获取容器的宽高等参数
    eventType: PropTypes.number, // 云存事件类型，0:连续云存 1:事件云存
  };

  static defaultProps = {
    navBar: undefined,
    navBarRight: [],
    toolBarMoreItems: [],
    fullScreenToolBarMoreItems: [],
    lensCorrect: {use: false, x: 0, y: 0},
    pageStartProp: 0,
    eventAlarmTypeProp: AlarmType.ALL,
    eventIdProp: '0',
    pictureTime: '0',
    showAlarmView: false, //看家是否打开
    netConnected: true, //是否联网
    eventType: 1,
  };

  _getDataFormatTime() {
    return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
  }
  UNSAFE_componentWillMount() {
    // Orientation.lockToPortrait();
  }


  /**
   * 获取当前数据           **  可以进行子类重写 **
   * @param isPullDown
   * @private
   */
  _queryVideoList = isPullDown => {
    this.refreshList && this.refreshList.refreshPreLoad(isPullDown);
    let page = isPullDown ? 0 : this.netError ? this.state.pageStart : this.state.pageStart + 1; //断网点击重试会自动+1
    this.setState({pageStart: page});
    //let paramsToken = isPullDown ? '' : this.reqToken;
    // this._getAliVideoListOnly(this.reqTime,   isPullDown,page);
    console.log(' this._queryDataList:' + isPullDown);
    //外部触发请求数据，都需要把这个清空
    tempRealVideoList = [];
    tempVideoList = [];
    tempVideoList = [];
  };


  /*显示没有找到对应的云存视频*/

  /**
   * 请求新的视频前的准备工作
   *
   */
  reqDataPre(shouldPlayFirstVideo = false) {
    //切换事件
    //清空临时
    tempRealVideoList = [];
    tempVideoList = [];
    tempVideoList = [];
    reqEventType = this.state.selectEventAlarmType == AlarmType.ALL ? 0 : this.state.selectEventAlarmType;
    this.refreshList && this.refreshList.refreshPreLoad(true);
  }

  /**
   *
   */
  getEventListAllNew() {

    const params = {
      Path: '/v1.0/imilab-01/app/cloudstorage/getEventListBySegmentid',
      ParamMap: {
        iotId: LetDevice.deviceID,
        segmentId: this.state.videoItem.segmentId,
        productId: LetDevice.model
      },
      Method: 'GET',
    };
    if(this.state.selectEventId !== '0') {
      params['ParamMap']['eventType'] = this.state.selectEventId
    }
    // showLoading(stringsTo('commLoadingText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        let newLists = [];

        newLists = item.cloudStorageEventList.filter(eventItem => eventItem?.displayEventType !== 'Ignore');
        this.setState({
          eventVideoList: newLists,
        });
      })
      .catch(e => {
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        // showLoading(false);
      });

  }


  componentDidMount() {
    this.leaveCloudStatus = false;
    console.log('DateUtils.getDay(-30)----', DateUtils.getDay(-30));
    this.props.componentContainerRef && this.props.componentContainerRef(this.componentContainer);
    // console.log("事件显示:"+this.state.currentDate)
    // 物理返回键需打开，否则全屏返回事件不对
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', _ => this._onPressBack());
    this.setState({dateTime: DateUtils.dateFormat(this.state.currentDate, 'yyyy-MM-dd')});
    //this._queryDataList(true);
    //this._queryMonthData();
    tempRealVideoList = [];
    tempVideoList = [];
    tempVideoList = [];
    reqEventType = 0;
    //20220323@byh loading时间过长可能会挡住播放器的播放,改为在九空格中loading
    //showLoading(stringsTo('commLoadingText'),true);
    this.refreshList && this.refreshList.refreshPreLoad(true);


    this.getEventListAllNew();

    //进入后台
    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      this.leaveCloudStatus = true;
      // 根据是否为弹出权限来决定是否暂停
      // this.IMIVideoView && this.IMIVideoView.pause();
      //如果处于播放状态退到后台暂停
      /*if(this.state.isFullScreen){
                console.log('云存储全屏时进入后台那么直接退出全屏');
                this._exitFullScreen();
            }*/
      //关闭录像
      if (this.state.isPlay && !isCheckingPermission) {
        backupIsPlay = true;
        this.setState({isPlay: false, showPauseView: true, isClickPause: true}, () => {
          this.IMIVideoView && this.IMIVideoView.pause();
        });
      } else {
        backupIsPlay = false;
      }
      console.log('addListener CloudStorage enterBackgroundListener : backupIsPlay= ' + backupIsPlay);
    });
    //回到前台
    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      //启动
      console.log('addListener CloudStorage enterForegroundListener : backupIsPlay= ' + backupIsPlay);
      // console.log('回到前台当前声音状态---',this.state.mute);
      // if (backupIsPlay && !isCheckingPermission) {
      //   // console.log('获取当前声音状态===',this.muteStatus);
      //   this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
      //     this.isForegroundPage && this.IMIVideoView && this.IMIVideoView.resume();
      //   });
      // }
      this.setState({mute: true});
    });

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      // console.log("CloudStoragePage _subscribe_focus"+this.camera)
      //Orientation.addOrientationListener(this._orientationDidChange);
      console.log('focus---进入当前云存页面全屏状态', this.state.isFullScreen);
      this.isForegroundPage = true;
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setBarStyle('light-content');
      console.log('更新导航栏状态---');
      // StatusBar.setBackgroundColor('transparent');//状态栏字体刷黑
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      this.isForegroundPage = false;
    });

    this.unsubscribe = NetInfo.addEventListener(state => {
      console.log('当前网路状态', state);
      //监听防止录像未停止
      if (state.isConnected == false) {
        showToast(stringsTo('network_not_connected'));
        if (this.state.recording) {
          //直播流暂停时，停止录像
          if (CONST.isAndroid) {
            this._stopRecord();
          } else {
            //因为IOS会自动停止视频录制，所以直接转存即可
            this._saveVideoToPhotosAlbum();
          }
        }
        this.setState({
          isLoading: false,
        });
        if (this.state.isPlay) {
          this.setState({isPlay: false, showPauseView: true, isClickPause: true}, callback => {
            this.IMIVideoView && this.IMIVideoView.pause();
          });
        }
      }
      console.log('Is connected?', state.isConnected);
    });

    // this._subscribe_blur = this.props.navigation.addListener('blur', () => {
    //     console.log('blur--离开当前云存页面全屏状态',this.state.isFullScreen);
    // });

    //510的播放结束回调
    /* LetDevice.addDeviceEventChangeListener((data) => {
             let {iotId, identifier, value} = JSON.parse(data);
             if (iotId == LetDevice.deviceID && identifier === "onPlayBackEnd") {
                 console.log("szm ============== 播放结束了:")
                 this.IMIVideoView&&this.IMIVideoView.pause();
                 this.setState({isPlay: false, showPauseView: true});
             }
         });*/
  }

  componentWillUnmount() {
    Orientation.removeOrientationListener(this._orientationDidChange);
    // this._subscribe_focus && this._subscribe_focus();
    this._subscribe_blur && this._subscribe_blur();
    this.IMIVideoView && this.IMIVideoView.stop();
    this.IMIVideoView && this.IMIVideoView.destroy();
    this.IMIVideoView = null;
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.preparetimer && clearTimeout(this.preparetimer);
    this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
    this.backHandler && this.backHandler.remove();
    this._enterBackground && this._enterBackground.remove();
    this._enterForeground && this._enterForeground.remove();
    this.playTimeOut && clearTimeout(this.playTimeOut);
    this.loadingTime && clearTimeout(this.loadingTime);
    this.unsubscribe && this.unsubscribe();
    //下载的监听
    this.listener && this.listener.remove();
    this.listener = null;
    this.intervalTimer && clearInterval(this.intervalTimer);
    this.intervalTimer = null;
    this.pollTimeout && clearTimeout(this.pollTimeout);
    this.pollTimeout = null;
    //防止退出后还有加载框显示
    showLoading(false);
    this.setState = (state, callback) => {
      return;
    };
  }

  _downloadPress = () => {
    console.log("his.state.videoItem?.segm",this.state.videoItem?.segmentId);
    
    if (!this.state.videoItem?.segmentId) {
      //可以做个提示
      return;
    }
    //需要检测权限
    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
    isCheckingPermission = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.getDownloadUrl();
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
          this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
        isCheckingPermission = false;
      }
    });
  };
  /**
   * 需要轮询接口获取下载地址
   * 此接口需要转码MP4耗时
   * @param filename
   */
  getDownloadUrl() {
    showLoading(stringsTo('alarm_download_downloading'), true);
    this.intervalTimer && clearInterval(this.intervalTimer);
    const saveFileName = `VIDEO_IMI_${new Date().getTime()}`;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${saveFileName}.mp4`;
    const {segmentId, publicKeyVersion} = this.state.videoItem;
    IMILog.logI('getDownloadUrl',`${segmentId} ${publicKeyVersion}`)
    aliAlarmEventCloudApi
      .downloadVideo(LetDevice.deviceID, segmentId, publicKeyVersion, saveFilePath, saveFileName)
      .then(_ => {
        var path = saveFilePath;
        if (isAndroid()) {
          path = `${saveFilePath}/concat.mp4`;
        }
        // 推送到相册
        IMIFile.saveVideoToPhotosAlbum(path, LetDevice.deviceID)
          .then(_ => {
            IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
            showLoading(false);
          })
          .catch(error => {
             IMILog.logI('getDownloadUrlsaveVideoToPhotosAlbumError',JSON.stringify(error))
            showToast(stringsTo('video_download_fail'));
            showLoading(false);
          });
      })
      .catch(error => {
         IMILog.logI('getDownloadUrlError',JSON.stringify(error))
        showToast(stringsTo('video_download_fail'));
        showLoading(false);
      });
    // this.intervalTimer = setInterval(() => {
    //     if (!isStartDownload){
    //         //未开始下载，才去轮询获取下载地址
    //         aliAlarmEventCloudApi.downloadAliVideoList(LetDevice.deviceID,fileName).then(data=>{
    //             console.log("data",data);
    //             let result = typeof (data) == "object"?data:JSON.parse(data);
    //             if (result.progress == 100 &&  !isStartDownload){
    //                 isStartDownload = true;
    //                 //说明已经转码完成了
    //                 let endTime = new Date().getTime();
    //                 let time = endTime - startTime;
    //                 console.log("转码使用时间："+time);
    //                 this.intervalTimer && clearInterval(this.intervalTimer);
    //
    //                 //转码完成后，我们去下载
    //                 this.downloadVideo(result.url);
    //             }
    //
    //         }).catch(error=>{
    //             showToast(stringsTo('video_download_fail'));
    //             this.intervalTimer && clearInterval(this.intervalTimer);
    //             showLoading(false);
    //         })
    //     }
    //
    // },1000);

    // this.pollToGetUrl(fileName);
  }

  pollToGetUrl(fileName) {
    aliAlarmEventCloudApi
      .downloadAliVideoList(LetDevice.deviceID, fileName)
      .then(data => {
        console.log('data', data);
        let result = typeof data === 'object' ? data : JSON.parse(data);
        if (result.progress == 100) {
          //说明已经转码完成了
          console.log('转码完成');
          this.intervalTimer && clearInterval(this.intervalTimer);

          //转码完成后，我们去下载
          this.downloadVideo(result.url);
        } else {
          this.pollTimeout && clearTimeout(this.pollTimeout);
          this.pollTimeout = setTimeout(() => {
            this.pollToGetUrl(fileName);
          }, 500);
        }
      })
      .catch(error => {
        if (this.state.eventVideoList.length > 0 && this.state.eventVideoList[0].deleteSegmentId === fileName) {
          showToast(stringsTo('video_download_fail_warning'));
        } else {
          showToast(stringsTo('video_download_fail'));
        }
        this.intervalTimer && clearInterval(this.intervalTimer);
        showLoading(false);
      });
  }

  downloadVideo(url) {
    let nameDate = new Date().getTime();
    //文件命名不能重复，相同文件名，会存在多个文件，
    //会出现转存相册一次转存多个文件进入
    // if (this.downLoadEventTime){
    //     nameDate = this.downLoadEventTime;
    // }
    let fileName = `${moment(nameDate).format('YYYYMMDDHHmmss')}.mp4`;
    const path = `${CLOUD_DOWNLOAD}/${fileName}`;

    IMIDownload.downloadToPath(EVENT_NAME, url, CLOUD_DOWNLOAD, fileName);

    this.listener = DeviceEventEmitter.addListener(EVENT_NAME, event => {
      //console.log('DeviceEventEmitter 下载 '+JSON.stringify(event));
      if (event.status === IMIDownload.STATUS_START) {
      }
      if (event.status === IMIDownload.STATUS_DOWNLOADING) {
        //这里可以显示下载进度，UI展示进度等，都OK
        //Android端与iOS端返回的数据，暂时不统一。
        //要求ios不显示进度，Android显示
        let progress = Math.round(event.progress * 100);
        if (isAndroid()) {
          progress = Math.round((event.progress * 100) / event.max);
        }
        if (progress >= this.preProgress) {
          this.preProgress = progress;
          showLoading(progress + '%', true, false);
        }
      }

      if (event.status === IMIDownload.STATUS_ERROR) {
        // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
        //用过一次必须释放
        this.listener && this.listener.remove();
        this.listener = null;
        this.preProgress = 0;
        showLoading(false);
        showToast(stringsTo('video_download_fail'));
      }
      if (event.status === IMIDownload.STATUS_CANCEL) {
        // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
        //用过一次必须释放
        this.listener && this.listener.remove();
        this.listener = null;
        this.preProgress = 0;
        showLoading(false);
        showToast(stringsTo('video_download_fail'));
      }

      if (event.status === IMIDownload.STATUS_SUCCESS) {
        console.log('--------save to album');
        this.preProgress = 0;
        //const path = `${event.downloadPath}/${fileName}`;
        IMILog.logD('传参 jeff path :', path);
        IMILog.logD('传参 jeff deviceID :', LetDevice.deviceID);
        IMIFile.saveVideoToPhotosAlbum(path, LetDevice.deviceID)
          .then(data => {
            showLoading(false);
            showToast(stringsTo('save_success'));
            console.log('保存相册' + data);
          })
          .catch(error => {
            showLoading(false);
            IMILog.logD('返回值 error :', JSON.stringify(error));
            console.log('保存相册error--' + path + JSON.stringify(error));
            showToast(stringsTo('save_failed'));
          });
        //用过一次必须释放
        this.listener && this.listener.remove();
        this.listener = null;
      }
    });
  }

  _orientationDidChange = orientation => {
    console.log('云存页面方向---', orientation, this.state.isFullScreen);
    if (orientation === 'LANDSCAPE') {
    } else {
      // do something with portrait layout
      console.log('云存页退出全屏--全屏状态-gengxin', this.state.isFullScreen);
      if (this.state.isFullScreen && isIos()) {
        this._exitFullScreen();
        // StatusBar.setBarStyle("light-content");
        // StatusBar.setBackgroundColor('transparent');//状态栏字体刷黑色
        // NavigationBar.setBarStyle('dark-content');
      }
    }
  };

  _onPressFullScreen = (cameraLens) => {
    // if(!this._canStepIn())  return;
    if(cameraLens===0){
      this.setState({cameraIsFull0:true})
    }
    if(cameraLens===1){
      this.setState({cameraIsFull1:true})
    }
    this.setState({isFullScreen: true,}, () => {
      isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
      NavigationBar.setStatusBarHidden(true);
      this.props.navigation.setOptions({tabBarVisible: false});
    });
    this._onPressFullScreenTools();
  };

  _exitFullScreen = () => {
    // if(!this._canStepIn())  return;
    this.setState({isFullScreen: false,cameraIsFull0: false,cameraIsFull1: false}, () => {
      Orientation.lockToPortrait();
      NavigationBar.setStatusBarHidden(false);
      this.props.navigation.setOptions({tabBarVisible: true});
    });
    this.pan.setValue({x: 0, y: 0});
    this._onCloseFullScreenTools();
  };

  _onPressBack = () => {
    this.isView = false;
    if (this.state.isFullScreen) {
      this._exitFullScreen();
    } else {
      if (this.state.recording) {
        showToast(stringsTo('screen_recording'));
        return;
      }

      this.props.navigation.goBack();
    }
    return true;
  };

  _onPressFullScreenTools = () => {
    this.setState({showFullScreenTools: true});
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.fullScreenTooltsTimer = setTimeout(() => {
      this._onCloseFullScreenTools();
    }, 5000);
  };

  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  _onPressMute = () => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (!this._canStepIn()) {
      return;
    }
    this.setState({mute: !this.state.mute});
  };

  //点击截屏按钮
  _onPressScreenShot = () => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (!this._canStepIn()) {
      return;
    }

    if (new Date().getTime() - lastClickSnapPhoto < 1000) {
      //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
      return;
    }
    lastClickSnapPhoto = new Date().getTime();

    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
    isCheckingPermission = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView.snap(this.currentSnapshotPath)
              .then(_ => {})
              .catch(() => showToast(stringsTo('action_fail')));
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
          this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
        isCheckingPermission = false;
      }
    });
  };
 //点击截屏按钮 摄像头1
  _onPressScreenShot1 = () => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (!this._canStepIn()) {
      return;
    }

    if (new Date().getTime() - lastClickSnapPhoto < 1000) {
      //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
      return;
    }
    lastClickSnapPhoto = new Date().getTime();

    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
    isCheckingPermission = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView1.snap(this.currentSnapshotPath)
              .then(_ => {})
              .catch(() => showToast(stringsTo('action_fail')));
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
          this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
        isCheckingPermission = false;
      }
    });
  };

  //点击录屏按钮
  _onPressRecord = () => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (!this._canStepIn()) {
      return;
    }
    if (this.state.recording) {
      console.log('录屏结束-------------');
      this._stopRecord();
    } else {
      this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
      isCheckingPermission = true;
      IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
        if (status === 0) {
          IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
            if (status2 === 0) {
              time = moment(new Date().getTime()).format('yyyyMMDD') + '_' + new Date().getTime();
              let pathUrl = VEDIO_RECORD_PATH;
              if (isIos()) {
                pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
              }
              this.IMIVideoView.startRecord(pathUrl)
                .then(_ => {
                  this.setState({recording: true, recordDuration: 0});
                })
                .catch(() => showToast(stringsTo('action_fail')));
            } else if (status2 === -1) {
              showToast(stringsTo('storage_permission_denied'));
            }
            this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            isCheckingPermission = false;
          });
        } else if (status === -1) {
          showToast(stringsTo('storage_permission_denied'));
          this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
          isCheckingPermission = false;
        }
      });
    }
  };

  //停止录像并保存在相册
  _stopRecord(isPause = false, isPlayCompletion = false) {
    let forSave = true;
    console.log('停止录制anle', this.state.recordDuration);
    if (isPlayCompletion && this.state.recordDuration == 0) {
      //防止在停止时
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      forSave = false;
    } else {
      if (this.state.recordDuration < 6) {
        IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
        forSave = false;
      }
    }
    this.IMIVideoView.stopRecord()
      .then(_ => {
        //停止录制
        console.log('停止录制*************************', forSave);
        if (!forSave) {
          //只停止，不保存
          console.log('停止录制-------保存失败'); //save_system_album_failed
          // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
          return;
        }
        let pathUrl = VEDIO_RECORD_PATH;
        if (isIos()) {
          pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID)
          .then(_ => {
            //转存视频
            IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            if (isPause) {
              return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
              //截屏一张作为悬浮缩略图，不转存
              this.setState({screenShotPathType: 2, screenShotPath: _.data, snapshotVisible: true});
              this.snapshotTimeout = setTimeout(() => {
                //3秒后截屏缩略图自动隐藏
                this.setState({snapshotVisible: false});
              }, 3000);
            });
          })
          .catch(error => {
            console.log('停止图片-------保存失败' + error);
          });
      })
      .catch(error => {
        console.log('停止录制-------保存失败' + error);
      });
    this.setState({recording: false, recordDuration: 0});
  }
  //IOS在视频流暂停时,将录制的视频保存到相册
  _saveVideoToPhotosAlbum(isPause = false) {
    if (this.state.recordDuration < 6) {
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      this.IMIVideoView.stopRecord()
        .then(_ => {})
        .catch(error => {}); //不调用，会导致iOS下次录制失败
      this.setState({recording: false, recordDuration: 0});
      return;
    }
    let pathUrl = VEDIO_RECORD_PATH;
    if (isIos()) {
      pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    }
    IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID)
      .then(_ => {
        //转存视频
        if (isPause) {
          return;
        }
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.setState({screenShotPathType: 2, screenShotPath: _.data, snapshotVisible: true});
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
        this.IMIVideoView.stopRecord()
          .then(_ => {})
          .catch(error => {}); //不调用，会导致iOS下次录制失败
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
      })
      .catch(err => {
        console.log('save video error:', err);
      });
    this.setState({recording: false, recordDuration: 0});
  }

  _onPressSpeed = () => {
    if (!this._canStepIn()) {
      return;
    }
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return;
    }
    // currentPlayTime = 0;
    //2022-03-22@byh 暂时去掉4倍速

    switch (this.state.speed) {
      case undefined:
        this.tempMute = this.state.mute;
        this.setState({speed: 2, mute: true, speedIndex: 1});
        break;
      case 1:
        this.setState({speed: 2, mute: this.tempMute, speedIndex: 1});
        break;
      case 2:
        this.tempMute = this.state.mute;
        this.setState({speed: 1, mute: true, speedIndex: 0});
        break;
      default:
        this.tempMute = this.state.mute;
        this.setState({speed: 1, mute: true, speedIndex: 0});
        break;
      // case 2:
      //     this.setState({speed: 0});
      //     this.IMIVideoView && this.IMIVideoView.speed(1);
      //     break;
      /*   case 3:
                   this.setState({speed: 4,mute: true});
                   this.IMIVideoView && this.IMIVideoView.speed(16);
                   break;
               case 4:
                   this.setState({speed: 0});
                   this.IMIVideoView && this.IMIVideoView.speed(1);
                   break;*/
    }
  };

  _onPressPlay = () => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }

    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return;
    }

    if (this.state.showErrorView && !this.state.isPlay) {
      this.progressView && this.progressView.resetStatus();
      //this.IMIVideoView && this.IMIVideoView.prepare();
      return;
    }
    // this.setState({isClickPause:true});

    if (this.tempScrollPause) {
      if (this.state.isClickPause) {
        // 暂停状态下滑动时间轴
        this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
          this.IMIVideoView && this.IMIVideoView.resume();
          this.IMIVideoView1 && this.IMIVideoView1.resume();
          this.props.fileNameStatusChangeListener && this.props.fileNameStatusChangeListener(1);
        });
      } else {
        this.progressView && this.progressView.resetStatus();
        //this.IMIVideoView && this.IMIVideoView.prepare();
        //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
      }
      return;
    }

    if (!this.state.isPlay) {
      if (this.state.isPlayFinish) {
        this.progressView && this.progressView.resetStatus();
        this.IMIVideoView?.reset();
        this.IMIVideoView?.prepare();
        this.IMIVideoView1?.reset();
        this.IMIVideoView1?.prepare();
        // if (isIos()) {
        //   this.setState({isLoading: true, showPauseView: false});
        // }
        this.setState({isPlay: true, showPauseView: false, isPlayFinish: false});
      } else {
        if (this.state.isClickPause) {
          this.IMIVideoView.resume();
          this.IMIVideoView1.resume();
        } else {
          this.progressView && this.progressView.resetStatus();
          //this.IMIVideoView.prepare();
        }
      }

      this.props.fileNameStatusChangeListener && this.props.fileNameStatusChangeListener(1);
      if (this.state.isClickPause) {
        this.setState({isPlay: true, showPauseView: false, isClickPause: false});
      } else {
        this.setState({isClickPause: false});
      }
      return;
    }

    if (this.state.recording) {
      console.log('暂停前自动关闭录屏 录屏结束-------------');
      this._stopRecord();
    }
    this.setState({isLoading: false, isClickPause: true}, () => {
      this.IMIVideoView && this.IMIVideoView.pause();
      this.IMIVideoView1 && this.IMIVideoView1.pause();
      this.props.fileNameStatusChangeListener && this.props.fileNameStatusChangeListener(0);
    });
  };

  _onEventChange = event => {
    console.log('event0:----------------',this.state.videoItem?.lensNumber, event);
    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      //this.setState({bps: event.extra.bps})
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
      //console.log(event.extra.currentTime);
      // let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;

      if (this.state.isLoading || this.state.showPauseView || !this.state.isPlay || !this.state.showErrorView||this.state.cameraNoVideo0) {
        // Iphone 12 bug: http://************:10083/issues/9393
        this.setState({
          isLoading: false,
          showPauseView: false,
          isPlay: true,
          isPlayFinish: false,
          showErrorView: false,
          cameraNoVideo0:false,
        });
      }
      if (this.state.noVideo) {
        this.progressView && this.progressView.onSliderValueChanged(0);
        this.state.homePictureTime = 0;
      } else {
        if (this.tempScrollPause) {
          //修改暂停逻辑 pause  需要resume后再seekto 防止回复在加载
          // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
          console.log(Math.round(this.tempScrollTime * 1000));
          this.tempScrollPause = false;
          //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime * 1000));
        } else {
          //202200302@byh ios最后返回的时间会比，视频时长少一秒，增加0.5修正，增加返回的视频时长与实际视频时长判断
          //Android不能添加,添加播放结束后可能在00：01秒位置
          let scrollTime = event.extra.currentTime;
          //20220318@byh 在播放完成后，监听播放完成，把进度拉满，所以这里不在需要修正
          // if (isIos()){
          //     scrollTime = scrollTime+0.5;
          // }
          let secondSTime = Math.round(scrollTime);
          let second = secondSTime > this.state.durationProgress ? this.state.durationProgress : secondSTime;

          if (second > 0&&this.state.videoItem?.lensNumber==0) {
            this.progressView && this.progressView.onSliderValueChanged(second);
          }
        }
      }
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      //调用video.start()后的第一个回调
      this.setState({
        isLoading: this.props.netConnected ? true : false,
        isPlay: false,
        showPauseView: false,
        isPlayFinish: false,
        showErrorView: false,
      });
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      // 第一帧
      this.setState({
        isLoading: !this.state.cameraReady1,
        showPauseView: false,
        isPlay: true,
        isPlayFinish: false,
        showErrorView: false,
        durationProgress: event.extra.duration && event.extra.duration,
      });
      if (this.selectBeginTime > 0) {
        //如果这个偏移时间比实际视频时长大，Android端是无法发生偏移的，需要这里做个修正
        if (this.state.durationProgress > 0 && this.selectBeginTime > this.state.durationProgress) {
          //如果大于进度时间，可能没找到视频，找了最近的播放，那么我们从头开始播放不进行偏移
          // this.selectBeginTime = this.state.durationProgress * 1000;
          this.selectBeginTime = 0;
          return;
        }
        //  this.IMIVideoView && this.IMIVideoView.seekTo(this.selectBeginTime);
        this.selectBeginTime = 0;
      }
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING);
    } else if (
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP ||
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_RESET
    ) {
      console.log(' 直播流----_onEventChange,出现停止');
      // IMILog.logD("王 错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
      this.setState({isLoading: false, isPlay: false, showPauseView: true, isPlayFinish: true});
      if (this.state.recording) {
        //直播流暂停时，停止录像
        if (CONST.isAndroid) {
          this._stopRecord();
        } else {
          //因为IOS会自动停止视频录制，所以直接转存即可
          this._saveVideoToPhotosAlbum();
        }
      }
      // console.log(" 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
      //this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
      // console.log(" 直播流----_onEventChange,开始播放");
      // 增加99004loading
      this.setState({isLoading:true});
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE) {
      this.setState({isLoading: false, isPlay: false, showPauseView: true});
      if (this.state.recording) {
        //直播流暂停时，停止录像 安卓
        this._stopRecord(true);
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE_IOS) {
      if (this.state.recording) {
        //直播流暂停时，停止录像 IOS
        this._stopRecord(true);
        // this._saveVideoToPhotosAlbum(true);
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_END) {
      // 此处逻辑同播放器onPlayCompletion
      // console.log('播放结束了----------');
      // this.progressView && this.progressView.resetStatus();
    }
  };
  // 第二个摄像头
  _onEventChange1 = event => {
    console.log('event1:----------------',this.state.videoItem?.lensNumber, event);
    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      //this.setState({bps: event.extra.bps})
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
      //console.log(event.extra.currentTime);
      // let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;

      if (this.state.isLoading || this.state.showPauseView || !this.state.isPlay || !this.state.showErrorView||this.state.cameraNoVideo1) {
        // Iphone 12 bug: http://************:10083/issues/9393
        this.setState({
          isLoading: false,
          showPauseView: false,
          isPlay: true,
          isPlayFinish: false,
          showErrorView: false,
          cameraNoVideo1:false,
        });
      }
      if (this.state.noVideo) {
        this.progressView && this.progressView.onSliderValueChanged(0);
        this.state.homePictureTime = 0;
      } else {
        if (this.tempScrollPause) {
          //修改暂停逻辑 pause  需要resume后再seekto 防止回复在加载
          // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
          console.log(Math.round(this.tempScrollTime * 1000));
          this.tempScrollPause = false;
          //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime * 1000));
        } else {
          //202200302@byh ios最后返回的时间会比，视频时长少一秒，增加0.5修正，增加返回的视频时长与实际视频时长判断
          //Android不能添加,添加播放结束后可能在00：01秒位置
          let scrollTime = event.extra.currentTime;
          //20220318@byh 在播放完成后，监听播放完成，把进度拉满，所以这里不在需要修正
          // if (isIos()){
          //     scrollTime = scrollTime+0.5;
          // }
          let secondSTime = Math.round(scrollTime);
          let second = secondSTime > this.state.durationProgress ? this.state.durationProgress : secondSTime;

          if (second > 0&&this.state.videoItem?.lensNumber==1) {
            this.progressView && this.progressView.onSliderValueChanged(second);
          }
        }
      }
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      //调用video.start()后的第一个回调
      this.setState({
        isLoading: this.props.netConnected ? true : false,
        isPlay: false,
        showPauseView: false,
        isPlayFinish: false,
        showErrorView: false,
        cameraReady1:false,
        cameraNoVideo1:false,
      });
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      // this.IMIVideoView && this.IMIVideoView.seekTo(Math.floor(this.state.videoItem?.offsetDuration || 0));
      // this.IMIVideoView1 && this.IMIVideoView1.seekTo(Math.floor(this.state.videoItem?.offsetDuration || 0));
      // 第一帧
      this.setState({
        isLoading:!this.state.cameraReady0,
        showPauseView: false,
        isPlay: true,
        isPlayFinish: false,
        showErrorView: false,
        cameraReady1:true,
        durationProgress: event.extra.duration && event.extra.duration,
      });
      if (this.selectBeginTime > 0) {
        //如果这个偏移时间比实际视频时长大，Android端是无法发生偏移的，需要这里做个修正
        if (this.state.durationProgress > 0 && this.selectBeginTime > this.state.durationProgress) {
          //如果大于进度时间，可能没找到视频，找了最近的播放，那么我们从头开始播放不进行偏移
          // this.selectBeginTime = this.state.durationProgress * 1000;
          this.selectBeginTime = 0;
          return;
        }
        //  this.IMIVideoView && this.IMIVideoView.seekTo(this.selectBeginTime);
        this.selectBeginTime = 0;
      }
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING);
    } else if (
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP ||
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_RESET
    ) {
      console.log(' 直播流----_onEventChange,出现停止');
      // IMILog.logD("王 错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
      this.setState({isLoading: false, isPlay: false, showPauseView: true, isPlayFinish: true, cameraReady1:false,});
      if (this.state.recording) {
        //直播流暂停时，停止录像
        if (CONST.isAndroid) {
          this._stopRecord();
        } else {
          //因为IOS会自动停止视频录制，所以直接转存即可
          this._saveVideoToPhotosAlbum();
        }
      }
      // console.log(" 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
      //this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
      // console.log(" 直播流----_onEventChange,开始播放");
      // 增加99004loading
      this.setState({isLoading:true});
      this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE) {
      this.setState({isLoading: false, isPlay: false, showPauseView: true});
      if (this.state.recording) {
        //直播流暂停时，停止录像 安卓
        this._stopRecord(true);
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE_IOS) {
      if (this.state.recording) {
        //直播流暂停时，停止录像 IOS
        this._stopRecord(true);
        // this._saveVideoToPhotosAlbum(true);
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_END) {
      // 此处逻辑同播放器onPlayCompletion
      // console.log('播放结束了----------');
      // this.progressView && this.progressView.resetStatus();
    }
  };

  _onRecordTimeChange = event => {
    console.log('recordDuration', event.extra);
    if (event.extra === 0) {
      //如果为0就不进行赋值操作
      return;
    }
    this.setState({recordDuration: event.extra}, callback => {
      if (this.state.recordDuration == 1) {
        // 临时截图
        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
          this.tempSnapShotPath = currentSnapshotPath;
          // console.log('临时路径---',this.tempSnapShotPath);
        });
      }
    });
  };

  //判断当前是否可以操作
  _canStepIn() {
    if (!this.state.isPlay) {
      showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
      return false;
    }
    return true;
  }

  // 设置切换播放源
  _setVideoItem(item) {
    this.IMIVideoView?.reset();
    this.IMIVideoView1?.reset();
    this.progressView && this.progressView.resetStatus();
    console.log('切换播放源--------', item);
    this.setState(
      {
        videoItem: item,
        switchVideoItemIng: true,
      },
      () => {
        //必须要这样写，不然安卓那边的时序和我们这不一样
        this.setState({switchVideoItemIng: false});
        setTimeout(() => {
          this.IMIVideoView1?.prepare();
          this.IMIVideoView?.prepare();
          this.IMIVideoView && this.IMIVideoView.seekTo(Math.floor(item.offsetDuration || 0));
        });
      },
    );
  }
  /**
   * 竖屏状态视屏区域填充UI
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenVideoViewArea() {
    return this.props.navBar ? (
      this.props.navBar(this.state.bps, this.state.isFullScreen)
    ) : (
      <NavigationBar
        type={NavigationBar.TYPE.LIGHT}
        // backgroundColor={'transparent'}
        ref={(ref)=>this.navigationBarRef=ref}
        title={stringsTo('bottom_house_video_keeping')}
        // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
        left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
        right={[
          {
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../resources/images/icon_delete_black.png'),
            styles: {
              width: 20,
              height: 20,
            },
            onPress: _ => {
              if(!(this.state.eventVideoList.length > 0)) {
                return
              }
              if (this.state.recording) {
                showToast(stringsTo('screen_recording'));
                return;
              }
              this.setState({showDeleteTip: true});
            },
            accessibilityLabel: 'homedelete',
          },
        ]}
        rightText={ "" }
      />
    );
  }


  /**
   * 全屏状态videoView区域填充UI
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenVideoViewArea() {
    let title = speedTitleAry[this.state.speedIndex];

    let speedAccessibilityLabel = speedAccessibilityLabelTitleAry[this.state.speedIndex];
    let item = {
      isText: false,
      data: [require('../../resources/images/icon_download_white.png')],
      onPress: this._downloadPress,
      disabled: !this.state.videoItem?.segmentId,
      dataIndex: 0,
      accessibilityLabel: ['play_back_full_screen'],
    };
    let {cloudDownload} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    let moreItems = [];
    if (cloudDownload) {
      moreItems = [{item: item, insertIndex: 5}];
    }

    return (
      <View
        pointerEvents="box-none"
        style={{width: '100%', height: '100%', display: this.state.showFullScreenTools ? 'flex' : 'none'}}>
        <PlayBackFullScreenToolBarView
          exitPress={this._exitFullScreen}
          playNot={true}
          mutePress={this._onPressMute} //静音
          mute={this.state.mute}
          muteDisabled={!this.state.isPlay}
          speedTitle={title}
          speedPress={this._onPressSpeed} // 快进
          // speedDisabled={!this.state.isPlay} 目前不支持
          speedDisabled={true}
          speedAccessibilityLabel={speedAccessibilityLabel}
          screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          recordPress={this._onPressRecord}
          recording={this.state.recording}
          recordDisabled={!this.state.isPlay}
          fullScreenPress={this._exitFullScreen}
          moreItems={moreItems}
          isShowSpeedItem={false}
          // 隐藏录屏按钮
          ignoreRecord={true}
        />

        <View
          style={{
            position: 'absolute',
            left: 0,
            bottom: 10,
            right: 0,
          }}
        />
      </View>
    );
  }
 
/* 
竖屏状态下的工具栏，下载截图，全屏,camera0 摄像机0
*/
 _renderPortraitCamera0VideoViewArea(){
  if(this.state.isFullScreen){
return null
  }
return    <CloudToolBarView
          playDisabled={this.state.noVideo || this.state.todayIsNoData}
          fullscreenPress={()=>this._onPressFullScreen(0)}
          screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          recordPress={this._onPressRecord}
          recordDisabled={!this.state.isPlay}
          fullscreenDisabled={this.state.noVideo || this.state.todayIsNoData || !this.state.videoItem?.segmentId}
          recording={this.state.recording}
          moreItems={[]}
          isShowSpeedItem={false}
          // 隐藏录屏按钮
          ignoreRecord={true}
           downloadPress={ this._downloadPress}
          downloadPressDisabled={!this.state.videoItem?.segmentId||this.state.videoItem?.lensNumber!=0}
          />
 }
 /* 
竖屏状态下的工具栏，下载截图，全屏,camera1 摄像机1
*/
 _renderPortraitCamera1VideoViewArea(){
   if(this.state.isFullScreen){
return null
  }
   if(!this.state.cameraReady1){
     return null
   }
   if(this.state.cameraNoVideo1){
     return null
   }
return    <CloudToolBarView
          playDisabled={this.state.noVideo || this.state.todayIsNoData}
          fullscreenPress={()=>this._onPressFullScreen(1)}
          screenshotPress={this._onPressScreenShot1}
          screenshotDisabled={!this.state.isPlay}
          recordPress={this._onPressRecord}
          recordDisabled={!this.state.isPlay}
          
          fullscreenDisabled={this.state.noVideo || this.state.todayIsNoData || !this.state.videoItem?.segmentId}
          recording={this.state.recording}
          moreItems={[]}
          isShowSpeedItem={false}
          // 隐藏录屏按钮
          ignoreRecord={true}
          downloadPress={ this._downloadPress}
          downloadPressDisabled={!this.state.videoItem?.segmentId||this.state.videoItem?.lensNumber!=1}
          />
 }
  /**
   * 事件展示列表
   * @returns {*}
   * @private
   */
  _renderListView() {
    let dataList = this.state.eventVideoList;
    // console.log(" _renderListView dataList  -> " + JSON.stringify(dataList));

    return (
      <SafeAreaInsetsContext.Consumer >
        {(insets) => {
          const insetsBottom=insets.bottom>0?insets.bottom+10:0
          return <XFlatList
            raw={true}
            style={{
              backgroundColor: colors.page_bg,
              paddingTop: 10,
              height:kWindowHeight-kWindowWidth*9/16*2- insetsBottom-this.navigationBarRef?.getNavigationBarHeight()||100- 10
            }}
            data={dataList}

            onEndReachedThreshold={0.1}
            numColumns={1}
            onLayout={e => {
              let height = e.nativeEvent.layout.height;
              if (this.state.flatListHeight < height) {
                this.setState({flatListHeight: height});
              }
            }}
            renderItem={({item, index}) => this._renderItem(item, index)}
          />}}

      </SafeAreaInsetsContext.Consumer>
    );
  }
  _renderEmptyNewView(status, isEmpty) {
    console.log(' _renderEmptyView  isEmpty' + isEmpty + ' status ' + JSON.stringify(status));
    let icon = isEmpty ? iconEmpty : null;
    return (
      this.isListLoading ? null : <IMIDesignEmptyViewNew
        rootStyle={{height: this.state.flatListHeight}}
        defaultIcon={icon}
        defaultText={stringsTo('housekeeping_no_event')}
      />
    );
  }
  /**
   * 绘制item view    **  可以进行子类重写 **
   * @param item
   * @param index
   * @returns {*}
   * @private
   */
  _renderItem = (item, index) => {
    let {displayEventType, segmentStartTime, eventStartTime, segmentId,lensNumber} = item;
    let time = moment(segmentStartTime || eventStartTime).format('YYYY-MM-DD HH:mm:ss');
    let formatStarTime = time.substr(11);
    const iconDotMove = require('../../resources/images/pic_move.png');
    const iconDotPeople = require('../../resources/images/pic_person.png');
    const iconDotSound = require('../../resources/images/pic_sound.png');

    const iconDotFenceEnter = require('../../resources/images/pic_enter_fence.png');
    const iconDotFenceLeave = require('../../resources/images/pic_leave_fence.png');
    const iconDotVehicleDetect = require('../../resources/images/pic_detect_vehicle.png');
    const iconDotApproachVehicle = require('../../resources/images/pic_approach_vehicle.png');
    const iconDotBikeDetect = require('../../resources/images/pic_detect_bike.png');
    const iconDotApproachBike = require('../../resources/images/pic_approach_bike.png');
    const eventTypeList = displayEventType && displayEventType.split(',').filter(item => item !== 'Ignore');
    const iconDot = {
      ObjectMotion: iconDotMove,
      PeopleMotion: iconDotPeople,
      AbnormalSound: iconDotSound,
      FenceIn: iconDotFenceEnter,
      FenceOut: iconDotFenceLeave,
      CarDetected: iconDotVehicleDetect,
      PersonApproaching: iconDotApproachVehicle,
      NonMotorDetected: iconDotBikeDetect,
      PersonApproachingNonMotor: iconDotApproachBike,
    };
    const textList = {
      ObjectMotion: stringsTo('picture_change'),
      PeopleMotion: stringsTo('people_move'),
      AbnormalSound: stringsTo('soundEvent'),
      FenceOut: stringsTo('person_leave_fence'),
      FenceIn: stringsTo('person_enter_fence'),
      PersonApproaching: stringsTo('vehicle_people_near'),
      CarDetected: stringsTo('vehicle_detection'),
      PersonApproachingNonMotor: stringsTo('non_vehicle_people_near'),
      NonMotorDetected: stringsTo('non_vehicle_detection'),

    }
    const text = [];
    const newEventTypeList = [];
    Array.isArray(eventTypeList) &&
    eventTypeList.map(res => {

      text.push(textList[res]);
      newEventTypeList.push(res);
    });
    const isCurrent = this.state.videoItem?.eventStartTime == eventStartTime;

    return (
      <XView
        key={time}
        style={styles.cardList}
        onPress={() => this.handleCurrent(item)}
        accessibilityLabel={'housekeeping_item_view_' + time}>
        <View>
          {Array.isArray(newEventTypeList) && newEventTypeList.map((item,index) => {
            return <Image style={{width: 18, height: 18, marginRight: 8, marginBottom: (newEventTypeList.length === 1 || index == newEventTypeList.length - 1) ? 0 : 2}} source={iconDot[newEventTypeList[index]]} />
          })}
        </View>
        <View style={styles.leftComponent}>
          {Array.isArray(text) && text.map(res => {
            return <Text style={styles.eventItemTitle} >{res}</Text>
          })}
        </View>
        <View style={styles.leftComponent}>
          <Text style={styles.itemTime} >{formatStarTime}</Text>
        </View>
        <View style={styles.imgWrap}>
          {isCurrent && (
            <Image
              style={styles.currentPlayIcon} // 新增图标样式
              source={require('../../resources/images/icon_playing.png')} // 绑定播放图标资源
              resizeMode="contain" // 保持图标比例，避免拉伸变形
              accessibilityLabel="当前播放中" // 无障碍标签（可选，提升体验）
            />
          )}
          <PathImage2 key={time} item={item} deviceID={LetDevice.deviceID} />
        </View>
      </XView>
    );
  };

  handleCurrent = item => {
    if (!this.props.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (this.state.videoItem.eventStartTime == item.eventStartTime) {
      return;
    }
    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return;
    }
    console.log('handleCurrent---', item);
    this.setState({
      isPlay: true,
      isClickPause: false,
      isPlayFinish: false,
      showPauseView: false,
      showErrorView: false,
    });
    this._setVideoItem(item);
  };
  /**
   * 竖屏状态下半屏区域填充UI 功能按钮
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenPlayerToolBarArea(isFullScreen) {

    const eventName = this.state.eventTypeArr.find(item => {
      return item.eventType === this.state.selectEventAlarmType;
    })?.eventTypeName;
    let item = {
      isText: false,
      data: [require('../../resources/images/icon_alarm_down.png')],
      onPress: this._downloadPress,
      disabled: !this.state.videoItem?.segmentId,
      dataIndex: 0,
      accessibilityLabel: ['play_back_full_screen'],
    };
    let {cloudDownload} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    let moreItems = [];
    if (cloudDownload) {
      moreItems = [{item: item, insertIndex: 5}];
    }
    return (
      <View style={{flexDirection: 'column', display: isFullScreen ? 'none' : ''}}>

        {this._renderListView()}
        {this.props.children}
      </View>
    );
  }


  _renderRecordingView() {
    if (!this.state.recording) {
      return null;
    }
    let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : '00:00';
    return (
      <View
        style={
          this.state.isFullScreen //修改全屏状态下的录屏时间显示不准确问题
            ? {
                position: 'absolute',
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                zIndex: 999,
                // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
              }
            : {
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
              }
        }>
        <View style={{backgroundColor: '#E74D4D', opacity: 0.9, width: 6, height: 6, borderRadius: 3}} />
        <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
      </View>
    );
  }

  _loadingView() {
    if (this.state.showPauseView) {
      return;
    }
    if (!this.state.isLoading) {
      return;
    }
    if (this.state.noVideo) {
      return;
    }
    if (this.state.todayIsNoData) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#ffffff'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}>
          {stringsTo('commLoadingText')}
        </Text>
      </View>
    );
  }

  _errorView(errorCode) {
    // if (!this.state.showErrorView) {
    //   return;
    // }
    if (this.state.todayIsNoData) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={I18n.t('onPlayErrorText', {code: errorCode})}
        />

        <RoundedButtonView
          buttonText={stringsTo('error_code_common_retry')}
          buttonStyle={{
            margin: 14,
            paddingHorizontal: 15,
            height: 40,
          }}
          buttonTextStyle={{textAlign: 'center'}}
          onPress={() => {
            // ;
            console.log('视频名称:' + this.state.deleteSegmentId);
            // this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);
            // console.log('播放失败---',playEndTime);
            if (!this.props.netConnected) {
              showToast(stringsTo('network_not_connected'));
              return;
            }
            this.tempScrollPause = false;
            this.errPrepare = true;
            this.progressView && this.progressView.resetStatus();
            this.IMIVideoView?.reset();
            this.IMIVideoView?.prepare();
             this.IMIVideoView1?.reset();
            this.IMIVideoView1?.prepare();
            this.setState({showErrorView: false});
          }}
        />
      </View>
    );
  }
  _showAlarmView() {
    if (!this.props.showAlarmView && this.state.todayIsNoData) {
      return;
    }
    if (this.state.todayIsNoData && this.props.showAlarmView) {
      return (
        <View
          pointerEvents="box-none"
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <XText
            raw={true}
            style={{
              textAlign: 'center',
              color: colors.white,
              fontSize: 15,
            }}
            text={I18n.t('alarm_turn_on_str', {code: this.state.errorCode})}
          />

          <RoundedButtonView
            buttonText={stringsTo('open_setting')}
            buttonStyle={{
              margin: 14,
              paddingHorizontal: 15,
              height: 40,
            }}
            buttonTextStyle={{textAlign: 'center'}}
            onPress={() => {
              // this.props.navigation.push("HouseKeepSetting");
              if (LetDevice.model == 'a1MSKK9lmbs') {
                this.props.navigation.push('HouseKeepSettingV2');
              } else if (
                LetDevice.model == 'a1FKrifIRwH' ||
                LetDevice.model == 'a1Ikkj5vsiK' ||
                LetDevice.model == 'a1znn6t1et8'
              ) {
                // 026 021E01 036 进入新的看家界面 原生项目转RN
                this.props.navigation.push('HouseKeepOldNativeSetting');
              } else {
                this.props.navigation.push('HouseKeepSetting');
              }
            }}
          />
        </View>
      );
    }
  }

  getStatusBarHeight() {
    if (isIos()) {
      return isIphone14ProMax() ? (59 + 50) : (isIphoneXSeries() ? 47 + 50 : 20 + 50);
    } else {
      return parseInt(StatusBar.currentHeight) + 50;
    }
  }

  _noVideoView() {
    if (this.state.isLoading) {
      return
    }
    if (this.state.videoItem?.segmentId) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
            marginTop: -(this.getStatusBarHeight() + 20)
          }}
          text={I18n.t('no_video_data_new')}
        />
      </View>
    );
  }
  _noVideoViewSinglePlayer( a,b) {
    if (this.state.isLoading) {
      return
    }

    return (
      <View
        pointerEvents="box-none"
        style={{

          width: '100%',
          height:'100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',

        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={I18n.t('no_video_data_new')}
        />
      </View>
    );
  }
  _noFirstData() {
    if (this.state.todayIsNoData && this.props.showAlarmView) {
      return;
    }
    if (!this.props.showAlarmView && this.state.todayIsNoData) {
      return (
        <View
          pointerEvents="box-none"
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <XText
            raw={true}
            style={{
              textAlign: 'center',
              color: colors.white,
              fontSize: 15,
            }}
            text={I18n.t('alarm_turn_on_str', {code: this.state.cameraErrorCode0})}
          />
        </View>
      );
    }
  }

  _pauseView() {
    if (!this.state.showPauseView) {
      return null;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (this.state.noVideo) {
      return;
    }
    if (this.state.todayIsNoData) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={require('../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          highlightedSource={require('../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          onPress={() => {
            this._onPressPlay();
            // if (!this.props.netConnected) {
            //   showToast(stringsTo('network_not_connected'));
            //   return;
            // }
            // // 点击过暂停 需要
            // console.log('isPlayzhuangtai---', this.state.isPlayFinish, this.state.isClickPause);
            // console.log('this.tempScrollPause::::', this.tempScrollPause + Math.round(this.tempScrollTime * 1000));
            // if (this.tempScrollPause) {
            //   // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
            //   // this.tempScrollPause = false;
            //   // this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
            //   if (this.state.isClickPause) {
            //     this.IMIVideoView && this.IMIVideoView.resume();
            //   } else {
            //     //20220324@byh 播放完成后，拖动进度条，然后再点击播放，播放后定位到拖动位置
            //     //这个标记在这里先不置为false
            //     //this.tempScrollPause=false;
            //     this.progressView && this.progressView.resetStatus();
            //     //this.IMIVideoView && this.IMIVideoView.prepare();
            //   }
            // } else {
            //   if (this.state.isPlayFinish) {
            //     this.progressView && this.progressView.resetStatus();
            //     this.IMIVideoView && this.IMIVideoView.prepare();
            //     if (isIos()) {
            //       this.setState({isLoading: true, showPauseView: false});
            //     }
            //   } else {
            //     if (this.state.isClickPause) {
            //       this.IMIVideoView.resume();
            //     } else {
            //       this.progressView && this.progressView.resetStatus();
            //       //this.IMIVideoView && this.IMIVideoView.prepare();
            //     }
            //   }
            // }

            // // this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
            // this.props.fileNameStatusChangeListener && this.props.fileNameStatusChangeListener(1);
            // if (this.state.isClickPause) {
            //   this.setState({isPlay: true, showPauseView: false, isClickPause: false});
            // } else {
            //   this.setState({isClickPause: false});
            // }
          }}
        />
      </View>
    );
  }

  _renderSnapshotView() {
    if (!this.state.snapshotVisible) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          bottom: this.state.isFullScreen ? 106 : 19,
          left: isPhoneX() ? (this.state.isFullScreen ? 44 + 14 : 14) : 14,
          width: 140,
          height: 80,
          zIndex: 999,
        }}>
        <ImageButton
          style={{
            width: '100%',
            height: '100%',
            borderWidth: 2,
            borderColor: 'white',
            borderRadius: 10,
          }}
          accessibilityLabel={'cloud_storage_page_picture_show'}
          source={{uri: 'file://' + this.state.screenShotPath}}
          onPress={_ => {
            //TODO 跳转到相册预览？
            if (this.state.recording) {
              showToast(stringsTo('screen_recording'));
              return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({snapshotVisible: false});
            this.isView = true;
            if (this.state.screenShotPathType === 1) {
              this.props.navigation.push('PhotoView', {
                imageUrl: 'file://' + this.state.screenShotPath,
                isFullScreen: this.state.isFullScreen,
              });
            } else {
              this.props.navigation.push('VideoPreView', {
                mediaData: {url: 'file://' + this.state.screenShotPath},
                isFullscreen: this.state.isFullScreen,
              });
            }

            // if(this.state.isFullScreen){ //横屏则退出全屏
            //     Orientation.lockToPortrait();
            //     this.props.navigation.setOptions({tabBarVisible: true});
            //     this._onCloseFullScreenTools();
            //     NavigationBar.setStatusBarHidden(false);
            //     this.setState({isFullScreen: false},()=>{
            //         // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            //     this.goToAlbum();
            //     });
            //     return;
            // }
            // // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            // this.goToAlbum();
          }}
        />
      </View>
    );
  }

  goToAlbum() {
    let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    if (showRNAlbum || LetDevice.model === 'a1l4Z7lJ1ns' || LetDevice.model === 'a1yMb5JVWDa') {
      this.props.navigation.push('CameraListPage');
    } else {
      IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    }
  }

  deletImageArray() {
    aliAlarmEventCloudApi
      .deleteFileBySegment(LetDevice.deviceID, [this.state.videoItem.segmentId])
      .then(data => {
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_success'));
        this.props.route?.params?.refreshList && this.props.route.params.refreshList();
        this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
      })
      .catch(error => {
        console.log('删除失败', JSON.stringify(error));
        IMILog.logI('删除失败', JSON.stringify(error));
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_failed'));
      });
  }

  /**
   *
   * @returns {JSX.Element|null}
   * @private
   * 进度条
   */
  renderVideoProgressView() {
    if (this.state.snapshotVisible) {
      return null;
    }
    if (this.state.noVideo) {
      return null;
    }
    if (this.state.todayIsNoData) {
      return null;
    }
    const widthProgress=this.state.isFullScreen?kWindowHeight: kWindowWidth
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          width:widthProgress, //NKGT-127
          bottom: 1,
       
          zIndex: 999,
          flexDirection:'row',
         
          opacity: this.state.isFullScreen && !this.state.showFullScreenTools ? 0 : 1,
        }}>
        <TouchableOpacity
          style={{justifyContent: 'center', alignItems: 'center', opacity:  1}}
          onPress={this._onPressPlay}
        >
          <Image style={{width: 40, height: 40, opacity:  1}}
                 source={this.state.isPlay?require('../../../../imi-rn-commonView/PlayerToolBarView/res/icon_pause_white.png'):
                   require('../../../../imi-rn-commonView/PlayerToolBarView/res/icon_play_white.png')
                 }/>

        </TouchableOpacity>
         
        <VideoProgressView
          ref={ref => (this.progressView = ref)}
          isPlayFromBeginning={this.state.isPlayFromBeginning}
          duration={this.state.durationProgress}
          recording={this.state.recording}
          isPlayFinish={this.state.isPlayFinish}
          onProgressValueChanged={this.onProgressChanged}
          switchVideoItemIng={this.state.switchVideoItemIng}
          style={{width:widthProgress-100,position:'',backgroundColor:'transparent',}}
        />
        <TouchableOpacityImage
          style={{justifyContent: 'center', alignItems: 'center',  opacity:  1}}
              onPress={()=>this._onPressMute}
              disabled={!this.state.isPlay}
              imageStyle={{width: 40, height: 40, opacity:  1}}
              source={this.state.mute? 
                require('../../../../imi-rn-commonView/PlayerToolBarView/res/icon_live_mute_on.png')
              :require('../../../../imi-rn-commonView/PlayerToolBarView/res/landscape_mute.png')}
        />
      </View>
    );
  }
  onProgressChanged = currentTime => {
    console.log('currentTime', currentTime + ':::' + this.state.isPlay);

    // console.log('当前iOS播放状态---',this.state.isPlay);
    if (!this.state.isPlay) {
      // 暂停状态
      this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
        this.tempScrollPause = true;
        this.tempScrollTime = currentTime;
        this.IMIVideoView && this.IMIVideoView.resume();
        this.IMIVideoView1 && this.IMIVideoView1.resume();
      });
    }
    // this.setState({seekTo: Math.round(currentTime)});
    this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(currentTime));
    this.IMIVideoView1 && this.IMIVideoView1.seekTo(Math.round(currentTime));
  };


  /*告知外部调用者监听状态*/
  getFullscreen() {
    return this.state.isFullScreen;
  }
  getCurrentStatus() {
    return this.isView;
  }
  setLoading(flag) {
    this.setState({
      isLoading: flag
    })
  }
  /*退出全屏状态*/
  quitFullScreen() {
    Orientation.lockToPortrait();
    this.setState({isFullScreen: false,cameraIsFull0: false,cameraIsFull1: false});
    NavigationBar.setStatusBarHidden(false);
    this.props.navigation.setOptions({tabBarVisible: true});
    this._onCloseFullScreenTools();
    console.log('云存页面调用退出全屏方法');
  }

  //获取录屏状态
  getRecordStatus() {
    return this.state.recording;
    // console.log('云存获取当前是否录像',this.state.recording);
  }


  // 截图成功回调
  _onCommCallback = event => {
    IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.deviceID)
      .then(_ => {
        this.setState({
          screenShotPathType: 1,
          screenShotPath: _.data,
          snapshotVisible: true,
        });
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
      })
      .catch(e => {
        IMIToast.showToast(stringsTo('action_fail'), IMIToast.TYPE.BOTTOM);
      });
  };

  render() {
    let showModel =
      LetDevice.model == 'a1Od0SjKPGt' ||
      LetDevice.model == 'a1Godgpvr3D' ||
      LetDevice.model == 'a1zcQKoHQ83' ||
      LetDevice.model == 'a1QRbHvcYBd';
    const VideoHeight=kWindowWidth*9/16;
    const pointerEventsCamera0 = (this.state.isFullScreen && this.state.cameraIsFull1)? 'box-only': 'auto'
    const pointerEventsCamera1 = (this.state.isFullScreen && this.state.cameraIsFull0)? 'box-only': 'auto'
    let camera0Height =this.state.isFullScreen&&this.state.cameraIsFull0?kWindowWidth:VideoHeight
    let camera1Height = this.state.isFullScreen&&this.state.cameraIsFull1?kWindowWidth:VideoHeight;
    let camera0Width =this.state.isFullScreen&&this.state.cameraIsFull0?kWindowHeight:kWindowWidth
    let camera1Width = this.state.isFullScreen&&this.state.cameraIsFull1?kWindowHeight:kWindowWidth ;
    const smallWindowStyle = this.state.isFullScreen
      ? {
        position: 'absolute',
        right: 0,
        top: 60,
        width: 195+4,
        height: 111+4,
        zIndex: 2,
        borderRadius: 5,
        borderWidth: 2,
        borderColor: '#ffffffff',
        transform: [{translateX: this.pan.x}, {translateY: this.pan.y}],
      }
      : {};
    if(this.state.isFullScreen){
      if(this.state.cameraIsFull1){
        camera0Height=111
        camera0Width=195
      }
      if(this.state.cameraIsFull0){
        camera1Height=111
        camera1Width=195
      }
    }


    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        // pointerEvents="box-none"
        style={{flex:1, backgroundColor: '#FFFFFF', flexDirection: 'column'}}>

        {this.state.isFullScreen?null:this._renderPortraitScreenVideoViewArea()}
        <XView
          activeOpacity={1}
          style={{ }}
          onPress={() => {
            this.state.isFullScreen && this.state.showFullScreenTools
              ? this._onCloseFullScreenTools()
              : this._onPressFullScreenTools();
          }}
        >
          <Animated.View style={[{position:'relative',
            height: camera0Height,

          }, this.state.cameraIsFull0 ? {} : smallWindowStyle,]}
                         pointerEvents={pointerEventsCamera0}
                         {...( this.state.isFullScreen && this.state.cameraIsFull1 ? this.panResponder.panHandlers : {})}
          >
            <IMCloudServerVideoView
              style={[{ height:camera0Height}]}
              ref={ref => {
                if (!this.IMIVideoView) {
                  this.IMIVideoView = ref;
                  this.IMIVideoView?.prepare();
                }
                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
              }}
              mute={this.state.mute} // 初始化必须为undefined
              speed={this.state.speed} // 初始化必须为undefined
              // seekTo={this.state.seekTo} // 初始化必须为undefined
              playerClass={IMICameraVideoView.PlayerClass.HLS}
              dataSource={{
                productId: LetDevice.model,
                iotId: LetDevice.deviceID,
                segmentId:this.state.videoItem?.lensNumber==0? this.state.videoItem?.segmentId:'',
                playerClass: IMICameraVideoView.PlayerClass.HLS,
                publicKeyVersion: this.state.videoItem?.publicKeyVersion,
                lensNumber:'0',
                startTime:this.state.videoItem?.lensNumber==0?null: `${this.state.videoItem?.eventStartTime}`,
              }}
              onPrepared={() => {
                this.progressView && this.progressView.resetStatus();
                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
              }}
              // onVideoViewClick={() => {
              //   this.state.isFullScreen && this.state.showFullScreenTools
              //     ? this._onCloseFullScreenTools()
              //     : this._onPressFullScreenTools();
              //   this.props.onVideoClick && this.props.onVideoClick();
              // }}
              onEventChange={this._onEventChange}
              onPlayCompletion={() => {
                // 播放完成
                this.progressView && this.progressView.resetStatus();
              }}
              onErrorChange={event => {
                console.log("eventevent0",event.extra.arg3,21504);
                if(event?.extra?.arg3==21504){
                  this.setState({cameraReady0:true,cameraNoVideo0:true,})
                  return
                }
                this.setState({
                  isPlay: false,
                  cameraShowErrorView0: true,
                  showPauseView: false,
                  isLoading: false,
                  errorCode0: event?.extra?.arg1 + '(' + event?.extra?.arg3 + ')'
                });
                if (this.state.recording) {
                  //直播流暂停时，停止录像
                  if (CONST.isAndroid) {
                    this._stopRecord();
                  } else {
                    //因为IOS会自动停止视频录制，所以直接转存即可
                    this._saveVideoToPhotosAlbum();
                  }
                }
                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
              }}
              onRecordTimeChange={this._onRecordTimeChange}
              onCommCallback={this._onCommCallback}
            />
            <View style={{position:'absolute',top:1,width:kWindowWidth}}>
              {this._renderPortraitCamera0VideoViewArea()}
            </View>
            <View style={{position:'absolute',bottom:1, width:camera0Width,height:camera0Height}}>
              {this.state.cameraNoVideo0?this._noVideoViewSinglePlayer():null}
              {this.state.cameraShowErrorView0?this._errorView(this.state.errorCode0):null}
            </View>
          </Animated.View>
          <Animated.View style={[{position:'relative',
            height: camera1Height,

          }, this.state.cameraIsFull1 ? {} : smallWindowStyle,]}
                         pointerEvents={pointerEventsCamera1}
                         {...( this.state.isFullScreen && this.state.cameraIsFull0 ? this.panResponder.panHandlers : {})}
          >
            <IMCloudServerVideoView
              style={{ height:camera1Height}}
              ref={ref => {
                if (!this.IMIVideoView1) {
                  this.IMIVideoView1 = ref;
                  this.IMIVideoView1?.prepare();
                }
                // this.props.videoRef && this.props.videoRef(this.IMIVideoView1);
              }}
              mute={this.state.mute} // 初始化必须为undefined
              speed={this.state.speed} // 初始化必须为undefined
              // seekTo={this.state.seekTo} // 初始化必须为undefined
              playerClass={IMICameraVideoView.PlayerClass.HLS}
              dataSource={{
                productId: LetDevice.model,
                iotId: LetDevice.deviceID,
                segmentId:this.state.videoItem?.lensNumber==1? this.state.videoItem?.segmentId:'',
                playerClass: IMICameraVideoView.PlayerClass.HLS,
                publicKeyVersion: this.state.videoItem?.publicKeyVersion,
                lensNumber:'1',
                startTime:this.state.videoItem?.lensNumber==1?null: `${this.state.videoItem?.eventStartTime}`,
              }}
              // onPrepared={() => {
              //   this.progressView && this.progressView.resetStatus();
              //   this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
              // }}
              // onVideoViewClick={() => {
              //   this.state.isFullScreen && this.state.showFullScreenTools
              //     ? this._onCloseFullScreenTools()
              //     : this._onPressFullScreenTools();
              //   this.props.onVideoClick && this.props.onVideoClick();
              // }}
              onEventChange={this._onEventChange1}
              // onPlayCompletion={() => {
              //   // 播放完成
              //   this.progressView && this.progressView.resetStatus();
              // }}
              onErrorChange={event => {
                // 这个错误码代表没有数据，不要错误弹窗

                console.log("eventevent1",event);

                if(event?.extra?.arg3==21504){
                  this.setState({cameraReady1:true,cameraNoVideo1:true,})
                  return
                }
                this.setState({
                  isPlay: false,
                  cameraShowErrorView1: true,
                  showPauseView: false,
                  isLoading: false,
                  errorCode1: event?.extra?.arg1 + '(' + event?.extra?.arg3 + ')'
                });
                if (this.state.recording) {
                  //直播流暂停时，停止录像
                  if (CONST.isAndroid) {
                    this._stopRecord();
                  } else {
                    //因为IOS会自动停止视频录制，所以直接转存即可
                    this._saveVideoToPhotosAlbum();
                  }
                }
                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
              }}
              onRecordTimeChange={this._onRecordTimeChange}
              onCommCallback={this._onCommCallback}
            />


            <View style={{position:'absolute',top:1,width:kWindowWidth}}>
              {this._renderPortraitCamera1VideoViewArea()}
            </View>
            <View style={{position:'absolute',bottom:1, width:camera1Width,height:camera1Height}}>
              {this.state.cameraNoVideo1?this._noVideoViewSinglePlayer():null}
              {this.state.cameraShowErrorView1?this._errorView(this.state.errorCode1):null}
            </View>
          </Animated.View>
          {/*全屏?横屏UI:竖屏UI(navBar)*/}
          <View
            pointerEvents="box-none"
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            {this._renderSnapshotView()}

            {this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()}
            {/* {this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()} */}
            {this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()}
            {this.state.isFullScreen
              ? this._renderLandscapeScreenVideoViewArea()
              : null}
            {this._renderRecordingView()}
            {this._showAlarmView()}
            {this._noFirstData()}
            {this._noVideoView()}
            {/* 进度条 */}
            {this.renderVideoProgressView()}
          </View>
        </XView>
        {/*全屏?null:渲染外层传入的UI*/}
        {this._renderPortraitScreenPlayerToolBarArea(this.state.isFullScreen)}

        <MessageDialog
          title={stringsTo('delete_alert_tip') + this.state.eventVideoList.length + stringsTo('delete_alert_tip1')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showDeleteTip: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteAlertVideo',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('delete_title'),
              accessibilityLabel: 'okDeleteAlertVideo',
              callback: _ => {
                showLoading(stringsTo('delete_title_loading'), true);
                this.setState({showDeleteTip: false});
                this.deletImageArray(0);
              },
            },
          ]}
        />

      </View>
    );
  }
}


const PathImage2 = ({item, deviceID}) => {
  const [path, setPath] = useState('');
  // console.log("yyh PathImage2:" + item + " deviceID:" + deviceID);
  //需要照顾数据格式的不同
  const {segmentId, publicKeyVersion, imgSignedDownloadUrl} = item;
  useEffect(() => {
    const getImageUrl = async () => {
      const data = await cloudDeviceService
        .downLoadImage(deviceID, segmentId, publicKeyVersion, imgSignedDownloadUrl)
        .catch(e => {
          console.log(11, e);
        });
      if (data) {
        setPath(data);
      }
    };
    getImageUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // if (isEmpty(path)) {
  //   return <IMIImageView style={styles.itemIcon} source={require('../../resources/images/play_back_comm_bg.png')} />;
  // }
  return <IMIImageView style={styles.eventItemIcon} source={{uri: isIos() ? isEmpty(path) ? require('../../resources/images/play_back_comm_bg.png') : `file://${path}` : `file://${path}`}} />;
};
const PathImage = ({imgSignedDownloadUrl, deviceID, isDelete,segmentId:segmentId1,publicKeyVersion:publicKeyVersion1,imagePath}) => {
  const [path, setPath] = useState('');
  const {segmentId, publicKeyVersion, imgPath} = customJsonParse(imgSignedDownloadUrl);
  // console.log("dsdsdsdsdds",segmentId,segmentId1,imgPath,imgSignedDownloadUrl,publicKeyVersion,publicKeyVersion1);
  
  useEffect(() => {
    const getImageUrl = async () => {
      // IMILog.logI('加载云存图片', JSON.stringify({imgSignedDownloadUrl: imgSignedDownloadUrl}));
      const data = await cloudDeviceService.downLoadImage(deviceID, segmentId||segmentId1, publicKeyVersion||publicKeyVersion1, imgPath||imagePath).catch(e => {
        console.log(e,"getImageUrl");
      });
      if (data) {
        if(isIos()){
          IMILog.logI('加载云存图片返回值', JSON.stringify({imgLoad: data}));
        }
        setPath(data);
      }
    };
    getImageUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <IMIImageView
      style={isDelete ? styles.itemDeleteIcon : styles.itemIcon}
      imageUrl={isIos() ? isEmpty(path) ? require('../../resources/images/play_back_comm_bg.png') : `file://${path}` : `file://${path}`} /* source={{uri: }} */
    />
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    //position: 'absolute',
  },

  itemParent: {
    alignItems: 'center',
    paddingLeft: 14,
    paddingTop: 14,
    // flexDirection: 'row',
    backgroundColor: colors.white,
  },

  itemIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 3,
    height: (((windowWidth - 4 * 14) / 3) * 57) / 101.33,
  },
  itemDeleteIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 3,
    height: (((windowWidth - 4 * 14) / 3) * 57) / 101.33,
    opacity: 0.9,
  },
  noVideoImage: {width: '100%', height: (windowWidth * 9) / 16},
  noVideoImageFullscreen: {width: '100%', height: '100%'},
  itemIconCenter: {
    position: 'absolute',
    width: 30,
    height: 30,
    left: (windowWidth - 4 * 14) / 3 / 2,
    top: (((windowWidth - 4 * 14) / 3) * 57) / 101.33 / 2,
  },
  itemTitle: {
    fontSize: 12,
    // flex: 1,
    // width:(windowWidth-4*14)/3-14*3,
    // fontWeight: 'bold',
    color: colors.black,
    // paddingLeft: 14
  },

  itemDesc: {
    fontSize: 12,
    paddingLeft: 14,
    // marginTop: 10,
    color: colors.black,
  },
  videoToolBtn: {
    marginHorizontal: 15,
    width: 30,
    height: 30,
  },
  dialogContent: {
    paddingVertical: 10,
  },
  cardList: {
    flex: 1,
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    // height: 80,
    paddingTop: 12,
    paddingBottom: 12,
    paddingLeft: 16,
    paddingRight: 16,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  leftComponent: {
    flex: 1,
    justifyContent: 'center',
  },
  eventItemIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 4,
    height: ((windowWidth - 4 * 14) / 4 / 16) * 9,
  },
  eventItemTitle: {
    fontSize: 12,
    lineHeight: 18,
    fontWeight: 'bold',
    color: colors.gray,
  },

  itemTime: {
    fontSize: 10,
    color: colors.gray,
    // marginTop: 2,
    fontWeight: 'bold',
  },
  imgWrap: {
    width: (windowWidth - 4 * 14) / 4,
    height: ((windowWidth - 4 * 14) / 4 / 16) * 9,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentPlayIcon: {
    position: 'absolute',
    zIndex: 99, // 确保图标在最上层（不被封面图遮挡）
    width: 30, // 图标宽度（根据需求调整，建议用具体数值）
    height: 30, // 图标高度（与宽度一致，避免图标变形）
  },
});
