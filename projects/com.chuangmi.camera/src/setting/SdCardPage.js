import React, {PureComponent} from 'react';
import {StyleSheet, View, Image, Text, Dimensions, ActivityIndicator, ScrollView, ImageBackground} from 'react-native';
import {ListItem, RoundedButtonView,Separator} from '../../../../imilab-design-ui'
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar"
import {LetDevice} from "../../../../imilab-rn-sdk";
import {showToast, MessageDialog, showLoading,ChoiceItem} from "../../../../imilab-design-ui";
import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";

import I18n, {stringsTo} from "../../../../globalization/Localize";
import {EmptySDCarView} from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/sdcard/EmptySDCarView"
//import LinearGradient from 'react-native-linear-gradient';

export default class SdCardPage extends PureComponent {

    constructor(props, context) {
        super(props, context);
        this.state = {
            storageTotalCapacity:0, //SD卡总容量
            storageRemainCapacity:0, //SD卡剩余容量
            storageUsedCapacity:0, //SD卡已用容量
            usedVolumeStickWidth:0, //表示SD卡已用容量的View长度
            sdCardStatus:-1, //SD卡状态，-1表示数据加载中
            dialogVisible:false, //格式化SD卡对话框的可见性
            injectDialogVisible:false, //弹出SD卡确认对话框的可见性
            loopQueryType:0, //0:初始化 1:退出SD卡 2:格式化SD卡
            showCalibrationModal:false,//录制模式弹窗
            storageRecordMode:2,//录制模式
            currenStorageRecordMode:2,// 录制模式
        }
    }
    UNSAFE_componentWillMount() {
        LetDevice.sendDeviceServerRequest("GetSDCardStatus", JSON.stringify({})).then((data) => {
            console.log(' GetSDCardStatus  then-> ' + JSON.stringify(data));
        }).catch((error) => {
            console.log('GetSDCardStatus error ' + error);
        });
    }

    componentDidMount() {
        // this.getRecordMode()
        this.getSdStatusAndStorageInfo(this.state.loopQueryType);
        this.intervalQuery = setInterval(() => this.getSdStatusAndStorageInfo(this.state.loopQueryType), 2500);

    }

    componentWillUnmount() {
        this.intervalQuery && clearInterval(this.intervalQuery);
    }
    //获取摄像机录制模式
    getRecordMode() {
        LetDevice.getPropertyCloud('StorageRecordMode').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log('录制模式' + value);
            this.setState({storageRecordMode:value,currenStorageRecordMode:value});
        })

        }

    /*每隔2.5秒查询一次SD卡状态和容量信息*/
    getSdStatusAndStorageInfo(type = 0) {
        LetDevice.getPropertyCloud('StorageStatus').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log('getPropertyCloud---------' + value + typeof (value));
            value = parseInt(value);
            if (value == 0) { //未插卡
                this.setState({sdCardStatus: value});
                if (type == 1) {
                    showToast(I18n.t("injectSuccess"));
                    this.setState({loopQueryType: 0});
                }
            } else if (value == 1 ||value == 2) { //正常使用中 和 未格式化(格式化失败为此状态)

                LetDevice.updateAllPropertyCloud().then((data) => {
                    let dataPackage = JSON.parse(data);
                    let stateProps = {};
                    stateProps.sdCardStatus = value;

                    if (dataPackage.StorageTotalCapacity) {
                        let totalCapacity = dataPackage.StorageTotalCapacity.value;
                        stateProps.storageTotalCapacity = (totalCapacity / 1000).toFixed(2);
                    }

                    if (dataPackage.StorageRemainCapacity) {
                        let remainCapacity = dataPackage.StorageRemainCapacity.value;
                        stateProps.storageRemainCapacity = (remainCapacity / 1000).toFixed(2);
                    }

                    if (stateProps.storageTotalCapacity != 0 && stateProps.storageRemainCapacity != 0) {
                        stateProps.storageUsedCapacity = (stateProps.storageTotalCapacity - stateProps.storageRemainCapacity).toFixed(2);
                    }

                    if (stateProps.storageUsedCapacity >= 0.01) { //格式化后，可用存储也许不为0，允许10MB偏差
                        let screenWidth = Dimensions.get('window').width;
                        stateProps.usedVolumeStickWidth = (screenWidth - 60) * stateProps.storageUsedCapacity / stateProps.storageTotalCapacity;
                        if (stateProps.usedVolumeStickWidth < 15) { //View宽度不够15，设置为15，防止绘制圆角View失败
                            stateProps.usedVolumeStickWidth = 15;
                        }
                        if (stateProps.usedVolumeStickWidth > screenWidth - 60) {
                            stateProps.usedVolumeStickWidth = screenWidth - 60;
                        }
                    } else {
                        stateProps.usedVolumeStickWidth = 0;
                        if (value==1&&type == 2) { //卡状态正常，已用内容为0，且处于格式化后的轮询状态，则格式化成功
                            showToast(I18n.t("sdcard_format_success"));
                            this.setState({loopQueryType: 0});
                            showLoading(false);
                        }
                    }

                    if(value==2&&type==2){ //状态为未格式化，且处于格式化后的轮询状态，则格式化失败
                        showToast(I18n.t("sdcard_format_fail"));
                        this.setState({loopQueryType: 0});
                        showLoading(false);
                    }

                    console.log("物模型云端数据--------", stateProps);
                    this.setState(stateProps);
                }).catch(error => {
                    console.log(JSON.stringify(error))
                });

            } else if (value == 3) { //正在格式化
                if (type != 2) { //格式化时轮循到，不弹提示，因为有loading框
                    showToast(I18n.t("sdCardFormating"));
                }

            } else if(value == 4){ //SD卡损坏
                this.setState({sdCardStatus: value});

            }

        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    getRecordModeStatus(){
        let comment = "";
        if (this.state.storageRecordMode == 1) {
            comment = stringsTo('setting_record_model_move');
        } else if (this.state.storageRecordMode == 2) {
            comment = stringsTo('setting_record_model_always');
        } else {
            comment = stringsTo('setting_record_model_close');
        }
        return comment;
    }

    /*弹出SD卡*/
    _injectSdCard(){
        LetDevice.sendDeviceServerRequest("UmountStorageMedium", JSON.stringify({})).then((data) => {
            console.log(' UnmountStorageMedium  then-> ' + JSON.stringify(data));
            this.setState({loopQueryType:1,injectDialogVisible: false});
        }).catch((error) => {
            // alert('UnmountStorageMedium error ' + error)
            this.setState({injectDialogVisible: false});
            showToast(I18n.t("injectFailed"));
        });

    }

    /*格式化SD卡*/
    _formatSdCard(){
        showLoading(stringsTo('sdCardFormating'), true);
        LetDevice.sendDeviceServerRequest("FormatStorageMedium", JSON.stringify({})).then((data) => {
            console.log(' FormatStorageMedium  then-> ' ,JSON.stringify(data));
            this.setState({loopQueryType:2,dialogVisible:false});
        }).catch((error) => {
            console.log('FormatStorageMedium error ' + error)
            this.setState({dialogVisible:false});
            showToast(I18n.t("sdcard_format_fail"));
            showLoading(false);
        });

    }

    /*页面加载中*/
    _renderLoading(){
        if (this.state.sdCardStatus != -1) {
            return null;
        }
        return (<View style={styles.loadingContainer}>
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#496EE0"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: "black",
                    fontSize: 15,
                    marginTop:20
                }}>
                {stringsTo("commLoadingText")}</Text>
        </View>);
    }

    /*未插卡时SD卡页面*/
    _renderEmptySdCard() {
        if (this.state.sdCardStatus != 0) {
            return null;
        }
        return (
            <EmptySDCarView defaultIcon={require("../../resources/images/lookback_pic_empty.png")}
                            defaultText={stringsTo('storage_no_sdcard_please_buy')}
                            subText={stringsTo('no_sd_card_tips_text')}/>
        )
    }


    /*SD卡正常使用中的页面和未格式化时的页面*/
    _renderNormalSdCard(){
        if (this.state.sdCardStatus != 1&&this.state.sdCardStatus != 2) {
            return null;
        }
        return(
            <View style={{flex: 1}}>
                <View  style={styles.sdVolumeContainer}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/icon_sd_card.png')}/>

                    <View style={styles.volumeStickContainer}>
                        <View style={[styles.volumeStickStyle,{width:this.state.usedVolumeStickWidth}]}/>
                    </View>

                    <View style={styles.textContainer}>
                        <Text style={styles.volumeTextStyle}
                              numberOfLines={1}>{I18n.t('sdCardRemain', {code: this.state.storageUsedCapacity})}</Text>
                        <Text style={[styles.volumeTextStyle,{textAlign:'right'}]}
                              numberOfLines={1}>{I18n.t('sdCardTotal', {code: this.state.storageTotalCapacity})}</Text>
                    </View>
                </View>

                <ListItem title={I18n.t('injectSdCardTitle')} subtitle={I18n.t('injectSdCardSubtitle')}
                          onPress={() => this.setState({injectDialogVisible: true})}/>


                <View style={{width: "100%", position: "absolute", bottom: 0}}>
                    <RoundedButtonView buttonText={I18n.t('sdCardFormat')}
                                       buttonStyle={{margin: 14,backgroundColor:"#F2F2F2"}}
                                       buttonTextStyle={{fontSize:15,color:"#E74D4D"}}
                                       onPress={() => {
                                           if (this._isShareUser())return;
                                           this.setState({dialogVisible:true});
                                       }}/>
                </View>
            </View>
        );


    }

    // 新的存储卡页面
    _renderNormalSdCardNew() {
        if (this.state.sdCardStatus != 1&&this.state.sdCardStatus != 2) {
            return null;
        }
        return(
            <View style={{width:'100%',backgroundColor:'#F7F7F7'}}>
                <View  style={styles.sdcardNewStyle}>
                    <ImageBackground style={{display: "flex", width: 208, height: 208, flexDirection: "column",alignItems:"center"}}
                                     source={this.state.sdCardStatus == 4 ? require('../../resources/images/sd_error.png') : require('../../resources/images/sdcard_normal.png')} >
                            <Text style={styles.stateCoverTitle}
                                  numberOfLines={1}>{I18n.t('sdcard_status_normal_new')}</Text>
                            <View style={styles.stateCoverSeprate}></View>
                            <Text style={styles.stateCoverDetail}>{I18n.t('sdcard_status_more')}</Text>
                            <Text style={styles.stateCoverDetail}>{this.state.storageRemainCapacity+'GB'}</Text>
                    </ImageBackground>
                </View>
                <View style={{height:14,backgroundColor: "#F1F1F1"}} />

                <ListItem title={stringsTo('setting_record_model')} value={this.getRecordModeStatus()} onPress={() => {
                    if (this._isShareUser())return;
                    this.setState({showCalibrationModal: true},callback=>{
                        console.log('显示录制模式弹窗')
                    });
                }}/>

                <View style={{height:14,backgroundColor: "#F1F1F1"}} />

                <ListItem title={stringsTo('sdCardFormat')} onPress={() => {
                    if (this._isShareUser())return;
                    this.setState({dialogVisible:true});
                }}/>
                <ListItem title={stringsTo('sdcard_exit')} onPress={() => {
                    if (this._isShareUser())return;
                    this.setState({injectDialogVisible: true})
                }}/>
                <View style={{width:'100%',height:130,backGroundColor:'#F7F7F7'}}>
                    <Text style={{marginLeft:14,marginTop:14,marginRight:14,color:'#7F7F7F',fontSize:'12'}}>{I18n.t('sdcard_tip1')}</Text>
                    <Text style={{marginLeft:14,marginTop:6,marginRight:14,color:'#7F7F7F',fontSize:'12'}} numberOfLines={2}>{I18n.t('sdcard_tip2')}</Text>
                </View>

            </View>
        );
    }

    /*SD异常的UI*/
    _renderAbnormalSdCard(){
        if (this.state.sdCardStatus !=4) {
            return null;
        }
        return(
            <View style={{flex: 1}}>
                <View  style={[styles.sdVolumeContainer,{backgroundColor:"#AAAAAA"}]}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/icon_sd_card_destroy.png')}/>

                    <View style={[styles.volumeStickContainer,{backgroundColor:"#FFFFFF"}]}>
                        <View style={[styles.volumeStickStyle,{width:this.state.usedVolumeStickWidth,backgroundColor:"#CECECE"}]}/>
                    </View>

                        <Text style={styles.destroyHintTextStyle}
                              numberOfLines={1}>{I18n.t('sdcard_out_already')}</Text>
                </View>

            </View>
        );

    }

     /*录制模式弹窗*/
    _renderRecordAlert() {
        if(!this.state.showCalibrationModal){
            return null;
        }
        return(<MessageDialog
            title={I18n.t("setting_record_model")}
            message={''}
            visible={this.state.showCalibrationModal}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    accessibilityLabel: 'cancelSetRecordAlert',
                    callback: _ => this.setState({showCalibrationModal:false})
                },
                {
                    text: I18n.t("ok_button"),
                    accessibilityLabel: 'okSetRecordAlert',
                    callback: _ => {
                        this.setState({showCalibrationModal: false});
                        LetDevice.setPropertyCloud({"StorageRecordMode": this.state.currenStorageRecordMode}).then(data => {
                            this.setState({storageRecordMode: this.state.currenStorageRecordMode});
                        }).catch(error => {
                            this.setState({currenStorageRecordMode: this.state.storageRecordMode});
                        });
                    }
                }
            ]}
            onDismiss={()=>this.setState({showCalibrationModal:false})}
        >
                <ChoiceItem title={stringsTo('setting_record_model_always')} subtitle={stringsTo('setting_record_model_always_title')}
                            containerStyle={{margin: 14, marginTop:0,height: 90}}
                            checked={this.state.currenStorageRecordMode==2?true:false}
                            onValueChange={(value) => {
                                this.setState({
                                    currenStorageRecordMode: value?2:this.state.storageRecordMode,
                                })
                            }}/>
                <ChoiceItem title={stringsTo('setting_record_model_move')} subtitle={stringsTo('setting_record_model_move_title')}
                            containerStyle={{margin: 14, marginTop:0,height: 90}}
                            checked={this.state.currenStorageRecordMode==1?true:false}
                            onValueChange={(value) => {
                                this.setState({
                                    currenStorageRecordMode: value?1:this.state.storageRecordMode,
                                })
                            }}/>
                <ChoiceItem title={stringsTo('setting_record_model_close')} subtitle={stringsTo('setting_record_model_close_title')}
                            containerStyle={{margin: 14, marginTop:0,height: 90}}
                            checked={this.state.currenStorageRecordMode==0?true:false}
                            onValueChange={(value) => {
                                this.setState({
                                    currenStorageRecordMode: value?0:this.state.storageRecordMode,
                                })
                            }}/>
            </MessageDialog>
        );
    }

    /*格式化SD卡二次确认对话框*/
    _rendDialog(){
        if(!this.state.dialogVisible){
            return null;
        }
        return(<MessageDialog
            title={I18n.t("formatTitle")}
            message={I18n.t("formatMessage")}
            visible={this.state.dialogVisible}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    accessibilityLabel: 'cancelSetCardFormat',
                    callback: _ => this.setState({dialogVisible:false})
                },
                {
                    text: I18n.t("ok_button"),
                    accessibilityLabel: 'okSetCardFormat',
                    callback: _ => this._formatSdCard()
                }
            ]}
            onDismiss={()=>this.setState({dialogVisible:false})}
        />);
    }

    _renderInjectDialog(){
        return(<AlertDialog
            showTitle={false}
            visible={this.state.injectDialogVisible}
            message={I18n.t("injectSdCardHint")}
            messageStyle={{
                marginTop: 20,
                marginBottom: 26,
                fontSize: 17,
                fontWeight: '700',
                color: "#333333"
            }}
            ignoreHint={this.state.isNeverRemind}
            onCheckValueChange={(checked) => {
                this.setState({isNeverRemind: checked});
            }}
            canDismiss={false}
            onDismiss={() => {
                this.setState({injectDialogVisible: false});
            }}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    callback: _ => {
                        this.setState({injectDialogVisible: false});
                    }
                },
                {
                    text: I18n.t("ok_button"),
                    callback: _ => {
                        this._injectSdCard();
                    }
                },
            ]}
        />);
    }

    _isShareUser(){
        if (LetDevice.isShareUser){
            showToast(stringsTo('shareUser_tip'));
            return true;
        }
        return false;
    }

    render() {
        global.navigation = this.props.navigation;

        return (<View style={styles.container}>
            <NavigationBar
                title={I18n.t('record_files_sdcard')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>  this.props.navigation.pop()}]}
                right={[]}
            />
            <Separator/>

            {this._renderLoading()}

            {this._renderEmptySdCard()}

           {/* <LinearGradient
                start={{x: 0, y: 0}} end={{x: 1, y: 0}}
                colors={['#496EE0', '#496EE0']}
                style={[{ top: 0,  position: "absolute", width: "100%", height: 170 }]}>

            </LinearGradient>*/}
            {/*<ScrollView showsVerticalScrollIndicator={false}>*/}
            {/*    /!*{this._renderNormalSdCardNew()}*!/*/}

            {/*    /!*{this._renderNormalSdCard()}*!/*/}

            {/*{this._renderAbnormalSdCard()}*/}
            {/*/!*{this._renderRecordAlert()}*!/*/}

            {/*</ScrollView>*/}


            {this._renderNormalSdCard()}

            {this._renderAbnormalSdCard()}

            {this._rendDialog()}
            {this._renderInjectDialog()}

            {/*{this._renderRecordAlert()}*/}

        </View>);
    }
}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#F7F7F7"
    },
    sdVolumeContainer: {
        height:170,
        marginTop:14,
        marginBottom:20,
        marginHorizontal: 14,
        paddingHorizontal:16,
        backgroundColor:"#496EE04C",
        borderRadius:10,
        alignItems: "center",
    },
    sdcardNewStyle: {
        paddingTop:42,
        width:'100%',
        height:300,
        alignItems: "center",
        paddingBottom:42,
        backgroundColor:'white',
    },
    // stateCover: {
    //     position: 'absolute',
    //     width: 208,
    //     height: 208,
    //     alignItems: 'center',
    //     backgroundColor:'orange',
    //     borderRadius:104,
    // },
    stateCoverTitle: {
        marginTop: 76,
        fontSize: 17,
        fontWeight: 'bold',
        color: 'white',
    },
    stateCoverSeprate: {
        marginTop: 20,
        backgroundColor: 'white',
        height: 1,
        width:208-28*2,
        marginLeft:28,
        marginRight:28,
    },
    stateCoverDetail: {
        marginTop: 14,
        fontSize: 15,
        color: 'white',
    },
    imageStyle:{
        width:60,
        height:60,
        marginTop: 20,
        marginBottom:24
    },
    volumeStickContainer:{
        width:"100%",
        height:20,
        backgroundColor:"#FFFFFF",
        borderRadius: 10
    },
    volumeStickStyle:{
        position:"absolute",
        height:20,
        backgroundColor:"#9E8BEF",
        borderRadius: 10
    },
    textContainer:{
        flexDirection:"row",
        justifyContent:"space-around",
    },
    volumeTextStyle:{
        flex:1,
        fontSize: 14,
        color:"#496EE0",
        marginTop:10
    },
    destroyHintTextStyle:{
        width:"100%",
        fontSize: 14,
        color:"#FFFFFF",
        marginTop:10,
        textAlign:"left"
    },
    loadingContainer: {
        width:"100%",
        height:"100%",
        position:"absolute",
        alignItems: "center",
        justifyContent:"center",
        backgroundColor: "transparent"
    },

});
