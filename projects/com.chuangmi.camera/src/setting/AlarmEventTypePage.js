import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import {BackH<PERSON>ler, ScrollView, StyleSheet, View} from 'react-native';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import React from 'react';
import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import {stringsTo} from '../../../../globalization';
import {SOUND_AND_LIGHT_WARNING} from '../../constants/Spec';
import {LetDevice} from '../../../../imilab-rn-sdk';

const TAG = 'AlarmEventTypePage';

export default class AlarmEventTypePage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      eventTypes: [],
      triggerTypes: this.props.route.params.triggerTypes,
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentDidMount() {
    this._initData(this.props.route.params.triggerTypes);
  }

  onBackHandler = () => {
    console.log('onBackAndroid  navigation.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  _onPressBack = () => {
    if (this.props.route.params.callback) {
      this.props.route.params.callback(this.state.triggerTypes);
    }
    this.props.navigation.pop();
  };

  _initData(triggerTypes) {
    const eventTypes = [
      {title: stringsTo('picture_change'), value: triggerTypes.includes(2), id: 2},
      {title: stringsTo('people_move'), value: triggerTypes.includes(5), id: 5},
      {title: stringsTo('detect_car'), value: triggerTypes.includes(9), id: 9},
      {title: stringsTo('detect_non_vehicle'), value: triggerTypes.includes(10), id: 10},
      {title: stringsTo('someone_break_into_key_area'), value: triggerTypes.includes(16), id: 16},
    ];
    this.setState({triggerTypes});
  }

  _updateTriggerType(id, isChecked) {
    console.log(TAG, `_updateTriggerType: id: ${id}, isChecked: ${isChecked}`);
    let {triggerTypes} = this.state;
    triggerTypes.map(item => {
      if(item.triggerId === id) {
        item.isChecked = isChecked
      }
      return item
    })
    const mTriggerTypes = triggerTypes.filter(item => item.isChecked).map(item => {return item.triggerId})
    this.setState({triggerTypes});
    const piid = SOUND_AND_LIGHT_WARNING.TRIGGER_EVENT.PIID;
    LetDevice.setProperties(
      true,
      LetDevice.deviceID,
      piid,
      JSON.stringify({
        msg_id: piid,
        value: `[${mTriggerTypes}]`,
      })
    )
      .then(() => {
        console.log(TAG, `update success: piid: ${piid}, value: ${triggerTypes}`);
      })
      .catch(error => {
        console.log(TAG, `update failed: piid: ${piid}, value: ${triggerTypes}, error: `, error);
      });
  }

  render() {
    let {showSynchronous056} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
    // const params = navigation.state.params.props;
    global.navigation = this.props.navigation;
    return (
      <View style={styles.container}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={stringsTo('effect_event_type')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          {this.state.triggerTypes.map(item => {
            return (
              <ListItmeWithSwitch
                title={item.eventTypeName}
                value={item.isChecked}
                key={item.triggerId}
                onValueChange={value => {
                  this._updateTriggerType(item.triggerId, value);
                }}
              />
            );
          })}
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
});
