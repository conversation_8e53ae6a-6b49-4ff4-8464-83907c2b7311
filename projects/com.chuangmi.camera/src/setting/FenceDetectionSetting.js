import React, {Component} from 'react';
import {
    Image,
    View,
    Dimensions,
    TouchableOpacity,
    Text,
    StyleSheet,
    PanResponder,
} from 'react-native';
import Svg, {
    G,
    Line,
    Rect
} from 'react-native-svg';

import {RNLine, showLoading, showToast, Separator, MessageDialog} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import HomePageLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import {
    LetDevice,
    BaseDeviceComponent,
} from '../../../../imilab-rn-sdk';
import {IMIStorage} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {TinyWindowLivePlayer} from  "../../../../imilab-modules/com.chuangmi.camera.moudle";
import { XText,XView} from 'react-native-easy-app';
import Toast from "react-native-root-toast";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import IMIPackage from "../../../../imilab-rn-sdk/native/local-kit/IMIPackage";

let windowWidth = Dimensions.get('window').width;
let windowHeight = Dimensions.get('window').height;
let minWidth = 64 / 320 * (windowWidth - 28);
let Height = (windowWidth - 28) * 187 / 332;
let minHeight = 64 / 192 * Height;
let area_Width = 320;
let area_Height = 192;
/**
 * 围栏侦测
 */
export default class FenceDetectionSetting extends BaseDeviceComponent {
    constructor(props, context) {
        super(props, context);
        this.state = {
            currentStatus:HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.LOADING,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isSleep: false,//设备是否休眠
            isOnline:LetDevice.isOnline,
            FenceSwitchValue:false,//异响侦测开关  t打开 f关闭
            effectTimeStr:stringsTo('allDay_time'),//生效时间
            effectTimeData:{
                "start_time": "00:00",
                "end_time": "23:59"
            },//生效时间
            switchClickNum:0,//单击切换方向次数 0 向上enter 1向下leaving 2 穿越cross
            //xy@20210316 手势相关
            minX: -5, //X轴起始点
            minY: -5, //Y轴起始点
            maxX: (windowWidth - 28),
            maxY: Height,
            drawColor: '#4A70A5',
            data: [],
            location:0,//this.props.navigation.state.params.location,//当前位置
            tempFenceStr:"0,0,100,100",//围栏侦测数值

        }
    }

    UNSAFE_componentWillMount() {
        this.zoomLastDistance = 0;
        this.myPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // console.log(`onStartShouldSetPanResponder ${ isMulti }`);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // // console.log("onMoveShouldSetPanResponder:" + isMulti);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                // console.log('000000');
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //   return false;
                // }
            },
            onPanResponderTerminationRequest: (evt, gestureState) => false,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('平移----');
                console.log('平移--show:',this.state.minX,"minY:"+this.state.minY,"-maxX"+this.state.maxX,"-maxY"+this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this.forceUpdate();
                if (evt.nativeEvent.changedTouches.length > 1) { // 双指时的中心点
                    console.log('平移中心点');
                    let distantX = evt.nativeEvent.changedTouches[1].pageX - evt.nativeEvent.changedTouches[0].pageX;
                    let distantY = evt.nativeEvent.changedTouches[1].pageY - evt.nativeEvent.changedTouches[0].pageY;
                    this.touchDownDistant = Math.sqrt(distantX * distantX + distantY * distantY);
                    // console.log("双指:downDistance" + this.touchDownDistant);
                }
            },
            onPanResponderMove: (evt, gestureState) => {
                let isMulti = evt.nativeEvent.changedTouches.length > 1;

                // console.log(`onPanResponderMove ${ isMulti }`);
                if (isMulti) {
                    let minX = 0;
                    let maxX = 0;
                    if (evt.nativeEvent.changedTouches[0].locationX > evt.nativeEvent.changedTouches[1].locationX) {
                        minX = evt.nativeEvent.changedTouches[1].pageX;
                        maxX = evt.nativeEvent.changedTouches[0].pageX;
                        console.log('平移中心点==isMulti');
                    } else {
                        minX = evt.nativeEvent.changedTouches[0].pageX;
                        maxX = evt.nativeEvent.changedTouches[1].pageX;
                        console.log('平移中心点==isMulti-minX');
                    }

                    let minY = 0;
                    let maxY = 0;
                    if (evt.nativeEvent.changedTouches[0].locationY > evt.nativeEvent.changedTouches[1].locationY) {
                        minY = evt.nativeEvent.changedTouches[1].pageY;
                        maxY = evt.nativeEvent.changedTouches[0].pageY;
                        console.log('平移中心点==minY');
                    } else {
                        minY = evt.nativeEvent.changedTouches[0].pageY;
                        maxY = evt.nativeEvent.changedTouches[1].pageY;
                        console.log('平移中心点==maxy');
                    }
                    const widthDistance = maxX - minX;
                    const heightDistance = maxY - minY;
                    const diagonalDistance = Math.sqrt(widthDistance * widthDistance + heightDistance * heightDistance);
                    this.zoomCurrentDistance = Number.parseInt(diagonalDistance);
                    if (this.zoomLastDistance == 0) {
                        this.zoomLastDistance = this.zoomCurrentDistance;
                        this.touchDownDistant = this.zoomCurrentDistance;
                        // console.log("双指刚放下");

                    } else {
                        let diff = this.zoomCurrentDistance - this.zoomLastDistance;
                        let diffScale = Number(((diff / 100)).toFixed(2));
                        // console.log(`双指:缩放 zoomCurrentDistance:${ this.zoomCurrentDistance } zoomLastDistance:${ this.zoomLastDistance } touchDownDistance ${ this.touchDownDistant } diff:${ diff } diffScale${ diffScale }`);
                        this.onScaleChanged(diffScale);
                        this.zoomLastDistance = this.zoomCurrentDistance;
                    }
                } else {
                    let minX = this._left + gestureState.dx;
                    minX = minX < -5 ? -5 : minX;
                    minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                    let minY = this._top + gestureState.dy;
                    minY = minY < -5 ? -5 : minY;
                    minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                    this.setState({minX: minX, minY: minY});
                    console.log('平移--isMuit--false');
                    console.log('平移--show:',parseInt(this.state.minX),"minY:"+parseInt(this.state.minY),"-maxX"+parseInt(this.state.maxX),"-maxY"+parseInt(this.state.maxY));
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                this.zoomLastDistance = 0;
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('左上处理----');
                console.log('左上处理--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minX: minX, minY: minY, maxX: maxX, maxY: maxY});
                console.log('左上处理Move--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('右上处理----');
                console.log('右上处理--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                // let minX = this._left + gestureState.dx;
                // minX = minX < -5 ? -5 : minX;
                // minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minY: minY, maxX: maxX, maxY: maxY});
                console.log('右上处理Move--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('左下处理----');
                console.log('左下处理--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                // let minY = this._top + gestureState.dy;
                // minY = minY < -5 ? -5 : minY;
                // minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28- this._left - 5) ? (windowWidth - 28- this._left - 5) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height - this._top - 5 ? Height - this._top - 5 : maxY;
                this.setState({minX: minX, maxX: maxX, maxY: maxY});
                console.log('左下处理Move--show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('右下处理----');
                console.log('右下处理-show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);

                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28- this._left - 5) ? (windowWidth - 28- this._left - 5) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height - this._top - 5  ? Height - this._top - 5  : maxY;
                this.setState({maxX: maxX, maxY: maxY});
                console.log('右下处理Move-show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
    }
    onScaleChanged(diffScale) {
        console.log('双指处理---');
        let width = this.state.maxX;
        let height = this.state.maxY;
        width = width * (diffScale + 1);
        height = height * (diffScale + 1);
        width = width > (windowWidth - 28) ? (windowWidth - 28) : width;
        height = height > Height ? Height : height;
        width = width < minWidth ? minWidth : width;
        height = height < minHeight ? minHeight : height;
        let minX = ((windowWidth - 28 - 5) - width) / 2;
        minX = minX > 0 ? minX : -5;
        let minY = (Height - height) / 2;
        minY = minY > 0 ? minY : -5;
        this.setState({
            minX: minX,
            minY: minY,
            maxX: width,
            maxY: height
        },callback=>{
            console.log('双指处理-show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
        });
    }

    componentDidMount() {
        this._subscribe_focus = this.props.navigation.addListener('focus', () => { //获取焦点监听
                this.getNetWork();
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => { //去往其他界面监听
            this.IMIVideoView && this.IMIVideoView.stop();
            showLoading(false);
        });

        // Subscribe
        const unsubscribe = NetInfo.addEventListener(state => {
            if (state.type!='wifi'&&this.state.isDataUsage){
                this.getNetWork();
            }
            console.log("Is connected?", state.isConnected);
        });

        // Unsubscribe
        unsubscribe();

        this.getAllValue();



    }

    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        //LetDevice.updateAllPropertyCloud().then((data) => {
         LetDevice.getPropertyCloud("FenceAttr").then((data) => {
            showLoading(false);
            console.log("dataObject.FenceSwitch",data)
            let dataObject = JSON.parse(data);
            let stateProps = {};
            if (dataObject){
                if (dataObject.fence_switch!=undefined){
                    console.log("dataObject.FenceSwitch",dataObject.fence_switch)
                    if(typeof dataObject.fence_switch=== 'number'){
                        if (dataObject.fence_switch==1){ //1是打开 0是关闭
                            stateProps.FenceSwitchValue =true;
                            this.timerVideoView = setTimeout(() => {
                                    this.IMIVideoView&&this.IMIVideoView.prepare();
                                },
                                1000
                            );
                        }else {
                            stateProps.FenceSwitchValue = false;
                        }
                    }else {
                        stateProps.FenceSwitchValue =false;
                    }
                }
                if (dataObject.start_time!=undefined&&dataObject.end_time!=undefined) {
                    let FenceDetectionTime={"start_time":dataObject.start_time,"end_time":dataObject.end_time}

                        stateProps.effectTimeData = FenceDetectionTime;
                        let startTimeStr = dataObject.start_time;
                        let endTimeStr = dataObject.end_time;
                        console.log('tempStr--startTime--endTime', startTimeStr, endTimeStr);
                        if ((startTimeStr == "00:00") && (endTimeStr == "23:59")) {
                            stateProps.effectTimeStr = stringsTo('allDay_time');
                            console.log('全天')
                        } else if ((startTimeStr == "08:00") && (endTimeStr == "20:00")) {
                            stateProps.effectTimeStr = stringsTo('day_time');
                            console.log('白天')
                        } else if ((startTimeStr == "20:00") && (endTimeStr == "08:00")) {
                            stateProps.effectTimeStr = stringsTo('night_time');
                            console.log('夜晚')
                        } else {
                            // stateProps.effectTimeStr = stringsTo('voice_for_custom')+stringsTo('allDay_time');
                            // stateProps.effectTimeStr = startTimeStr + '-' + endTimeStr;
                            stateProps.effectTimeStr = stringsTo('voice_for_custom') + '（' + startTimeStr + '-' + endTimeStr + '）';
                            console.log('自定义')
                    }
                }

                console.log("rect:",dataObject.rect)
                if (dataObject.rect!=undefined) {
                    let rect=dataObject.rect.split(",");
                    let rect1= -5+parseInt(parseInt(rect[0])/100*(windowWidth - 28));
                    let rect2= -5+parseInt(parseInt(rect[1])/100 *Height);
                    let rect3=parseInt( parseInt(rect[2])/100*(windowWidth - 28));
                    let rect4= parseInt(parseInt(rect[3])/100 *Height);
                    console.log("rect:",rect.length+"---"+rect1+","+rect2+","+rect3+","+rect4+"")
                       this.setState({
                           tempFenceStr:dataObject.rect,//围栏侦测数值
                           minX: rect1, //X轴起始点
                           minY: rect2, //Y轴起始点
                           maxX: rect3,
                           maxY: rect4,
                       })
                }
                if (dataObject.direction!=undefined) {
                    this.setState({
                        switchClickNum:parseInt(dataObject.direction),//围栏侦测数值
                    })
                }
                // 统一设置从设备端获取的值
                this.setState(stateProps);
            }
            // alert(dataObject);

        }).catch(error => {
            // alert(JSON.stringify(error));
           // showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
            console.log(error)
        });
    }

    getNetWork(){
        IMIStorage.load({
            key: LetDevice.deviceID+'isDataUsageWarning',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage){
                NetInfo.fetch().then(state => {
                    if (state.type=='wifi'){
                        this.IMIVideoView.prepare();
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                    }
                });
            }else {
                this.IMIVideoView.prepare();
            }
        }).catch(_=> {
            this.setState({isDataUsage: false});
            this.IMIVideoView.prepare();
        });
    }

    componentWillUnmount() {
      /*  if (isAndroid()) { }*/
        this.IMIVideoView&&this.IMIVideoView.destroy();
        LetDevice.removeDeviceEventChangeListener();
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this.timerVideoView && clearTimeout(this.timerVideoView);
    }

    _onPressBack = () => {
        this.props.navigation.pop();
    };

    _saveFence() {
        let rect1= parseInt((this.state.minX+5)/(windowWidth - 28)*100);
        let rect2=parseInt((this.state.minY+5)/Height *100);
        let rect3=parseInt((this.state.maxX)/(windowWidth - 28)*100);
        let rect4= parseInt((this.state.maxY)/Height *100);

        let rect=rect1+","+rect2+","+(rect3>100?100:rect3)+","+(rect4>100?100:rect4);
        let switchClickNum=this.state.switchClickNum+"";
        console.log("switchClickNum:",switchClickNum)
        // let rect=(this.state.minX<0?0:this.state.minX)+","+(this.state.minY<0?0:this.state.minY)+","+parseInt(this.state.maxX/(windowWidth - 28)*100)+","+parseInt(this.state.maxY/Height *100);
         let FenceSwitchValue=this.state.FenceSwitchValue?1:0;
        console.log("开始 rect:", rect)
        let value={fence_switch:FenceSwitchValue
            ,start_time:this.state.effectTimeData.start_time
            ,end_time:this.state.effectTimeData.end_time
            , rect:rect,direction:switchClickNum}

        let params={//FenceDetectionTime  FenceAttr
            FenceAttr:JSON.stringify(value) // "{ fence_switch:"+FenceSwitchValue+ ",start_time:"+this.state.effectTimeData.start_time+",end_time:"+this.state.effectTimeData.end_time+ ", rect:"+rect+",direction:"+this.state.switchClickNum+"}"
         /*   FenceDetectionTime: {fence_switch:this.state.FenceSwitchValue?1:0
                ,start_time:this.state.effectTimeData.start_time
                ,end_time:this.state.effectTimeData.end_time
                , rect:+rect,direction:this.state.switchClickNum}*/
             }
            console.log("开始JSON.stringify(params):",JSON.stringify(params))
        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            if (this.props.route.params.callback) {
                console.log('异响侦测返回传值',this.state.FenceSwitchValue)
                let tempStr;
                if (this.state.FenceSwitchValue){
                    tempStr = "1"; // 已开启
                }else {
                    tempStr = "0"; // 未开启
                }
                console.log('返回值---',tempStr)
                this.props.route.params.callback(tempStr);
            }
            this.props.navigation.pop();
        }).catch(_=> {
            console.log("失败:",_)
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
        });
    }



    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status){
        if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        }else if(this.state.currentStatus==HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR
            &&status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE){
            //目前发现IOS ERROR后还会调用PAUSE，所以ERROR和暂停View重叠
            return;
        }
        this.setState({currentStatus: status});

    }
    //判断当前是否可以操作
    _canStepIn(){
        console.log('通话打印当前直播流状态----',this.state.isStartMove);
        if(this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }



    render() {
        return (
            <View style={{flex:1,backgroundColor:'#F2F3F5'}}>
                <NavigationBar
                    title={stringsTo('fence_detect_switch')}
                    left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this.props.navigation.pop(),
                        accessibilityLabel:"fence_detect_switch_go_back"
                    }]}
                    right={[{key: NavigationBar.ICON.COMPLETE, onPress: ()=>{
                            console.log('平移--show:',this.state.minX,"minY:"+this.state.minY,"-maxX"+this.state.maxX,"-maxY"+this.state.maxY);
                            this.IMIVideoView && this.IMIVideoView.stop();
                               this._saveFence();
                        },
                        accessibilityLabel:"fence_detect_switch_COMPLETE"
                    }]}
                />
                {/*围栏侦测开关*/}
                <ListItmeWithSwitch title={stringsTo('fence_detect_switch')} value={this.state.FenceSwitchValue}
                                    onValueChange={(value) => {
                                        // if (IMIPackage.minApiLevel<10005){
                                        //     showToast(stringsTo("show_upgrade_app"))
                                        //      return
                                        //  }
                                        console.log("value:",value)
                                        this.setState({
                                            FenceSwitchValue:value
                                        })
                                        if (value==false){
                                            this.IMIVideoView&&this.IMIVideoView.stop();
                                        }else {
                                            this.timerVideoView = setTimeout(() => {
                                                    this.IMIVideoView&&this.IMIVideoView.prepare();
                                                },
                                                1000
                                            );
                                        }
                                    }}/>
                {this.state.FenceSwitchValue ? (<ListItem title={stringsTo('nursingTimeSetting')} value={this.state.effectTimeStr} onPress={() => {
                    this.props.navigation.navigate('EffectiveTimeSetPage',{
                        type:'FenceEffectTime',
                        effectTimeValue:JSON.stringify(this.state.effectTimeData),
                        callback:((timeStr,timeData) => {
                            console.log('返回修改围栏侦测侦测生效时间--',timeStr,timeData);
                            if (timeStr != 'failed'){
                                this.setState({
                                    effectTimeStr:timeStr,
                                    effectTimeData:JSON.parse(timeData)
                                })
                            }
                        })
                    })
                }}/>) : null}
                {this.state.FenceSwitchValue ?
                <XText style={{marginTop:14,marginLeft:14,fontSize:12,color:'#000',lineHeight:16}} allowFontScaling={false} text={stringsTo('imi_fence_tit')}/>:null }
                {this.state.FenceSwitchValue ?
                <View style={{marginTop:12,justifyContent:'center',alignItems:'center'}}>
                    { <TinyWindowLivePlayer  {...this.props}
                                                                     videoRef={ref => this.IMIVideoView = ref}
                                                                     onLivePlayerStatusChange={(status)=>this._onLivePlayerStatusChangeListener(status)}
                                                                     playerMarginHorizontal={14}
                    />}

                    {this._renderGestureLayoutView()}
                </View> :null }
                {this.state.FenceSwitchValue ?this._renderShowImg():null}
                {this.state.FenceSwitchValue ?this._renderSwitchDirection():null}


            </View>


        );
    }
    _renderGestureLayoutView() {
        return (<View style={{
            position: 'absolute',
            flexDirection: 'column',
            width: windowWidth-28,
            height: '100%',
        }}>
            <View style={{
                marginTop: this.state.minY, flexDirection: 'row', zIndex: 2,
            }}>
                <View style={{
                    marginLeft: this.state.minX,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }} hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} {...this.leftTopPanResponder.panHandlers}/>

                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.maxX-10,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: '#4A70A5'
                }}{...this.rightTopPanResponder.panHandlers}/>
            </View>

            <View style={{
                zIndex: 1,
                marginTop: -5,
                marginLeft: this.state.minX + 5,
                width: this.state.maxX,
                height: this.state.maxY,
                // backgroundColor: '#4A70A5',
                // opacity: 0.3,

            }} {...this.myPanResponder.panHandlers}/>

            <View style={{
                marginTop: -5, flexDirection: 'row', zIndex: 2,

            }}>
                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.minX,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: '#4A70A5'
                }}{...this.leftBottomPanResponder.panHandlers}/>

                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.maxX - 10,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: '#4A70A5'
                }}{...this.rightBottomPanResponder.panHandlers}/>
            </View>
            {this._renderRectView()}


        </View>);
    }
    // 画矩形
    _renderRectView() {
        console.log('画矩形---',this.state.minX+5,this.state.minY+5,this.state.maxX,this.state.maxY);
        return(<View style={{
            position:'absolute',
            width:'100%',
            height:'100%',
        }}>
            <Svg height="100%" width="100%" >
                <Rect
                    x={this.state.minX+5}
                    y={this.state.minY+5}
                    width={this.state.maxX}
                    height={this.state.maxY}
                    stroke={"#4A70A5"}
                    strokeWidth="4"
                    strokeDasharray="10"
                    fill={"transparent"}
                />
            </Svg>
        </View>)
    }
    _renderShowImg() {
        // console.log('show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
        let marLeft = this.state.minX+(this.state.maxX-20+5)/2;
        let marTop = this.state.minY+this.state.maxY-20/2+5;
        // console.log('上----左',marTop,marLeft);
        let imgPath;
        if (this.state.switchClickNum == 0){
            imgPath = require("../../resources/images/imi_fence_up.png");
        }else if (this.state.switchClickNum == 1){
            imgPath = require("../../resources/images/imi_fence_down.png");
        }else if (this.state.switchClickNum == 2){
            imgPath = require("../../resources/images/imi_fence_change.png");
        }else if (this.state.switchClickNum == 3){
            imgPath = require("../../resources/images/imi_fence_up.png");
        }
        return(<View style={{
            top:-20,
            marginLeft:windowWidth/2-14,
            height:20,
            width:20,
            flexDirection:'row',

        }}>
            <Image
                style={{width: 20, height: 20}}
                source={imgPath}
            />

        </View>)
    }
    _renderSwitchDirection() {
        return (
            <View style={{flexDirection: "row"}}>
                    <TouchableOpacity
                        style={{flexDirection: "row",left:windowWidth-150,right:28,top:-10,width:120,height:20, alignItems: 'center',
                            justifyContent: 'center',}}
                        hitSlop={{
                            left: 35,
                            right:35,
                            top: 20,
                            bottom: 20
                        }}
                        onPress={()=>{
                            console.log('切换方向');
                            let clickNum = this.state.switchClickNum +1;
                            // clickNum += 1;
                            console.log('点击次数===',clickNum,this.state.switchClickNum);
                            if (clickNum == 3){
                                console.log('当前点击次数---',clickNum);
                                this.setState({switchClickNum:0})
                            }else {
                                this.setState({switchClickNum:clickNum},callback=>{
                                    console.log('当前次数---',this.state.switchClickNum);
                                })
                            }
                        }}>
                        <Image
                            style={{width: 20, height: 20}}
                            source={require("../../resources/images/imi_switch_det.png")}
                        />
                        <Text
                            numberOfLines={1}
                            allowFontScaling={false}
                            style={{
                                left:3,
                                // lineHeight:19,
                                textAlign:'right',
                                textAlignVertical:'center',
                                color:'#333333',
                                fontSize:11,
                            }}>{ stringsTo("imi_switch_dection")}</Text>
                    </TouchableOpacity>
            </View>
        )
    }

}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: 80,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center"
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        marginTop: 15,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
});
