import React from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
  PanResponder,
  Platform,
  BackHandler,
  PixelRatio,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';

import { Surface, Shape, Path } from '@react-native-community/art';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, MessageDialog, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = (viewWidth * 9) / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 0; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
const {width: screenWidth, height: screenHeight} = Dimensions.get('screen');
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const TAG = 'VirtualFenceAreaSetting';
export default class VirtualFenceAreaSetting extends React.Component {
  static navigationOptions = navigation => {
    return {headerTransparent: true, header: null};
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      progressing: false,
      showCover: true,
      canSave: false,
      showSaveDialog: false,
      dynamicBgSource: require("../../resources/images/ai_pic_area.webp"), // 动态背景图源
    };
    this.isMoving = false;
    this.timeStamp = Date.now();

    // 获取双摄像头相关参数
    this.cameraNumber = this.props.route?.params?.cameraNumber || '1';
    this.areaType = this.props.route?.params?.areaType || 0;

    // 使用从VirtualFenceSetting传入的真实数据
    this.rectDatas = this.props.route?.params?.areaData || [0, 0, viewWidth, viewHeight];
    this.privateSwitch = this.props.route?.params?.privateSwitch || false;
    this.privateDatas = this.props.route?.params?.privateAreaData;
    this.styleType = this.props.route?.params?.styleType || 2;

    console.log('VirtualFenceAreaSetting 初始化参数:', {
      cameraNumber: this.cameraNumber,
      areaType: this.areaType,
      rectDatas: this.rectDatas,
      privateDatas: this.privateDatas
    });
    // 样式背景图片（如果需要的话）
    this.styleSource = require('../../resources/images/area_style_bg_one.webp'); // 使用默认背景
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = [
      Math.floor(this.rectDatas[0] / itemWidth) * itemWidth,
      Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth,
      Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight,
    ];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值
    this.existsSettingsImg = false;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: evt => {
        this.setState({showCover: false, showBg: false});
        let x = evt.nativeEvent.locationX; // 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) {
          // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) {
          // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) {
          // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) {
          // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        if (Math.abs(gestureState.dx) <= 5 && Math.abs(gestureState.dy) <= 5) {
          console.log('---------------------------没滑动距离');
          return;
        }
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy;// dy 从触摸操作开始时的累计纵向位移
        console.log(
          '===========is moving',
          this.touchBeginCoordX,
          gestureState.dx,
          this.touchBeginCoordY,
          gestureState.dy,
        );
        let pointChange = false;
        switch (this.touchPosition) {
          case 12: { // 拖动左上角 触摸点[8,4]

            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y < 0 ? 0 : y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: { // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: { // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: { // 拖动左下角 触摸点[8,1]
            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        if (pointChange) {
          this.setState({ canSave: true });
        }

      },

      onPanResponderRelease: () => {
        console.log('onPanResponderRelease');
        this.cropImage();
      },

      onPanResponderTerminate: () => {
        console.log('onPanResponderTerminate');
      },
    });
  }

  componentDidMount() {
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }

    // 调试信息：打印从VirtualFenceSetting传入的真实数据
    console.log('=== VirtualFenceAreaSetting 接收到的数据 ===');
    console.log('绘制区域尺寸:', { viewWidth, viewHeight });
    console.log('传入的围栏区域坐标:', this.props.route?.params?.areaData);
    console.log('传入的隐私区域坐标:', this.props.route?.params?.privateAreaData);
    console.log('传入的隐私区域开关:', this.props.route?.params?.privateSwitch);
    console.log('传入的样式类型:', this.props.route?.params?.styleType);
    console.log('最终使用的围栏区域坐标:', this.rectDatas);
    console.log('最终使用的隐私区域坐标:', this.privateDatas);
    console.log('最终使用的隐私区域开关:', this.privateSwitch);
    console.log('=== 数据接收完成 ===');

    // 检查并设置对应的封面图
    this.checkAndSetSnapshotBackground();

    // 初始化显示状态
    this.setState({ showBg: true, showCover: false });
  }

  // 检查并设置对应的封面图
  async checkAndSetSnapshotBackground() {
    try {
      // 根据areaType确定要检查的图片路径
      const sensorIndex = this.areaType === 0 ? 'sensor0' : 'sensor1';
      const snapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${LetDevice.model}_live_${sensorIndex}.jpg`;

      console.log('检查围栏区域封面图路径:', snapshotPath);

      // 检查文件是否存在
      const fileExists = await IMIFile.fileExists(snapshotPath);
      console.log('围栏区域封面图是否存在:', fileExists);

      if (fileExists) {
        // 如果文件存在，设置为背景图
        this.setState({
          dynamicBgSource: { uri: `file://${snapshotPath}?t=${Date.now()}` }
        });
        console.log('设置围栏区域封面图为背景:', snapshotPath);
      } else {
        // 如果文件不存在，使用默认背景图
        this.setState({
          dynamicBgSource: require("../../resources/images/ai_pic_area.webp")
        });
        console.log('使用默认围栏区域背景图');
      }
    } catch (error) {
      console.error('检查围栏区域封面图失败:', error);
      // 出错时使用默认背景图
      this.setState({
        dynamicBgSource: require("../../resources/images/ai_pic_area.webp")
      });
    }
  }

  cropImage() {
    // 简化截图逻辑，直接更新显示状态
    this.setState({showBg: true, showCover: false});
  }

  renderTitleBar() {
    // 根据摄像头类型和区域类型显示不同的标题
    let title = stringsTo('fence_area');
    if (this.cameraNumber === '2') {
      title = this.areaType === 0 ? stringsTo('dual_camera_ptz_area_edit') : stringsTo('dual_camera_gun_area_edit');
    }

    return (
      <NavigationBar
        title={title}
        type={this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white}
        left={[
          {
            key: this.state.canSave ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
            n_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
            p_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
            onPress: () => {
              if (this.state.canSave) {
                this.setState({showSaveDialog: true});
                return;
              }
              this.props.navigation.goBack();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={
          this.state.canSave
            ? [
                {
                  key: NavigationBar.ICON.COMPLETE,
                  onPress: () => {
                    this.onSubmit();
                  },
                },
              ]
            : []
        }
        titleStyle={{
          fontSize: 18,
          color: '#333333',
          fontWeight: 500,
        }}
      />
    );
  }

  // 点击保存看护区域坐标数据
  async onSubmit() {
    console.log('保存围栏区域坐标:', this.rectDatas);
    console.log('摄像头类型:', this.cameraNumber, '区域类型:', this.areaType);

    try {
      // 显示加载状态
      showLoading(stringsTo('commWaitText'), true);

      // 参考米家项目：将像素坐标转换为百分比坐标
      let positions = [
        Math.ceil(this.rectDatas[0] / viewWidth * 100),
        Math.ceil(this.rectDatas[1] / viewHeight * 100),
        Math.ceil(this.rectDatas[2] / viewWidth * 100),
        Math.ceil(this.rectDatas[3] / viewHeight * 100)
      ];

      let fenceAreaData;

      if (this.cameraNumber === '2') {
        // 双摄像头设备：需要获取当前数据，然后更新对应的sensor
        console.log('双摄像头设备，获取当前虚拟围栏数据...');

        try {
          // 获取当前的虚拟围栏数据
          const currentData = await LetDevice.getSingleProperty('100001');
          console.log('当前虚拟围栏数据:', JSON.stringify(currentData));

          let existingData = null;
          if (currentData?.value?.code === 0 && currentData.value.value) {
            existingData = JSON.parse(currentData.value.value);
          }

          // 构建双摄像头虚拟围栏数据格式
          // 数据结构：fence_area[0].sensor0 和 fence_area[1].sensor1
          fenceAreaData = {
            "fence_area": [
              {
                "sensor0": existingData?.fence_area?.[0]?.sensor0 || [{
                  "area": [{"pos": [0, 0, 100, 100]}],
                  "color": 0
                }]
              },
              {
                "sensor1": existingData?.fence_area?.[1]?.sensor1 || [{
                  "area": [{"pos": [0, 0, 100, 100]}],
                  "color": 0
                }]
              }
            ]
          };

          // 根据areaType更新对应的sensor数据
          if (this.areaType === 0) {
            // 更新sensor0(球机)数据
            fenceAreaData.fence_area[0].sensor0[0].area[0].pos = positions;
            console.log('更新球机虚拟围栏数据:', positions);
          } else if (this.areaType === 1) {
            // 更新sensor1(枪机)数据
            fenceAreaData.fence_area[1].sensor1[0].area[0].pos = positions;
            console.log('更新枪机虚拟围栏数据:', positions);
          }

        } catch (error) {
          console.error('获取当前虚拟围栏数据失败:', error);
          // 如果获取失败，使用默认数据结构
          fenceAreaData = {
            "fence_area": [
              {
                "sensor0": [{
                  "area": [{"pos": this.areaType === 0 ? positions : [0, 0, 100, 100]}],
                  "color": 0
                }]
              },
              {
                "sensor1": [{
                  "area": [{"pos": this.areaType === 1 ? positions : [0, 0, 100, 100]}],
                  "color": 0
                }]
              }
            ]
          };
        }
      } else {
        // 单摄像头设备：使用原有逻辑
        fenceAreaData = {
          "fence_area": [{
            "sensor0": [{
              "area": [{
                "pos": positions
              }],
              "color": 0
            }]
          }]
        };
      }

      const valueString = JSON.stringify(fenceAreaData);
      console.log("虚拟围栏区域数据:", valueString);

      // 按照物模型格式构建数据
      const paramJson = JSON.stringify({
        msg_id: '100001',
        value: valueString
      });
      console.log("格式化后的物模型数据:", paramJson);

      // 使用物模型方式设置数据
      LetDevice.setProperties(true, LetDevice.deviceID, '100001', paramJson).then((res) => {
        console.log("虚拟围栏区域数据设置成功:", JSON.stringify(res));

        // 设置成功后调用回调函数，将编辑后的坐标数据传回VirtualFenceSetting页面
        const callback = this.props.route?.params?.callback;
        if (callback) {
          callback(this.rectDatas);
        }

        showToast(stringsTo('settings_set_success'));
        this.props.navigation.goBack();

      }).catch((err) => {
        console.error("设置虚拟围栏区域数据失败:", JSON.stringify(err));
        showToast(stringsTo('operationFailed'));
      }).finally(() => {
        showLoading(false);
      });

    } catch (error) {
      console.error("保存围栏区域数据异常:", error);
      showToast(stringsTo('operationFailed'));
      showLoading(false);
    }
  }

  render() {
    let imageSource = '';

    let paths = [];
    // 全部处于隐私区域内
    let allInPrivate = 2;
    // if (!this.privateSwitch) {
    //   // 隐私区域未开启
    //   allInPrivate = 2;
    // } else {
    //   if (this.rectDatas[0] >= this.privateDatas[2]
    //     || this.rectDatas[2] <= this.privateDatas[0]
    //     || this.rectDatas[1] >= this.privateDatas[3]
    //     || this.rectDatas[3] <= this.privateDatas[1]
    //     || (this.rectDatas[0] <= this.privateDatas[0] && this.rectDatas[1] <= this.privateDatas[1] && this.rectDatas[2] >= this.privateDatas[2] && this.rectDatas[3] >= this.privateDatas[3])) {
    //     // 全部位于隐私区域内
    //     allInPrivate = 1;
    //   } else if (this.rectDatas[0] >= this.privateDatas[0]
    //     && this.rectDatas[1] >= this.privateDatas[1]
    //     && this.rectDatas[2] <= this.privateDatas[2]
    //     && this.rectDatas[3] <= this.privateDatas[3]) {
    //     //全部位于在区域内
    //     allInPrivate = 2;
    //   } else {
    //     //隐私区域与围栏有交集
    //     allInPrivate = 0;
    //     paths = this.getPaths()
    //   }
    // }

    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    // 隐私区域的绘制路径
    let private_area_path = Path()
      .moveTo(this.privateDatas[0], this.privateDatas[1])
      .lineTo(this.privateDatas[2], this.privateDatas[1])
      .lineTo(this.privateDatas[2], this.privateDatas[3])
      .lineTo(this.privateDatas[0], this.privateDatas[3])
      .close();

    // 移除未使用的 fence_path，因为 this.fenceDatas 未定义
    // let fence_path = Path()
    //   .moveTo(this.fenceDatas[0], this.fenceDatas[1])
    //   .lineTo(this.fenceDatas[2], this.fenceDatas[1])
    //   .lineTo(this.fenceDatas[2], this.fenceDatas[3])
    //   .lineTo(this.fenceDatas[0], this.fenceDatas[3])
    //   .close();

    let background_path_all = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
    let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);
    // 隐私区域的尺寸
    let privateCropWidth = Math.abs(this.privateDatas[2] - this.privateDatas[0]);
    let privateCropHeight = Math.abs(this.privateDatas[3] - this.privateDatas[1]);

    // TODO 需要替换ImageBackground图片
    return (
      <View
        style={{
          display: 'flex',
          height: '100%',
          width: '100%',
          flex: 1,
          flexDirection: 'column',
          backgroundColor: '#FFFFFF',
        }}>
        {this.renderTitleBar()}
        <Text
          style={{
            fontSize: 12,
            color: '#666666',
            marginLeft: 27,
            marginTop: 28,
          }}>
          {stringsTo('fence_area_draw')}
        </Text>
        <View {...this.panResponder.panHandlers}>
          <ImageBackground
            style={{
              width: viewWidth,
              height: viewHeight,
              marginHorizontal: 24,
              marginTop: 13,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>

            {/* 展示背景图 - 根据areaType动态设置封面图或默认图片 */}
            <Image
              style={{
                width: viewWidth,
                height: viewHeight,
              }}
              resizeMode={'cover'}
              source={this.state.dynamicBgSource}
            />

            {/* 隐私区域框内显示固定图 */}
            {this.privateSwitch ? (
              <View
                style={{
                  position: 'absolute',
                  width: privateCropWidth,
                  height: privateCropHeight,
                  left: this.privateDatas[0],
                  top: this.privateDatas[1],
                  overflow: 'hidden',
                }}>
                <View
                  backgroundColor={'#000'}
                  style={{
                    width: privateCropWidth,
                    height: privateCropHeight,
                  }}
                />
              </View>
            ) : null}

            <View style={{ position: 'absolute' }}>
              <Surface width={viewWidth} height={viewHeight}>
                {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
                <Shape d={background_path_one} fill="#000000" opacity="0.5" />
                <Shape d={background_path_two} fill="#000000" opacity="0.5" />
                <Shape d={background_path_three} fill="#000000" opacity="0.5" />
                <Shape d={background_path_four} fill="#000000" opacity="0.5" />
                {
                  allInPrivate === 1 ? <Shape d={draggable_rectangle_path} stroke="#ff0000" strokeWidth={1} /> :
                    allInPrivate === 2 ?
                      <Shape d={draggable_rectangle_path} stroke="#806BFF" strokeWidth={1} /> : null
                }
                {allInPrivate === 0 ?
                  paths.map((item, index) => {
                    return (
                      <Shape key={index} d={item.path} stroke={item.color} strokeWidth={1} />
                    )
                  }) : null
                }

                {/* 移除隐私区域白色背景，让固定图显示出来 */}
                {/* {this.privateSwitch && <Shape d={private_area_path} fill="#FFFFFF" opacity="1" />} */}

              </Surface>

            </View>
          </ImageBackground>
          <View
            style={{
              width: viewWidth + 6,
              height: viewHeight + 6,
              position: 'absolute',
              marginHorizontal: 21,
              marginTop: 10,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>
            <Surface width={viewWidth + 6} height={viewHeight + 6}>
              <Shape d={top_left_circle} fill="#806BFF" />
              <Shape d={top_right_circle} fill="#806BFF" />
              <Shape d={bottom_right_circle} fill="#806BFF" />
              <Shape d={bottom_left_circle} fill="#806BFF" />
            </Surface>
          </View>
        </View>

        {this._renderBackDialog()}
      </View>
    );
  }

  // getPaths() {
  //   let fStartX = this.rectDatas[0];// 起始x
  //   let fStartY = this.rectDatas[1];// 起始y
  //   let fEndX = this.rectDatas[2];// 结束点x
  //   let fEndY = this.rectDatas[3];// 结束点y
  //
  //   let pStartX = this.privateDatas[0];// 起始x
  //   let pStartY = this.privateDatas[1];// 起始y
  //   let pEndX = this.privateDatas[2];// 结束点x
  //   let pEndY = this.privateDatas[3];// 结束点y
  //   let redPath = Path();
  //   let greenPath = Path();
  //   let paths = [];
  //   if (fStartX < pStartX && fStartY < pStartY && fEndX > pEndX && fEndY < pEndY) {
  //     // 包裹隐私区域上半部分
  //     redPath.moveTo(pStartX, fEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //
  //     greenPath.moveTo(pStartX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY < pStartY && fEndX < pEndX && fEndY > pEndY) {
  //     // 包裹隐私区域左半部分
  //     redPath.moveTo(fEndX, pStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, pEndY);
  //     greenPath.moveTo(fEndX, pStartY)
  //       .lineTo(fEndX, pEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY < pStartY && fEndX >= pEndX && fEndY > pEndY) {
  //     //包裹隐私区域右半部分
  //     redPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fStartX, pEndY);
  //
  //     greenPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, pEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY < pEndY) {
  //     // 包裹隐私区域左上部分
  //     redPath.moveTo(pStartX, fEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, pStartY);
  //
  //     greenPath.moveTo(fEndX, pStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pStartX, fEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY < pStartY && fEndX > pEndX && fEndY < pEndY) {
  //     // 包裹隐私区域右上部分
  //     redPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //
  //     greenPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY < pEndY) {
  //     // 包裹隐私区域中上部分 可视区域中上
  //     redPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, pStartY);
  //
  //     greenPath.moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, pStartY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY >= pEndY) {
  //     // 包裹隐私区域中下部分 可视区域中下
  //     redPath.moveTo(fStartX, pEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, pEndY);
  //
  //     greenPath.moveTo(fStartX, pEndY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, pEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX > pStartX && fStartY < pStartY && fEndX <= pEndX && fEndY > pEndY) {
  //     // 上下超出
  //     redPath
  //       .moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, pStartY);
  //
  //     let redPathTwo = Path()
  //       .moveTo(fStartX, pEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, pEndY);
  //
  //     greenPath
  //       .moveTo(fStartX, pStartY)
  //       .lineTo(fStartX, pEndY);
  //
  //     let greenPathTwo = Path()
  //       .moveTo(fEndX, pStartY)
  //       .lineTo(fEndX, pEndY);
  //
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: redPathTwo, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //     paths.push({ path: greenPathTwo, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY < pEndY) {
  //     // 处于课时区域高度内，包裹左侧部分隐私区域
  //     redPath.moveTo(pStartX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(pStartX, fEndY);
  //
  //     greenPath.moveTo(pStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pStartX, fEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY > pStartY && fEndX <= pEndX && fEndY >= pEndY) {
  //     // 左下角交集
  //     redPath.moveTo(pStartX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, pEndY);
  //
  //     greenPath.moveTo(pStartX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, pEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY > pStartY && fEndX > pEndX && fEndY >= pEndY) {
  //     // 右下角交集
  //     redPath.moveTo(fStartX, pEndY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(pEndX, fStartY);
  //
  //     greenPath.moveTo(fStartX, pEndY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(pEndX, fStartY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY > pStartY && fEndX > pEndX && fEndY >= pEndY) {
  //     // 包裹底部
  //     redPath.moveTo(pStartX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(pEndX, fStartY);
  //
  //     greenPath.moveTo(pStartX, fStartY)
  //       .lineTo(pEndX, fStartY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   } else if (fStartX < pStartX && fStartY > pStartY && fEndX >= pEndX && fEndY < pEndY) {
  //     // 左右超出
  //     redPath.moveTo(pStartX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(pStartX, fEndY);
  //
  //     let redPathTwo = Path()
  //       .moveTo(pEndX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //
  //     greenPath
  //       .moveTo(pStartX, fStartY)
  //       .lineTo(pEndX, fStartY);
  //
  //     let greenPathTwo = Path()
  //       .moveTo(pStartX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: redPathTwo, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //     paths.push({ path: greenPathTwo, color: "#32BAC0" });
  //   } else if (fStartX >= pStartX && fStartY > pStartY && fEndX > pEndX && fEndY < pEndY) {
  //     // 处于课时区域高度内，包裹右侧部分隐私区域
  //     redPath.moveTo(pEndX, fStartY)
  //       .lineTo(fEndX, fStartY)
  //       .lineTo(fEndX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //
  //     greenPath.moveTo(pEndX, fStartY)
  //       .lineTo(fStartX, fStartY)
  //       .lineTo(fStartX, fEndY)
  //       .lineTo(pEndX, fEndY);
  //     paths.push({ path: redPath, color: "#ff0000" });
  //     paths.push({ path: greenPath, color: "#32BAC0" });
  //   }
  //   return paths;
  // }


  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={stringsTo('exit_save_confirm')}
        messageStyle={{textAlign: 'center'}}
        canDismiss={false}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: _ => {
              this.setState({showSaveDialog: false});
            },
          },
          {
            text: stringsTo('exit'),
            callback: _ => {
              this.setState({showSaveDialog: false});
              this.props.navigation.goBack();
            },
          },
        ]}
      />
    );
  }

  componentWillUnmount() {
    if (Platform.OS === 'android') {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };
}

const styles = StyleSheet.create({
  areaStyle: {
    width: screenWidth,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0,
    backgroundColor: imiThemeManager.getTheme().pageBg,
  },
});
