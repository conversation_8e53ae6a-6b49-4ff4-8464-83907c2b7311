/*
 * 作者：sunhongda
 * 文件：AlarmTopSelectBar.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React, {Component} from 'react';

import {Dimensions} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';

import {DateUtils} from '../../../../imilab-rn-sdk';

import {CalendarList} from 'react-native-calendars';
import moment from 'moment';
import Orientation from 'react-native-orientation';
import {stringsTo} from '../../../../globalization';

const {width, height} = Dimensions.get('window');
let SCREEN_WIDTH = height > width ? width : height;


export default class CalendarShow extends Component {
  constructor(props) {
    super(props);

    this.state = {
      /** 获取当前时间 **/
      dateTime: this.props.current || this.getDataFormatTime(),
    };
  }

  render() {
    return (
      this.getCalendarView.bind(this)()
    );
  }

  getDataFormatTime() {
    return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
  }

  /**
   * 日历控件逻辑
   * @returns {*}
   * @constructor
   */
  getCalendarView() {
    const date1 = moment(this.state.dateTime);
    const date2 = moment();
    const date3 = moment().subtract(1, 'months').startOf('month');
    // 计算月份差
    const pastScrollRange = date1.diff(date3, 'months');
    let futureScrollRange = date2.diff(date1, 'months');
    // 判断是否需要加上不满一个月的部分
    if (futureScrollRange === 0 && date2.month() !== date1.month()) {
      futureScrollRange += 1; // 不满一月的部分算作一个月
    }
    const bigWidth = width > height ? width : height;
    return (
      <CalendarList
        monthFormat={'yyyy' + stringsTo('year') + 'MM' + stringsTo('month')}
        maxDate={new Date().toDateString()}
        minDate={this.props.minDate ? this.props.minDate : null}
        horizontal={true}
        calendarWidth={this.props.isFullScreen ? ((bigWidth / 2) + 30) : SCREEN_WIDTH}
        onDayPress={day => {
          this.setState({
            dateTime: day.dateString
          }, () => {
            this.props.onDayPress(day)
          })
        }}
        pagingEnabled={this.props.isFullScreen ? false : true}
        onVisibleMonthsChange={month => {
          this.props.onVisibleMonthsChange && this.props.onVisibleMonthsChange(month);       
        }}
        current={this.state.dateTime} //设置选中时间
        // 允许滚动到过去的最大月份数。Default = 50
        pastScrollRange={this.props.noControl ? 120 : pastScrollRange}
        // 允许滚动到未来的最大月份数。默认值 = 50
        futureScrollRange={futureScrollRange} // 允许滚动到未来的最大月份数。Default = 50 0再选择上月后无法滑动到本月
        markedDates={
          this.props.dateTimeDotSelect || {
            [this.state.dateTime]: {
              selected: true,
              marked: false,
              disableTouchEvent: true,
              selectedColor: imiThemeManager.theme.primaryColor,
            },
          }
        }
        theme={{
          arrowColor: '#000000', //左右箭头的颜色
          todayTextColor: imiThemeManager.theme.primaryColor,
          selectedDayBackgroundColor: imiThemeManager.theme.primaryColor,
          textDayHeaderFontSize: 12, //星期的字体大小
        }}
      />
    );
  }

  componentDidMount() {
    // Orientation.lockToPortrait();
  }
}
