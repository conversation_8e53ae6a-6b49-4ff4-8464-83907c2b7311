/*
 * 作者：sunhongda
 * 文件：AlarmListPlayerComponent.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React from 'react';

import {
    StyleSheet,
    View,
    Dimensions,
    DeviceEventEmitter,
    ScrollView,
    Text,
    Image,
    TouchableOpacity, BackHandler
} from "react-native";

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';

import {
    CheckBoxButton,
    colors,
    IMIDesignEmptyView,
    IMIDesignEmptyViewNew,
    IMIImageView,
    IMIImageView2,
    RNLine, showLoading, showToast, MessageDialog, imiThemeManager, RoundedButtonView
} from '../../../../imilab-design-ui';

import {
    AlarmType,
    BaseDeviceComponent,
    DateUtils,
    aliAlarmEventCloudApi,
    LetDevice, IMILog
} from '../../../../imilab-rn-sdk';

import AlarmTopSelectBar
    from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/alarm/AlarmTopSelectBar";
import iconPlay from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_play.png';
import iconPause from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_pause.png';
import iconVoiceClose
    from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_voice_close.png';
import iconVoiceOpen from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_voice_open.png';
import iconSave from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_saveforever.png';
import iconFull from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_full.png';
import iconEmpty from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/police_pic_empty.png';
import iconEmptyNew from '../../resources/images/police_pic_empty_new.png';
import Orientation from "react-native-orientation";
import PropTypes from 'prop-types';
import {getScreenWidth, isAndroid, isEmpty, isIos, objHasKey} from "../../../../imilab-rn-sdk/utils/Utils";
import I18n, {stringsTo} from "../../../../globalization/Localize";
import {IMIDownload, IMIPackage,} from '../../../../imilab-rn-sdk';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {PLAYER_EVENT_CODE} from "../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIGotoPage from "../../../../imilab-rn-sdk/native/local-kit/IMIGotoPage";
import moment from "moment";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import AlarmCenterSelectBar from "./AlarmCenterSelectBar";
import iconDot from "../../resources/images/pic_move.png";
import CommonUtils from "../utils/CommonUtils";
import VideoStateType from "../comm/VideoStateType";
import videoStateType from "../comm/VideoStateType";
import TouchableOpacityImage from "../../../../imi-rn-commonView/TouchableOpacityImage/TouchableOpacityImage";
import TimeScaleView2 from "../ui/TimeScaleView2";
import {EVENT_TYPE, EVENT_TYPE_COLOR} from "../live/EventTypeConfig";
import CloudVideoUtil from "../utils/CloudVideoUtil";
import SDVideoUtil from "../utils/SDVideoUtil";
import DateHasManager from "../utils/DateHasManager";
import TabType, {CLOUD_VIDEO_STATUS} from "./TabType";
import AlarmSDType from "../../../../imilab-rn-sdk/components/camera/core/AlarmSDType";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import tabType from "./TabType";
import GlobalUtil from "../utils/GlobalUtil";



/**
 * @Description: 摄像机看家模块
 * @Author:   sunhongda
 * @CreateDate: 2020/9/28 18:02
 * @UpdateUser:     更新者
 * @UpdateDate:  2020/9/28 18:02
 * @UpdateRemark:   更新说明
 */
const defaultEventCurrentTitles = [
    [stringsTo('all_events_str'), AlarmType.ALL],
    [stringsTo('people_event'), AlarmType.PEOPLE],
    [stringsTo('move_event'), AlarmType.MOVE],
    // [stringsTo('alarm_loud_switch'), AlarmType.SOUND],
];

let windowWidth = Dimensions.get('window').width;
let isGoBack = false;//判断数组是否返回父视图
const queryAnimations = 'https://api.jikan.moe/v3/search/anime?q=Fate/Zero';//模拟測試接口

const DemoUrl = ["https://vfx.mtime.cn/Video/2017/01/05/mp4/170105105137886980_480.mp4", "http://vfx.mtime.cn/Video/2019/07/11/mp4/190711113255914537.mp4"];

const LIMIT = 50;
const EVENT_NAME = "IMIDownloadImageScheduler - ";

const TAG = " AlarmListPlayerComponent- ";
let tempVideoList = [];//用于存储临时视频列表
let tempEventList = [];//用于存储根据视频列表开始时间与结束时间请求返回的事件列表
let tempRealVideoList = [];//用于存储筛选出来的视频有效数据
let tempRealPictureList = [];//用于存储筛选出来的图片有效数据
let secondsDay=24*3600;
const threeMin=3*60*1000;//用于请求云存视频对应的看家事件的时间偏移，3分钟
let reqEventType = 0;//当前筛选的事件类型，默认是0，表示全部事件
let ms=3000;//沟通固件多出3秒 筛选云存视频事件，时间宽度最小时间、最大时间偏移3秒
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
export default class AlarmListPlayerComponent extends BaseDeviceComponent {

    static propTypes = {
        /* 事件选择title 数组 */
        eventCurrentTitles: PropTypes.array,
        onClickCancel: PropTypes.func,
        isEdit: PropTypes.bool,
        onClickSelect: PropTypes.func,
        isSelectedAll: PropTypes.bool,
        dataArray: PropTypes.array,
        vipState: PropTypes.number,
        storageType: PropTypes.number,
        // 传入navigation用于返回事件
        navigation: PropTypes.object,
        getDataLength: PropTypes.func,//获取的数据列表的长度，分组后，这里的返回的是分组数量
        lifecycleDay: PropTypes.number,
        modeChange: PropTypes.func,
        modeType: PropTypes.number,
        tabType: PropTypes.number,
        videoStateChangeListener: PropTypes.func,
        pictureChangeListener: PropTypes.func,
        onAllSelectChangeListener: PropTypes.func,//全选、全不选按钮状态改变
        isFullScreen: PropTypes.bool,//是否全屏显示
        eventTypeFlags: PropTypes.number,//是否全屏显示
        eventTimeline: PropTypes.array,//是否全屏显示
        onTimelineVideoTimestampChangeListener: PropTypes.func,//时间轴滚动后，回调方法
        onScrollingListener: PropTypes.func,//时间轴滚动后，回调方法
        onTimelineStartPlay: PropTypes.func,//时间轴滚动后，开始播放
        onDateChangeListener: PropTypes.func,//日期选择后的回调
        onNoTimeLineDataListener: PropTypes.func,//没有查询到时间轴的数据
        onCloudVideoStatusListener: PropTypes.func,//云存视频删除后的回到方法
        recording: PropTypes.bool,//是否处于录制中

    };
    static defaultProps = {
        eventCurrentTitles: null,
        onClickCancel: null,
        isEdit: false,
        onClickSelect: null,
        isSelectedAll: false,
        dataArray: [],
        vipState: -1,
        storageType: 2,//2表示事件型云存
        getDataLength: null,
        modeChange: null,
        modeType: 0,
        tabType: 0,
        videoStateChangeListener:null,
        pictureChangeListener:null,
        onAllSelectChangeListener:null,
        isFullScreen:false,
        eventTypeFlags:0,
        eventTimeline:[],
        onTimelineVideoTimestampChangeListener:null,
        onScrollingListener:null,
        onTimelineStartPlay:null,
        onDateChangeListener:null,
        onNoTimeLineDataListener:null,
        onCloudVideoStatusListener:null,
        recording:false,
    };

    constructor(props) {
        super(props);
        this.state = {
            /* 图片数据集合  */
            dataList: [],
            /* 云存视频数据集合  */
            eventVideoList: [],
            /* sd视频数据集合  */
            sdVideoList: [],
            //分组后的数据集合，用于展示出来的网格数据
            dataGroupList: [],
            muted: false,
            videoCover: undefined,
            flatListHeight: 0,
            isDataEmpty: true,
            fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
            isPlaying: false,
            dataSource: {},
            eventCurrentIndex: 0,
            pageStart: 0,
            /** 某个日期下日历小圆点 **/
            dateTimeDotSelect: {},
            showDeleteTip: false,
            selectTag:'',//选中标记，网格布局时图片、云存视频选中tag
            eventTypeFlags: props.eventTypeFlags,
            showGridError:false
        };
        this.reqTime = this._getDataFormatTime();
        this.reqIntelligentTypeArray = [];

        this.pageIndex = 1;//页码
        this.topSelectBarRoot = undefined;

        this.mCurItem = null;
        this.mCurIndex = 0;
        this.num = 0;
        this.keepLoad = false;//标记是否继续加载后面的数据，true继续，false不继续
        this.loadingTimes = 0;//每次加载为0，失败时重新请求数据的标记位
        this.isAllSelect = false;//默认非全选
        this.isFirstToPlayVideo = true;//第一次进入，有云存视频的情况下，获取数据成功后，播放第一个视频
        this.selectArr = [];//保存编辑模式下，用户选择的item,切换日期，事件后，这个临时数组需要清空，退出编辑模式后，这个数组也需要清空
        //**********************************处理时间轴*******************************
        //**********************************相关的操作*******************************
        this.toStartTime = 0;// 暂存要播放的view的时间戳
        this.videoItem = null;//当前时间轴正在播放的视频item
        this.isFirstReceiveFiles = true;//第一次进入时，开始播放最后一个视频
        this.isFirstReceiveSDFiles = true;//第一次进入时，开始播放最后一个视频
        this.isSDPullOtherDayData = false;//是否开始请求其他天的数据
        this.sdTimeForPlay = 0;//拖动到其他天时，时间需要播放的时间点
        this.shouldPlayCloudFiles = false;//针对滑动后，请求新的一天的时间数据后，是否需要播放这个时间点的视频
        this.reqDate = new Date();
        this.isInitEvent = false;
        //**********************************SD卡回看网格*******************************
        this.isSDPullDown = true;//网格时间轴，是否是下拉刷新
        this.isForegroundPage = true;//标记是否在前台
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        //云存：1319 SD卡：63489
        if (nextProps.eventTypeFlags != this.state.eventTypeFlags) {
            if (!this.isInitEvent){
                this.isInitEvent = true;
                this.setState({eventTypeFlags: nextProps.eventTypeFlags});
            }
        }
    }

    _getDataFormatTime() {
        //SD卡回看event模块进入
        if (GlobalUtil.isEventIn && GlobalUtil.eventStartTime >0){
            return DateUtils.dateFormat2(GlobalUtil.eventStartTime*1000);
        }
        //云存视频event模块进入
        if (GlobalUtil.isCloudEventIn && GlobalUtil.eventStartTime >0){
            return DateUtils.dateFormat2(GlobalUtil.eventStartTime*1000);
        }

        return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
    };

    getDay(day) {
        var today = new Date();
        var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
        var tYear = today.getFullYear();
        var tMonth = today.getMonth();
        var tDate = today.getDate();
        tMonth = this.doHandleMonth(tMonth + 1);
        tDate = this.doHandleMonth(tDate);
        return tYear + "-" + tMonth + "-" + tDate;
    }

    /**
     * 选择日期
     * 父组件会调用此方法去刷新获取数据
     * 暂时用在时间抽全屏时，时间的选择
     * @param date
     */
    calendarDateChange(date){
        this.reqTime = date;
        this.selectArr = [];
        this._queryDataList(true,true);
        // this._getAlarmListData();
    }
    doHandleMonth(month) {
        var m = month;
        if (month.toString().length == 1) {
            m = "0" + month;
        }
        return m;
    }

    render() {
        //20220727@byh
        //1、设置view高度 this.props.isFullScreen && this.props.isFullScreen?{height:0}:
        //2、采用condition?view:null结构，这不能在父组件做，
        //因为会导致子组件卸载导致，会重新请求网络数据
        if (this.props.isFullScreen && this.props.isFullScreen){
            return null;
        }
        let minDay = this.getDay(this.props.vipState == 1 ? -this.props.lifecycleDay : -3);
        // console.log('前3天日期---',minDay);
        let m = DateUtils.doHandleMonth();
        let d = DateUtils.doHandleDay2();
        let dayTitle = stringsTo('day') != "日" ? (m + "/" + d) : `${m}${stringsTo('month')}${d}${stringsTo('day')}`;
        console.log(TAG,"this mode is:"+this.props.modeType)
        let past = 2;
        if (this.props.tabType ==2){
            past = 9;
        }
        return (

            <XView style={[styles.container]}>
                <AlarmCenterSelectBar
                    ref={component => (this.alarmCenterSelectBar = component)}
                    initDate={[dayTitle, stringsTo('all_events_str')]}
                    dateTimeDotSelect={this.state.dateTimeDotSelect}
                    minDate={minDay}
                    // minDate={timeStr}
                    editMode={this.props.isEdit}
                    showTodayBackGroundColor={true}
                    pastScrollRange={past}
                    eventCurrentTitles={this.props.eventCurrentTitles ? this.props.eventCurrentTitles : defaultEventCurrentTitles}
                    onEventItemPress={(currentTitles, index) => {
                        console.log("-------------onEventItemPress-------------------" + currentTitles[index][1],"index:"+index);
                        //selectEventAlarmType
                        let typeParams = aliAlarmEventCloudApi._eventType2Info(currentTitles[index][1]);
                        this.reqIntelligentTypeArray = typeParams ? [typeParams] : [];
                        this.setState({eventCurrentIndex: index}, () => {
                            if (this.props.tabType == TabType.SD){
                                //直接去筛选需要的数据
                                let allArr = this.props.eventCurrentTitles;
                                let tempType = allArr[this.state.eventCurrentIndex][1];
                                //100表示all，现在取不到数据，0为全部
                                reqEventType = tempType == AlarmType.ALL ? AlarmType.COMMON : tempType;
                                this.isFirstToPlayVideo = true;
                                this.pickAndRefreshSDDataByEvent(this.state.sdVideoList);
                            }else {
                                this.selectArr = [];
                                // this._getAlarmListData();
                                this._queryDataList(true,true);
                            }

                        });
                    }}
                    modeChange={(mode) => {
                        let jsonParam = {CloudModeChange:mode};
                        if (this.props.tabType == TabType.SD){
                            jsonParam = {PlayBackModeChange:mode};
                        }
                        IMILogUtil.uploadClickEventValue(jsonParam);

                        this.props.modeChange && this.props.modeChange(mode);
                    }}
                    hideTitleSelect={false}
                    //云存视频 时间轴与网格模式，连续云存不显示切换模式
                    //20230320@baiyihao 连续云存和事件云存 都显示宫格切换
                    showGridLine={(this.props.tabType ==TabType.CLOUD) || this.props.tabType ==TabType.SD}
                    // showGridLine={this.props.tabType ===0}
                    tabType={this.props.tabType}
                    modeType={this.props.modeType}
                    recording={this.props.recording}
                    onDayPress={(date) => {

                        console.log("---------------onDayPress-----------------" + date.toString());
                        this.props.onDateChangeListener && this.props.onDateChangeListener(date);
                        this.reqTime = date;
                        this.selectArr = [];
                        this._queryDataList(true,true);
                    }}
                    selectDay={this.reqTime}
                    renderView={this._renderView.bind(this)}
                >
                </AlarmCenterSelectBar>
                <MessageDialog
                    title={stringsTo('delete_alert')}
                    visible={this.state.showDeleteTip}
                    canDismiss={true}
                    onDismiss={() => {
                        this.setState({showDeleteTip: false})
                    }}
                    buttons={[
                        {
                            text: I18n.t("cancel"),
                            callback: _ => {
                                this.setState({showDeleteTip: false});
                            }
                        },
                        {
                            text: I18n.t("ok_button"),
                            callback: _ => {
                                IMILogUtil.uploadClickEventForCount("DeleteCloudStorageVideo");
                                showLoading(stringsTo('delete_title_loading'), true);
                                this.setState({showDeleteTip: false});
                                this.deleteDataArray();
                                // this.deleteImageArray(0);
                            }
                        },
                    ]}
                >
                </MessageDialog>
            </XView>
        );
    }


    setReqTimeForExitFullscreen(){
        this.alarmCenterSelectBar && this.alarmCenterSelectBar.setSelectDate(this.reqTime);
        let allArr = this.props.eventCurrentTitles;
        let eventTx = allArr[this.state.eventCurrentIndex][0];
        this.alarmCenterSelectBar && this.alarmCenterSelectBar.setSelectEvent(eventTx);
    }

    resetReqTimeAndEvent(timeReq){
        this.reqTime = timeReq;
        this.alarmCenterSelectBar && this.alarmCenterSelectBar.setSelectDate(timeReq);

        this.setState({eventCurrentIndex: 0}, () => {
            let allArr = this.props.eventCurrentTitles;
            let eventTx = allArr[this.state.eventCurrentIndex][0];
            this.alarmCenterSelectBar && this.alarmCenterSelectBar.resetEvent(eventTx);
            this._queryDataList(true,true);
        });
    }
    setReqTime(timeReq){
        this.reqTime = timeReq;
        this.alarmCenterSelectBar && this.alarmCenterSelectBar.setSelectDate(timeReq);
    }

    /**
     * 时间轴滚动后
     * 与当期所选天比较，看看是否还处于同一天
     * @param timeReq 时间轴时间戳
     */
    compareReqTime(timeReq){
        let currentTimeReq = DateUtils.dateFormat2(timeReq);
        if (currentTimeReq != this.reqTime){
            this.setReqTime(currentTimeReq);
            this.props.onDateChangeListener && this.props.onDateChangeListener(currentTimeReq,timeReq);

        }
    }
    //让list数据滚动到顶部
    listScrollToTop(){
        if (this.state.dataGroupList.length>0){
            this.refreshList && this.refreshList.flatList.scrollToLocation({sectionIndex: 0,
                itemIndex: 0, viewPosition:0,viewOffset:40})
        }
    }
    /**
     * 获取当前数据           **  可以进行子类重写 **
     * @param isPullDown  true刷新数据  FALSE加载更多数据
     * @param toPlayVideo 是否需要加载完数据后，播放第一个视频
     * @param cleanData 是否清除原有数据，播放第一个视频
     * @private
     */
    _queryDataList = (isPullDown,toPlayVideo = false,cleanData = true) => {
        console.log(TAG,"isPullDown:"+isPullDown+"  tabType:"+this.props.tabType,"modeType:"+this.props.modeType)
        if (this.state.showGridError){
            this.setState({showGridError:false});
        }
        if (this.props.tabType ===0){
            //云存储视频
            if (this.props.modeType ===0){
                if (toPlayVideo){
                    this.isFirstToPlayVideo = true;
                }
                if (isPullDown){
                    this.listScrollToTop();
                    if (cleanData){
                        this.setState({dataGroupList:[]},()=>{
                            if (this.props.getDataLength) {
                                this.props.getDataLength(this.state.dataGroupList.length);
                            }
                        })
                    }
                }

                this._queryVideoList(isPullDown);
            }else {
                this.props.onCloudVideoStatusListener && this.props.onCloudVideoStatusListener({type:CLOUD_VIDEO_STATUS.LOAD,value:true});

                this.queryTimelineData(true);
            }

        }else if (this.props.tabType === 1){
            //看家图片
            if (isPullDown){
                this.listScrollToTop();
                if (cleanData){
                    this.setState({dataGroupList:[]},()=>{
                        if (this.props.getDataLength) {
                            this.props.getDataLength(this.state.dataGroupList.length);
                        }
                    })
                }
            }


            this._queryEventList(isPullDown);
        }else if (this.props.tabType === 2){
            if (this.props.modeType == 0){
                this.isSDPullDown = isPullDown;
                if (toPlayVideo){
                    this.isFirstToPlayVideo = true;
                }
                if (isPullDown){
                    this.listScrollToTop();
                    if (cleanData){
                        this.setState({dataGroupList:[]},()=>{
                            if (this.props.getDataLength) {
                                this.props.getDataLength(this.state.dataGroupList.length);
                            }
                        })
                    }
                }

                this.queryPlaybackForGird(isPullDown);
            }else {
                //SD卡页面
                if (toPlayVideo){
                    this.isFirstToPlayVideo = true;
                }
                this.queryTimelineData(false);
            }

            // this.queryTimelineData(false);

        }

    };

    dealSDData(){
        this.isFirstToPlayVideo = true;
        //标记
        this.isFirstReceiveSDFiles = true;
        showLoading(stringsTo('commWaitText'), false);
        if (this.props.modeType == 0){
            this.isSDPullDown = true;

            this.listScrollToTop();
            this.onGetSDFilesForGrid();
        }else {
            //SD卡页面
            this.onGetSDFiles();
        }
    }

    /**
     * 查询回看网格数据
     * @param isPullDown 是否是下拉刷新
     */
    queryPlaybackForGird = (isPullDown) =>{
        this.refreshList && this.refreshList.refreshPreLoad(isPullDown);

        let page = isPullDown?0:this.state.pageStart+1;

        let allArr = this.props.eventCurrentTitles;
        let tempType = allArr[this.state.eventCurrentIndex][1];
        //100表示all，现在取不到数据，0为全部
        reqEventType = tempType == AlarmType.ALL ? AlarmType.COMMON : tempType;
        //请求网格回看数据
        this.queryTimelineData(false);
    }

    /**
     * 查询时间轴数据
     * @param isCloud 是否是云存视频  true云存视频 FALSE回看视频
     */
    queryTimelineData(isCloud = true){
        if (this.props.modeType == 1){
            //非网格的时候请求数据给loading，网格请求列表自带loading动画
            showLoading(stringsTo('commWaitText'), true);

            //增加一个20s的超时，20s后使loading消失
            this.loadingTimeout && clearTimeout(this.loadingTimeout);
            this.loadingTimeout = setTimeout(()=>{
                this.showSdLoading = false;
                showLoading(stringsTo('commWaitText'), false);
            },20000);
        }
        if (isCloud){
            CloudVideoUtil.setCloudReceiveDataCallback(this.bindReceiveData)
            CloudVideoUtil.setCloudReceiveEventDataCallback(this.bindReceiveEventData)
            let timestamp = moment(this.reqTime).valueOf();
            let date = new Date();
            date.setTime(timestamp);
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0,0)
            if (DateUtils.isToday(date)){
                //同一天,请求当天的数据
                let video = CloudVideoUtil.getLastestVideoInDay(date.getTime());
                if (video!=null){
                    //标记查询后是否开始播放当天的视频
                    this.isFirstReceiveFiles = true;
                    //从endTime开始查询后面的数据
                    //改为从startTime开始查询，需要对数据进行去重
                    CloudVideoUtil.startToFetchDataStartAtTime(video.startTime)
                }else {
                    this._initCloudVideoData();
                }
            }else {
                let video = CloudVideoUtil.getLastestVideoInDay(date.valueOf());
                this.props.onCloudVideoStatusListener && this.props.onCloudVideoStatusListener({type:CLOUD_VIDEO_STATUS.LOAD,value:true});
                this.isFirstReceiveFiles = true;
                if (video == null){
                    //去请求这一天的数据
                    let recordType = this.props.storageType == 2?1:0;
                    CloudVideoUtil.fetchCloudVideoData(date.valueOf(),recordType,false);
                }else {
                    //已经有数据了，直接展示时间轴数据吧
                    this.onGetFiles();
                }
            }

        }else {
            SDVideoUtil.setSDReceiveDataCallback(this.bindSDReceiveData)
            if (GlobalUtil.needClearSDData){
                this._initSDVideoData();
                return;
            }
            //判断SD卡这天的视频是否已经请求
            //如果有数据：
            //默认当天的数据需要重新请求
            //非当天的不需要去请求，直接去取就好
            let timestamp = moment(this.reqTime).valueOf();
            let date = new Date();
            date.setTime(timestamp);
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0,0)
            if (DateUtils.isToday(date)){
                //同一天,请求当天的数据
                let video = SDVideoUtil.getLastestVideoInDay(date.getTime());
                if (video!=null){
                    //标记查询后是否开始播放当天的视频
                    this.isFirstReceiveSDFiles = true;
                    //从endTime开始查询后面的数据
                    SDVideoUtil.startToFetchDataStartAtTime(video.endTime)
                }else {
                    this._initSDVideoData();
                }
            }else {
                let video = SDVideoUtil.getLastestVideoInDay(date.getTime());
                if (video == null){
                    //去请求这一天的数据
                    this.isFirstReceiveSDFiles = true;
                    SDVideoUtil.fetchSDVideoData(date.getTime(),false);
                }else {
                    //已经有数据了
                    this.dealSDData();
                }
            }
            // this._initSDVideoData();
        }

    }

    _initCloudVideoData(){
        // if (CloudVideoUtil.isFetched){
        //     //数据已经拉取过了
        //     this.onGetFiles();
        //     return;
        // }
        this.props.onCloudVideoStatusListener && this.props.onCloudVideoStatusListener({type:CLOUD_VIDEO_STATUS.LOAD,value:true});
        this.isFirstReceiveFiles = true;
        let timestamp = moment(this.reqTime).valueOf();
        this.timelineView && this.timelineView.scrollToTimestampWithNotify(timestamp, true);
        //开启这个类去加载数据
        CloudVideoUtil.setCloudReceiveDataCallback(this.bindReceiveData)
        CloudVideoUtil.setCloudReceiveEventDataCallback(this.bindReceiveEventData)
        //连续性云存视频录像，与事件型不一致
        let recordType = this.props.storageType == 2?1:0;
        CloudVideoUtil.fetchCloudVideoData(timestamp,recordType)
    }

    /**
     * 拉取这个时间戳对应天的云存数据
     * 拉取完成后，需要播放这个时间点视频
     * @param timestamp
     */
    getCloudTimelineData(timestamp,showLoadingView = false){
        //开启这个类去加载数据
        if (showLoadingView){
            showLoading(stringsTo('commWaitText'), true);
        }
        this.shouldPlayCloudFiles = true;
        let recordType = this.props.storageType == 2?1:0;
        CloudVideoUtil.fetchCloudVideoData(timestamp,recordType,false);
    }


    _initSDVideoData(){
        // if (SDVideoUtil.isFetched){
        //     //数据已经拉取过了
        //     this.onGetSDFiles();
        //     return;
        // }

        this.isFirstReceiveSDFiles = true;
        this.isSDPullOtherDayData = false;
        this.sdTimeForPlay = 0;
        let timestamp = moment(this.reqTime).valueOf();
        this.timelineView && this.timelineView.scrollToTimestampWithNotify(timestamp, true);
        //开启这个类去加载数据
        SDVideoUtil.setSDReceiveDataCallback(this.bindSDReceiveData)
        SDVideoUtil.fetchSDVideoData(timestamp);
    }

    /**
     * 拉取这个时间戳对应天的SD卡视频数据
     * 拉取完成后，需要播放这个时间点视频
     * @param timestamp 请求这个时间戳对应天的数据
     * @param showLoadingView 是否显示loading框
     * @param shouldPlay 请求完数据是否播放请求后的数据
     */
    getSDTimelineData(timestamp,showLoadingView = false,shouldPlay = true){
        //开启这个类去加载数据
        console.log("timestamp",timestamp)
        if (showLoadingView){
            showLoading(stringsTo('commWaitText'), true);
            //增加一个20s的超时，20s后使loading消失
            this.loadingTimeout && clearTimeout(this.loadingTimeout);
            this.loadingTimeout = setTimeout(()=>{
                this.showSdLoading = false;
                showLoading(stringsTo('commWaitText'), false);
            },20000);
        }
        if (shouldPlay){
            this.isFirstReceiveSDFiles = true;
            this.isSDPullOtherDayData = true;
        }

        //这个是相对于当前选中时间的偏移
        let date = new Date();
        date.setTime(timestamp);
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0,0);
        this.sdTimeForPlay = timestamp - date.getTime();
        if (shouldPlay){
            //指针不要定位到请求的这个天上
            this.timelineView && this.timelineView.scrollToTimestampWithNotify(timestamp, true);
        }
        console.log("just do",timestamp)
        SDVideoUtil.setSDReceiveDataCallback(this.bindSDReceiveData)
        SDVideoUtil.fetchSDVideoData(timestamp,false);
    }

    /**
     *
     * @param startTime 刷新去去请求后的SD卡数据的开始时间
     */
    refreshSDTimelineData(startTime){
        let timestamp = moment(this.reqTime).valueOf();
        //开启这个类去加载数据
        SDVideoUtil.setSDReceiveDataCallback(this.bindSDReceiveData)
        SDVideoUtil.refreshSDVideoData(startTime,timestamp)

    }
    bindReceiveData = (isOk)=>{
        console.log(TAG,"bindReceiveData in");


        if (!isOk){
            //出现了不可知的啥问题
            console.log("get cloud data wrong");
            this.shouldPlayCloudFiles = false;
            showLoading(stringsTo('commWaitText'), false);
            return;
        }
        //做一个标记，是否已经成功get到数据
        this.isFileReceived = true;
        this.onGetFiles();
    }

    bindReceiveEventData = (isOk)=>{
        console.log("bindReceiveEventData");
        if (!isOk){
            showLoading(stringsTo('commWaitText'), false);
            return;
        }
        //只做刷新时间轴事件渲染处理，只刷新UI
        this.timelineView && this.timelineView.onReceiveCloudDatas();
    }

    bindSDReceiveData = (isOk,monthOk = true)=>{
        if (this.props.tabType !== TabType.SD){
            //如果不是SD卡，不需要去处理后面的数据
            return;
        }
        console.log(TAG,"bindReceiveSDData in");
        if (this.isForegroundPage){
            showLoading(stringsTo('commWaitText'), false);
        }
        if (!isOk){
            if (!monthOk && this.isForegroundPage && GlobalUtil.isSdCanPullData){
                //异常卡
                showToast(stringsTo('commLoadingFailText'));
            }
            //出现了不可知的啥问题
            if (this.props.modeType == 1){
                if (SDVideoUtil.getLastestVideo() == null){
                    this.props.onNoTimeLineDataListener && this.props.onNoTimeLineDataListener(true);
                    return;
                }
            }
            //网格回看，这里出问题
            if (this.props.modeType == 0){
                //注意：下面这行log放开会导致插件卡死，可能是this.refreshList 对象太大，很坑  注意！注意！注意！
                // console.log("do error",this.isSDPullDown,this.refreshList);
                this.refreshList && this.refreshList.refreshLoaded(false, this.isSDPullDown, true, true);
                if (this.isSDPullDown){
                    //只有刷新的时候出错的时候才显示这个按钮
                    this.setState({showGridError:true});
                }

            }

            return;
        }
        IMILog.logD("DateHasManager","success");
        //做一个标记，是否已经成功get到数据
        this.isFileSDReceived = true;
        if (this.props.modeType == 0){
            //网格类型的SD卡
            this.onGetSDFilesForGrid();
        }else {
            this.onGetSDFiles();
        }
        GlobalUtil.needClearSDData = false;
    }

    onGetFiles(){
        console.log(TAG,"onGetFiles in");
        //全屏选择日期后，这个判断会导致不会播放选中天的视频
        // if (this.timelineView == null) {
        //     showLoading(stringsTo('commWaitText'), false);
        //     return;
        // }

        // let video = CloudVideoUtil.getLastestVideo();
        this.reqDate.setTime(moment(this.reqTime).valueOf());
        this.reqDate.setHours(0);
        this.reqDate.setMinutes(0);
        this.reqDate.setSeconds(0,0);
        let video = CloudVideoUtil.getLastestVideoInDay(this.reqDate.valueOf());

        this.timelineView && this.timelineView.onReceiveCloudDatas();
        console.log("onGetFiles",this.reqTime,video);
        if (video == null) {
            console.log("find cloud items failed");
            showLoading(stringsTo('commWaitText'), false);
            this.props.onNoTimeLineDataListener && this.props.onNoTimeLineDataListener(false)
            return;
        }
        //数据为空，展示数据为空时的UI
        // this.setState({ isEmpty: false });
        console.log(TAG,"onGetFiles success");

        // this.timelineView && this.timelineView.onReceiveCloudDatas();


        if (!this.isFirstReceiveFiles) {
            showLoading(stringsTo('commWaitText'), false);
            return;
        }

        this.isFirstReceiveFiles = false;
        setTimeout(() => {
            //加2秒，为了等云存视频拉取完，否则找第一个播放视频可能不是在最初的时候
            // let video = CloudVideoUtil.getLastestVideo();
            let video = CloudVideoUtil.getLastestVideoInDay(this.reqDate.valueOf());
            if (video == null) {
                console.log("find cloud items failed");
                return;
            }
            this.timelineView && this.timelineView.scrollToTimestamp(video.startTime);
            //准备开始去播放
            this.toStartTime = video.startTime;
            console.log("find cloud onTimelineStartPlay");
            this.props.onTimelineStartPlay && this.props.onTimelineStartPlay(video.startTime,true);
            showLoading(stringsTo('commWaitText'), false);
        },2000);

    }

    onGetSDFiles(){
        console.log(TAG,"onGetFiles in");
        // if (this.timelineView == null) {
        //     return;
        // }
        this.reqDate.setTime(moment(this.reqTime).valueOf());
        this.reqDate.setHours(0);
        this.reqDate.setMinutes(0);
        this.reqDate.setSeconds(0,0);
        let video = SDVideoUtil.getLastestVideoInDay(this.reqDate.valueOf());

        this.timelineView && this.timelineView.onReceiveSDDatas();
        if (video == null) {
            console.log("find sd items failed");
            //这里我们认为没有SD卡的视频数据
            //需要通知父组件，当前时间轴的数据
            this.props.onNoTimeLineDataListener && this.props.onNoTimeLineDataListener(true);
            return;
        }
        //数据为空，展示数据为空时的UI
        // this.setState({ isEmpty: false });
        console.log(TAG,"onSDGetFiles success");

        // this.timelineView && this.timelineView.onReceiveSDDatas();
        if (!this.isFirstReceiveSDFiles) {
            return;
        }

        this.isFirstReceiveSDFiles = false;
        setTimeout(() => {
            // let video = SDVideoUtil.getLastestVideo();
            if (this.isSDPullOtherDayData && this.sdTimeForPlay>0){
                this.timelineView && this.timelineView.scrollToTimestamp(this.sdTimeForPlay);
                this.props.onTimelineStartPlay && this.props.onTimelineStartPlay(this.sdTimeForPlay,true,true);
                this.isSDPullOtherDayData = false;
                this.sdTimeForPlay = 0;
            }else {
                let video = SDVideoUtil.getLastestVideoInDay(this.reqDate.valueOf());
                if (video == null) {
                    console.log("find cloud items failed");
                    return;
                }
                //这个是最初的时间轴所处于的位置
                //这个位置不太好控制在哪里显示
                // this.timelineView && this.timelineView.scrollToTimestamp(video.startTime);
                //准备开始去播放
                this.toStartTime = video.startTime;
                this.props.onTimelineStartPlay && this.props.onTimelineStartPlay(video.startTime,true);
            }

            //this._startPlay(true);
        });

    }

    /**
     * 回看网格类型数据回调处理
     * 1、数据倒序，最新的时间需要在最上方（已经排过序了）
     * 2、时间处理，视频的时长处理(已在请求数据时，时长已转换)
     * 3、筛选数据，根据事件类型筛选数据
     * 4、分组处理，根据小时段，分组
     * 5、完成后播放第一个视频
     */
    onGetSDFilesForGrid(){
        let timestamp = moment(this.reqTime).valueOf();
        let videoArray = SDVideoUtil.getSDDataByTimestamp(timestamp);
        this.refreshList && this.refreshList.refreshLoaded(true, this.isSDPullDown, true, false);
        //console.log("onGetSDFilesForGrid",videoArray)
        //3、筛选数据
        this.pickAndRefreshSDDataByEvent(videoArray);

    }

    /**
     * 筛选SD卡网格数据
     */
    pickAndRefreshSDDataByEvent(videoArray = []){
        //reqEventType，数据请求的类型
        //回看事件类型与看家类型不一致，这里需要做转换
        let sdEventType;
        switch (reqEventType){
            case AlarmType.MOVE:
                sdEventType = AlarmSDType.MOVE;
                break;
            case AlarmType.PEOPLE:
                sdEventType = AlarmSDType.PEOPLE;
                break;
            case AlarmType.ABNORMAL_SOUND:
                sdEventType = AlarmSDType.ABNORMAL_SOUND;
                break;
            case AlarmType.LAUGHTER:
                //用笑声代替，无人
                sdEventType = AlarmSDType.NOBODY;
                break;
            case AlarmType.KEY_AREA:
                sdEventType = AlarmSDType.KEY_AREA;
                break;
            case AlarmType.INTRUSION:
                sdEventType = AlarmSDType.INTRUSION;
                break;
            case AlarmType.VEHICLE:
                sdEventType = AlarmSDType.VEHICLE;
                break;
            case AlarmType.BIKE:
                sdEventType = AlarmSDType.BIKE;
                break;
            default:
                sdEventType = AlarmSDType.ALL;
                break;
        }

        let usefulVideoArray = videoArray;
        if (sdEventType !== AlarmSDType.ALL){
            usefulVideoArray = videoArray.filter((item,index)=>{
                return sdEventType == item.eventType;
            })
        }

        if (usefulVideoArray.length>0 && this.isFirstToPlayVideo){

            //1、普通播放  2、event模块进入，携带某个item的开始时间
            if (GlobalUtil.isEventIn && GlobalUtil.eventStartTime > 0){
                //app event模块进来
                //查询出符合条件的视频
                //注意：数据时一包一包返回的，返回的某包数据可能不包含需要的数据，可能在下一包中，也可能在下下包中。。。
                //直到找到视频，能从event跳过来的回看视频，一定是能找到对应视频的
                //0点附近的数据会比较慢，因为可能需要多次请求才能请求到
                let item = this.selectRightSdVideo(usefulVideoArray,GlobalUtil.eventStartTime);
                if (item != null){
                    this.isFirstToPlayVideo = false;
                    GlobalUtil.isEventIn = false;
                    GlobalUtil.eventStartTime = 0;
                    this.setState({selectTag:item.startTime})
                    this.props.pictureChangeListener && this.props.pictureChangeListener(item);
                }
            }else {
                //播放第一个视频文件
                this.isFirstToPlayVideo = false;
                this.setState({selectTag:usefulVideoArray[0].startTime})
                this.props.pictureChangeListener && this.props.pictureChangeListener(usefulVideoArray[0]);
            }

        }
        //区分无数据时的显示
        if (usefulVideoArray.length>0){
            this.refreshList && this.refreshList.refreshLoaded(true, false, true, false);
        }else {
            this.refreshList && this.refreshList.refreshLoaded(true, true, true, false);
        }


        let groupData  = this.doSDDataSubGroup(usefulVideoArray,false);
        this.setState({
            sdVideoList:videoArray,
            dataGroupList:groupData,
        },()=>{
            if (this.props.getDataLength) {
                this.props.getDataLength(this.state.dataGroupList.length);
            }
        })
    }

    selectRightSdVideo(videoArray = [],startTime = 0){
        if (videoArray.length === 0 || startTime === 0){
            return null;
        }
        let item = null;
        //传入的开始时间是秒 videoArray里面存入的时间是毫秒
        startTime = startTime*1000;
        for (let i = 0; i < videoArray.length; i++) {
            let child = videoArray[i];
            if (child.startTime <= startTime && child.endTime >startTime){
                //开始时间小于等于传入的时间   结束时间大于传入时间
                item = child;
                break;
            }
        }
        return item;
    }

    reRenderCloudTimeLineView(offsetTime = 0){
        this.timelineView && this.timelineView.onReceiveCloudDatas();
        this.timelineView && this.timelineView.scrollToTimestamp(offsetTime);
    }

    reRenderSDTimeLineView(offsetTime = 0){
        this.timelineView && this.timelineView.onReceiveSDDatas();
        this.setReqTime(this.reqTime);
        //矫正隐藏后再显示，指针偏移量可能不对
        this.timelineView && this.timelineView.scrollToTimestamp(offsetTime);
    }

    //时间轴的滚动
    scrollToTimestamp(timestamp){
        this.timelineView && this.timelineView.scrollToTimestamp(timestamp);
    }

    /**
     * 查询看家图片列表
     * @param isPullDown 是否是刷新数据
     * @param forSD 是否是为了展示回看
     * @private
     */
    _queryEventList(isPullDown,forSD = false){
        this.refreshList && this.refreshList.refreshPreLoad(isPullDown);
        let page = isPullDown ? 0 : this.state.pageStart + 1;
        this.setState({pageStart: page});
        tempRealPictureList =[];
        let paramsToken = isPullDown ? "" : this.reqToken;
        this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown, page,forSD);
    }
    /**
     * 获取云存视频列表
     * @param isPullDown
     * @private
     */
    _queryVideoList = (isPullDown) => {
        this.refreshList && this.refreshList.refreshPreLoad(isPullDown);

        let page = isPullDown?0:this.state.pageStart+1;//断网点击重试会自动+1
        this.setState({pageStart:page});

        console.log(" this._queryDataList:"+isPullDown);
        //外部触发请求数据，都需要把这个清空
        tempRealVideoList = [];
        tempVideoList = [];
        let allArr = this.props.eventCurrentTitles;
        let tempType = allArr[this.state.eventCurrentIndex][1];
        //100表示all，现在取不到数据，0为全部
        reqEventType = tempType == AlarmType.ALL ? AlarmType.COMMON : tempType;
        this._getAliVideoListOnlyNew(this.reqTime,   isPullDown,page,true);
    };


    /**
     *  切换事件的时间点，
     * @param time  格式化过后的 yyy/mm/dd 格式时间
     * @param paramsToken
     * @param intelligentTypeArray 需要查询的事件类型
     * @param isPullDown
     * @private
     */
    _changeEventTime(time, paramsToken, intelligentTypeArray, isPullDown, page,forSD) {
        let dataList = [];
        if (!isPullDown){
            //如果是上拉加载
            dataList = this.state.dataList;
        }

        //根据选择类型获取数据，获取后不需要额外处理
        let curStartTime = DateUtils.startUnix(time);
        let curEndTime = DateUtils.endUnix(time);
        let event = 0;
        //根据index获取类型不准确，应该关联所在数组
        let allArr = this.props.eventCurrentTitles;
        if (allArr.length > this.state.eventCurrentIndex) {
            let tempType = allArr[this.state.eventCurrentIndex][1];
            //100表示all，现在取不到数据，0为全部
            event = tempType == 100 ? 0 : tempType;
        }

        console.log(" eventStorageSessions " + this.state.eventCurrentIndex + '---' + event + " curStartTime " + curStartTime + " curEndTime " + curEndTime + " time  " + time + " intelligentTypeArray " + intelligentTypeArray);

        aliAlarmEventCloudApi.getAliPictureList(LetDevice.deviceID,
            curStartTime,
            curEndTime,
            page, 100).then((data) => {
            if (this.props.tabType != TabType.PIC){
                //不是图片，没必要在做后面的操作了
                return;
            }
            let result = JSON.parse(data);

            let {pictureList} = result;

            // this.reqToken = token;

            console.log('getAliPictureList  then-> json ' + page + '-----' + data);

            let isSuccess = true;
            //每次加载100条数据，标记是否还有数据
            let noMoreData = pictureList.length < 100 //!objHasKey(pictureList);

            //如果此时没有数据，不认为没有更多数据
            console.log('getAliPictureList  dataList  noMoreData   -> ' + noMoreData + '----');

            //根据事件类型，筛选需要的数据
            if (event != 0) {  //筛选数组
                pictureList = pictureList.filter(item => item.eventType == event)
            }

            //如果是下拉刷新，并且不是继续加载数据
            // let itemList = isPullDown && !this.keepLoad ? pictureList : [...dataList, ...pictureList];
            tempRealPictureList = isPullDown && !this.keepLoad ? pictureList : [...tempRealPictureList, ...pictureList];
            console.log("isPullDown", isPullDown, "keepLoad", this.keepLoad, dataList.length, pictureList.length, tempRealPictureList.length);

            console.log("getAliFileList  dataList.length:", tempRealPictureList.length, this.state.dataList.length)
            /**
             * 请求回来的有效数据不满足条数，
             * 这里暂时定位30条
             * 不满足再去请求一次
             **/
            if (!noMoreData && tempRealPictureList.length<30){
                this.keepLoad = true;
                let page = this.state.pageStart + 1;
                this.setState({pageStart: page});
                let paramsToken = isPullDown ? "" : this.reqToken;
                this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown, page,forSD);
                return;
            }
            //这个数据是最终的数据
            let itemList = [...dataList,...tempRealPictureList];
            //修改FlatList的请求状态
            //第三个参数主要配合noMoreData展示无更多数据文案
            if (noMoreData && isPullDown){
                //1、后面没有数据 2、下拉刷新
                if (itemList.length>0){
                    this.refreshList && this.refreshList.refreshLoaded(true, false, true, false);
                }else {
                    //请求后没有数据，需要展示暂无数据
                    this.refreshList && this.refreshList.refreshLoaded(true, true, true, false);
                }
            }else {
                this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, noMoreData, false);
            }
            //根据时间对数据进行分组，9：00-10：00   8：00-9：00类型分组

            let groupData = this.doDataSubgroup(itemList);
            if (itemList.length>0){
                let tag = `${itemList[0].pictureId}_${moment(itemList[0].pictureTimeUTC).valueOf()}`;
                this.setState({selectTag:tag})
                this.props.pictureChangeListener && this.props.pictureChangeListener(itemList[0]);

            }
            this.setState({
                dataList: itemList,
                dataGroupList: groupData,
            }, () => {
                this.keepLoad = false;

                if (this.props.getDataLength) {
                    this.props.getDataLength(this.state.dataGroupList.length);
                }
            });


        }).catch((error) => {
            console.log("error:", error)
            // this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, token === undefined, netWorkException(error));
            let isSuccess = false;
            this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown);
            if (isPullDown){
                //只有刷新的时候出错的时候才显示这个按钮
                this.setState({showGridError:true});
            }
            if (this.props.getDataLength) {
                this.props.getDataLength(this.state.dataGroupList.length);
            }
        });
    }

    /**
     * 请求云存视频列表数据
     * @param reqTime
     * @param isPullDown
     * @param page
     * @private
     */
    _getAliVideoListOnlyNew(reqTime,isPullDown=true,page,isFirst = false,isOnly=false){
        let startTime=moment(reqTime).valueOf()/1000;
        let endTime=startTime+secondsDay;
        console.log("page::then:"+page)
        if (GlobalUtil.isCloudEventIn){
            GlobalUtil.isCloudEventIn = false;
            GlobalUtil.eventStartTime = 0;
        }
        let recordType = this.props.storageType == 2?1:0;
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            recordType,
            page, 20,true).then( (data) => {
            let {recordFileList, nextValid, nextBeginTime} = data;
            console.log('__getAliVideoList  _getAliVideoListOnly  recordFileList:' + JSON.stringify(data))
            //console.log('__getAliVideoList    recordFileList77:' + nextValid+isFirst+isOnly)
            if (this.props.tabType != TabType.CLOUD){
                //不是云存视频，没必要在做后面的操作了
                return;
            }
            if (page == 0 && !nextValid && recordFileList.length === 0) {
                showLoading(false);
                this.refreshList && this.refreshList.refreshLoaded(true, isPullDown, true, false);
                this.netError=false,
                    this.loadingTimes=0;
                this.setState({
                    isPlay: false,
                    fileName: "",
                    noVideo: false,
                    todayIsNoData: true,
                    isPlayFinish: false,
                    selectEventId: "0",
                })
            } else {
                if(!nextValid && recordFileList.length === 0){
                    showLoading(false);
                    this.refreshList && this.refreshList.refreshLoaded(true, false, true, false);
                }

                if (recordFileList.length > 0) {
                    this.netError=false;
                    this.loadingTimes=0;
                    //每次请求前清空这个数组
                    tempEventList = [];
                    //请求回来的视频列表
                    tempVideoList = recordFileList;
                    //最小时间-3分钟是因为，用视频最小时间请求不到对应事件
                    console.log("recordFileList::::"+moment(recordFileList[recordFileList.length - 1].beginTimeUTC).valueOf()+" ---"+moment(recordFileList[recordFileList.length - 1].beginTime).valueOf())
                    //编辑模式，不要去播放了isFirstToPlayVideo

                    this.getEventListAllNew(moment(recordFileList[recordFileList.length - 1].beginTimeUTC).valueOf()-threeMin,
                        moment(recordFileList[0].endTimeUTC).valueOf(),
                        0, isPullDown, nextValid,isFirst,page);
                }
            }
        }).catch(err=>{
            console.log("page::catch:"+page,err);
            this.netError=true,
            this.refreshList && this.refreshList.refreshLoaded(false, isPullDown, true, true); //加载失败
            showLoading(false);
            if (isPullDown){
                //只有刷新的时候出错的时候才显示这个按钮
                this.setState({showGridError:true});
            }
        })
    }

    /**
     * 请求云存视频对应的看家事件
     * 请求某段时间内的全部事件数据，可能会请求多次
     * @param curStartTime 开始时间
     * @param curEndTime 结束时间
     * @param page 页数
     * @param isPullDown 是下拉刷新还是加载更多数据
     * @param nextValidParam 标记视频列表后面是否还有数据
     * @param isFirst 这个标记主要用于请求完数据后，是否播放第一个视频，刚进入页面，第一次请求需要播放视频
     */
    getEventListAllNew(curStartTime,curEndTime, page,isPullDown=true,nextValidParam=true,isFirst = false,pageVideo=0) {
        let stateVideoList=this.state.eventVideoList;
        //根据选择类型获取数据，获取后不需要额外处理
        // console.log( '__getAliVideoList:' +" curStartTime " + curStartTime + " curEndTime " + curEndTime + " time  " + page +"isFirst"+isFirst);
        aliAlarmEventCloudApi.getAliFileList(LetDevice.deviceID,
            curStartTime,
            curEndTime,
            reqEventType,
            page, 100).then((data) => {
            if (this.props.tabType != TabType.CLOUD){
                //不是云存视频，没必要再去请求后续的云存事件了
                return;
            }
            console.log('__getEventListAllNew  getEventListAll:'+data)
            let result = JSON.parse(data);
            let {eventList, nextValid} = result;
            //后面还有数据
            tempEventList = [...tempEventList,...eventList];
            console.log("[[[[[[[[[[[[[",tempVideoList);
            console.log("[[[[[[[[[[[[[",tempEventList);
            console.log("[[[[[[[[[[[[[nextValid",nextValid);
            if (nextValid){
                let tempPage = page+1;
                this.getEventListAllNew(curStartTime,curEndTime,tempPage,isPullDown,nextValidParam,isFirst,pageVideo);
            }else {
                //已加载完时间段内所有的事件
                //这边开始处理数据，从我们获取的视频列表中，筛选出事件
                let valueSelect=false;
                let realVideoList = [];
                for (let value in tempVideoList) {
                    let {beginTime,endTime,beginTimeUTC,endTimeUTC} = tempVideoList[value];
                    //标记是否能加入到展示的视频队列里面
                    valueSelect=false;
                    let times=0;//加载数据次数,为了一个视频对应多种类型
                    let videoValue=tempVideoList[value];
                    let isTure = false;
                    let isThreeAdd = false;
                    let mlTime = moment(videoValue.endTimeUTC).diff(moment(videoValue.beginTimeUTC));
                    videoValue['duration'] = CommonUtils.formatTime(mlTime);
                    if (tempEventList.length>0){
                        //筛选出某个视频的全部事件，原云存视频页最多只筛选出3个事件就可停止
                        //这里需要筛选出全部，时间性能上会受影响
                        for (let valueInt in tempEventList) {
                            let {eventTime,eventType,eventTimeUTC} =tempEventList[valueInt];
                            eventTime=moment(eventTimeUTC).valueOf()
                            isTure=(eventTime<=(moment(endTimeUTC).valueOf()+ms))&&((moment(beginTimeUTC).valueOf()-ms)<=eventTime)
                            if (isTure){
                                valueSelect=true;

                                if (videoValue.hasOwnProperty("eventType")){
                                    if (videoValue.eventType.indexOf(eventType) === -1){
                                        videoValue.eventType.push(eventType);
                                    }

                                }else {
                                    let itemValue={"eventType":[eventType],"eventTime":beginTimeUTC,"thumbUrl":tempVideoList[value].snapshotUrl,isVideo:true}
                                    videoValue = Object.assign(videoValue,itemValue);
                                }
                            }
                        }
                        if (valueSelect){
                            //有事件在我们这个视频里面
                            realVideoList.push(videoValue);
                        }
                    }
                    //如果不是筛选事件，而是全部事件，这个视频筛选完全部事件，还是没有对应事件，给个默认
                    //这里属于异常情况
                    if (!valueSelect && reqEventType === 0){
                        console.log("[[[[[[[[[[[[[1111111");
                        let list=tempVideoList[value];
                        //连续云存有宫格，会存在没有事件的视频，连续型云存就不需要添加默认事件
                        //类型给1，移动侦测吧
                        if (this.props.storageType == 2){
                            list["eventType"]=[1];
                        }
                        list["eventTime"]=beginTimeUTC;
                        list["thumbUrl"]=tempVideoList[value].snapshotUrl;
                        list["isVideo"]=true;
                        realVideoList.push(list);
                    }
                }
                // console.log("加载后的realVideoList：",realVideoList.length+"-------"+tempRealVideoList.length);
                //下拉刷新情况下：处理筛选条件，如果是事件筛选，筛选出的视频不满足我们需要的条数，我们需要继续加载，下一页视频继续筛选
                //筛选时暂时选择10条作为判断依据，如果小于10条，我们再去加载下一页数据
                //加载更多数据时，如果小于5条，我们再去加载下一页数据
                //如果是加载更多数据时，realVideoList

                //这个存储的是，一次表面的请求的数据集合，不管是上拉加载还是下拉刷新，这个数据每次都要清空掉
                tempRealVideoList = [...tempRealVideoList,...realVideoList]
                // console.log("加载后的realVideoList222：",realVideoList.length+"-------"+tempRealVideoList.length);
                if(isPullDown){

                    if (tempRealVideoList.length<10 && nextValidParam){
                        //这时去加载下一页视频数据
                        let nextPage = this.state.pageStart + 1;
                        this.setState({pageStart:nextPage});
                        this._getAliVideoListOnlyNew(this.reqTime,isPullDown,nextPage,isFirst);
                        return;
                    }


                }else {
                    if (tempRealVideoList.length<5 && nextValidParam){
                        //这时去加载下一页视频数据
                        let nextPage = this.state.pageStart + 1;
                        this.setState({pageStart:nextPage});
                        this._getAliVideoListOnlyNew(this.reqTime,isPullDown,nextPage,isFirst);
                        return;
                    }
                }
                showLoading(false);
                //下拉刷新请求完全部数据，列表底部不会展示已加载全部数据
                //测试要求需要显示已加载全部数据
                let result= isPullDown?tempRealVideoList:[...stateVideoList,...tempRealVideoList];
                if (!nextValidParam && isPullDown && result.length>0){
                    //1、后面没有数据 2、下拉刷新
                    this.refreshList && this.refreshList.refreshLoaded(true, false, true, false);
                }else {
                    this.refreshList && this.refreshList.refreshLoaded(true, isPullDown, !nextValidParam, false);
                }
                //寻找第一个视频并播放
                // console.log("[[[[[[[[[[[[[1111111"+isPullDown+"  "+isFirst+"  "+tempRealVideoList.length);
                //切换日期后，显示选择后的
                // if (isFirst && tempRealVideoList.length>0 && reqEventType !=0){
                // //编辑模式，不要去播放了isFirstToPlayVideo
                // if (isFirst && tempRealVideoList.length>0 && !this.props.isEdit){
                //     this.getByEventName(tempRealVideoList[0].fileName);
                // }
                //延迟初始化播放相关逻辑
                // this.delayTimeout && clearTimeout(this.delayTimeout);
                // this.delayTimeout = setTimeout(res=>{
                //     if (tempRealVideoList.length>0 && !this.props.isEdit && this.isFirstToPlayVideo){
                //         // this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:tempRealVideoList[0].fileName});
                //         this.getByEventName(tempRealVideoList[0].fileName,tempRealVideoList[0].eventTime);
                //
                //         this.isFirstToPlayVideo = false;
                //     }
                // },400);

                let groupData  = this.doDataSubgroup(result,false);
                this.setState({
                    eventVideoList:result,
                    dataGroupList:groupData,
                },()=>{
                    if (this.props.getDataLength) {
                        console.log("[[[[[[")
                        this.props.getDataLength(this.state.dataGroupList.length);
                    }
                })
            }
        }).catch((error) => {
            console.log("[[[[[[[[[[[",error)
            if (this.loadingTimes<2){
                this.loadingTime&&clearTimeout(this.loadingTime);
                this.loadingTime = setTimeout(()=>{
                    this.loadingTimes=this.loadingTimes+1;
                    this.getEventListAllNew(curStartTime,curEndTime,page,isPullDown,nextValidParam,isFirst,pageVideo);
                },500);
            }else {
                //还是没有拉取到正确数据，不要再去做数据拉取了，直接提示用户吧
                showToast(stringsTo('waitFailedTip'));
                console.log("page:event:"+pageVideo);
                if (this.props.getDataLength) {
                    this.props.getDataLength(this.state.dataGroupList.length);
                }
                this.refreshList && this.refreshList.refreshLoaded(false, isPullDown, true, false);
                //tempVideoList = [];
                //this._getAliVideoListOnlyNew(this.reqTime,true,pageVideo==0?0:pageVideo,isFirst,true);

            }
        });

    }
    renderListOver(){
        if (tempRealVideoList.length>0 && !this.props.isEdit && this.isFirstToPlayVideo){
            // this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:tempRealVideoList[0].fileName});
            this.getByEventName(tempRealVideoList[0].fileName,tempRealVideoList[0].eventTime);

            this.isFirstToPlayVideo = false;
        }
    }
    /**
     * 点击时通过evetnId判断视频
     * only:是否第一次有ID加载
     * @param eventId
     */
    getByEventName(fileName,eventTime,isOnly=false,isFirst=false){
        aliAlarmEventCloudApi.getByEventName(LetDevice.deviceID,
            fileName, true).then((data) => {
                console.log("-------",fileName);
            // console.log("getVideo   fileName: data:",data)
            this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:videoStateType.VIDEO_CHANGE});

            this.setState({
                noVideo:false,
                todayIsNoData:false,
                isPlayFinish:false,
                isPlay:false,
                fileName:fileName,
                selectTag:fileName
            },()=>{

                if (isIos()){
                    this.setState({isLoading:true,showPauseView:false});
                }
                if (isAndroid()){
                    this.delayToPlay && clearTimeout(this.delayToPlay);
                    this.delayToPlay = setTimeout(res=>{
                        this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:fileName,eventTime:eventTime,isFirstVideo:true});
                    },800);
                }else {
                    this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:fileName,eventTime:eventTime,isFirstVideo:true});
                }
            })
        }).catch(_=>{
            //如果未找到视频，需要在播放上面盖一层图片
            console.log('getVideo   error:',_)
            this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
            this.setState({
                noVideo:true,
                todayIsNoData:false,
                isPlayFinish:false,
                isPlay:false,
                selectEventId:"0",
            })
        });
    }

    doSDDataSubGroup(data){
        for (let i = 0; i < data.length; i++) {
            let timeUTC = data[i].startTime;
            let hour = moment(timeUTC).format('HH');
            let nextHourInt = parseInt(hour) + 1;
            let nextHour = nextHourInt > 9 ? nextHourInt : `0${nextHourInt}`;
            let tag = `${hour}:00-${nextHour}:00`;
            data[i].title = tag;
        }

        let newArr = []
        data.forEach((item, i) => {
            let index = -1;
            //添加一个选中标记
            item.selected = false;
            let alreadyExists = newArr.some((newItem, j) => {
                if (item.title === newItem.title) {
                    index = j;
                    return true;
                }
            });
            //数据处理成这样是为了后面进行宫格展示
            if (!alreadyExists) {
                newArr.push({
                    title: item.title,
                    data: [[item]]
                });
            } else {
                newArr[index].data[0].push(item);
            }
        })
        console.log("分组后的数据", newArr);
        return newArr;

    }
    /**
     * 对数据进行分组
     * 对分组数据多封装了一层，改为多嵌套一层数组
     * sectionList不支持网格，需要自己实现，
     * 此种做法为暂时尝试方法的最优解
     **/
    doDataSubgroup(data,isPic = true) {
        //生成tag标记，标记该数据应该位于哪个分组中
        for (let i = 0; i < data.length; i++) {
            let timeUTC = isPic?data[i].pictureTimeUTC:data[i].eventTime;
            let hour = moment(timeUTC).format('HH');
            let nextHourInt = parseInt(hour) + 1;
            let nextHour = nextHourInt > 9 ? nextHourInt : `0${nextHourInt}`;
            let tag = `${hour}:00-${nextHour}:00`;
            data[i].title = tag;
        }
        let newArr = []

        data.forEach((item, i) => {
            let index = -1;
            //添加一个选中标记
            item.selected = false;
            //看家图片
            let hasIndex = this.selectArr.findIndex(selectItem=>{
                if (isPic){
                    return item.pictureId == selectItem.pictureId
                }
               return item.fileName == selectItem.fileName;
            })
            if (hasIndex !==-1){
                item.selected = true;
            }

            let alreadyExists = newArr.some((newItem, j) => {
                if (item.title === newItem.title) {
                    index = j;
                    return true;
                }
            });
            //数据处理成这样是为了后面进行宫格展示
            if (!alreadyExists) {
                newArr.push({
                    title: item.title,
                    data: [[item]]
                });
            } else {
                newArr[index].data[0].push(item);
            }
        })
        console.log("分组后的数据", newArr);
        let {isAllSelect} = this._handlerDataForClick(newArr);
        this.isAllSelect = isAllSelect;
        return newArr;
    }

    _renderView() {
        console.log(" _renderView  fullScreen " + this.state.fullScreen,this.props.tabType,this.props.modeType);


        return (
            <View style={{flex: 1}}>
                {/*云存储、看家图片、SD卡*/}

                {/*渲染播放列表*/}
                {this._renderListView()}
                {/*时间轴*/}
                {this._renderTimeListView()}

                {/*编辑底部*/}
                {this._renderEditView()}
            </View>
        )
    }


    // _renderPlayerVideoViewComponents() {
    //     return (
    //         <View>
    //
    //             {!this.state.fullScreen ? <RNLine/> : null}
    //
    //             {this.getPlayerVideoView()}
    //
    //             {!this.state.fullScreen ? this._renderVideoToolBar() : null}
    //
    //             {!this.state.fullScreen ? <RNLine/> : null}
    //
    //         </View>
    //     )
    // }


    // getPlayerVideoView() {
    //
    //     /** ! 此处暂时备注 这样写法有风险，猜测：多个地方引用会导致共享变量 !*/
    //     CoverVideoImage.defaultProps = {
    //         videoCover: this.state.videoCover
    //     };
    //
    //     return (
    //         <VideoPlayerContainer
    //             ref={ref => this.videoPlayer = ref}
    //             muted={this.state.muted}
    //             PlayerElement={RNVideoImiCloudEx}
    //             isPlaying={this.state.isPlaying}
    //             dataSource={this.state.dataSource}
    //             cover={[CoveLoading, CoverVideoImage, CoverVideoToolProgress, CoveError]}
    //             videoCover={this.state.videoCover}
    //             onEventChange={this.doHandleOnEventChange.bind(this)}
    //             onCompletion={() => {
    //                 showToast('播放完成');
    //             }}
    //             onUpdateOrientation={(orientation) => {
    //                 this.setState({fullScreen: orientation !== 'PORTRAIT'});
    //                 this.props.onClickFullScreen && this.props.onClickFullScreen(orientation !== 'PORTRAIT');
    //             }
    //          }/>
    //     )
    // }


    onBackAndroid() {
        if (this.state.fullScreen) {
            this._toggleOrientation(!this.state.fullScreen);
            return true;
        }
        return super.onBackAndroid();
    }

    /**
     * 视频功能工具栏
     * @private
     */
    _renderVideoToolBar() {
        console.log("    _renderVideoToolBar() " + JSON.stringify(this.state.isPlaying));

        return (
            <View style={{
                backgroundColor: colors.white,
                flexDirection: 'row',
                paddingVertical: 10,
                justifyContent: 'space-between',
                paddingHorizontal: 15
            }}>

                <CheckBoxButton
                    checked={this.state.isPlaying}
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconPlay, iconPause]}
                    onValueChange={(checkValue) => {
                        console.log("CheckBoxButton 暂停/播放  checkValue  " + +checkValue);
                        if (checkValue && this.mCurItem == null) {
                            this.mCurItem == null && (this.mCurItem = this.state.dataList.length > 0 ? this.state.dataList[0] : null);
                            this.mCurIndex = 0;
                            this._onItemPress(this.mCurItem, this.mCurIndex);
                        } else {
                            this.videoPlayer.togglePlayPause();
                        }
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconVoiceOpen, iconVoiceClose]}
                    onValueChange={(checkValue) => {
                        this.videoPlayer.muted(checkValue)
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconSave]}
                    onValueChange={() => {
                        this._saveVideoToLocal();
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconFull]}
                    onValueChange={(checkValue) => {
                        this._toggleOrientation(checkValue);
                    }}/>

            </View>
        )
    }

    _toggleOrientation(fullScreen) {
        this.videoPlayer.toggleOrientation();

        console.log("_toggleOrientation   " + this.state.fullScreen + " checkValue " + fullScreen);
        this.setState({fullScreen: fullScreen});
        this.props.onClickFullScreen && this.props.onClickFullScreen(fullScreen);
    }


    /**
     * 事件展示列表
     * sectionList不支持宫格显示
     * 1、contentContainerStyle={styles.listViewStyle}，横排可换行显示
     * 上拉加载更多有问题
     * 2、封装数据，每组的宫格数据封装到一起
     * 3、sectionList + FlatList,FlatList展示item
     * 本方法使用方式2
     * @returns {*}
     * @private
     */
    _renderListView() {
        if (this.props.tabType === 2 && this.props.modeType === 1){
            return null;
        }
        // if (this.props.tabType === 2){
        //     return null;
        // }
        if (this.props.tabType === 0 && this.props.modeType === 1){
            return null;
        }

        // let dataList = this.state.dataList;
        let dataList = [];
        // console.log(" _renderListView dataList  -> " + JSON.stringify(dataList));
        //this.state.dataGroupList
        return (
            <XFlatList
                style={{
                    backgroundColor: colors.white
                }}
                // data={dataList}
                keyExtractor={(item, index) => "group_" + index}
                sections={this.state.dataGroupList}
                onRefresh={() => {
                    //编辑模式，不给刷新
                    if (this.state.showGridError){
                        //显示这个错误的时候，需要把这个view隐藏调
                        this.setState({showGridError:false})
                    }
                    this._queryDataList(true,false,false);
                }}
                onLoadMore={() => {
                    console.log(" this._queryDataList(false)");
                    this._queryDataList(false,false,false);
                }
                }
                refreshStatus={{
                    // RefreshingData: {text: I18n.t('commLoadingText')},
                    // NoData: {text: I18n.t('housekeeping_no_event')},
                    // LoadingMoreData: {text: I18n.t('commLoadingMoreDataText')},
                    // LoadFailure: {text: I18n.t('commLoadingFailText')},
                    // LoadMoreFailure: {text: I18n.t('commLoadingClickText')},
                    PreLoading: {text: I18n.t('housekeeping_no_event')},//不传值默认是first loading，在当天视频为空且续费成功回来后必现
                    RefreshingData: {text: I18n.t('commLoadingText')},
                    NoData: {text: I18n.t('housekeeping_no_event')},
                    LoadFailure: {text: I18n.t('commLoadingFailText')},
                    //列表底部提示文案
                    LoadingMoreData: {moreText: I18n.t('commLoadingMoreDataText')},
                    NoMoreData: {moreText: I18n.t('x_flat_list_no_more_data')},
                    LoadMoreFailure: {moreText: I18n.t('commLoadingClickText')},
                    NetException: {moreText: I18n.t('commLoadingFailText')},

                }}
                onEndReachedThreshold={0.1}
                renderEmptyViewFunc={(status, isEmpty) => this._renderEmptyView(status, isEmpty)}

                onLayout={e => {
                    let height = e.nativeEvent.layout.height;
                    if (this.state.flatListHeight < height) {
                        this.setState({flatListHeight: height})
                    }
                }}
                ref={refreshList => this.refreshList = refreshList}
                onScrollToIndexFailed={info => {
                    //byh 未加载出云存视频，切换到SD卡，在切回云存视频，scrollToLocation可能会异常
                    const wait = new Promise(resolve => setTimeout(resolve, 700));
                    wait.then(() => {
                        this.refreshList && this.refreshList.flatList.scrollToLocation({sectionIndex: 0,
                            itemIndex: 0, viewPosition:0,viewOffset:40})
                        // this.refreshList.current?.scrollToIndex({ index: info.index, animated: true/false });
                    });
                }}
                stickySectionHeadersEnabled={false}
                renderSectionHeader={({section, index}) => this._renderHeader(section, index)}
                // contentContainerStyle={styles.listViewStyle}
                renderItem={({item, index,section}) => this._renderItem(item, index,section)}/>
        );
    }

    //列表加载失败的时候，显示点击重试按钮
    _renderDataPullError(){
        if (this.props.modeType != 0){
            return null;
        }
        return(
            <View
                  style={[{
                      flex: 1,
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center"
                  },{height: this.state.flatListHeight}]}
            >
                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal: 15,
                                       height: 40
                                   }}
                                   buttonTextStyle={{textAlign: 'center'}}
                                   onPress={() => {
                                       this.setState({showGridError:false},()=>{
                                           this._queryDataList(true,true);
                                       })
                                   }}/>
            </View>
        )
    }
    /**
     * 时间轴模块
     */
    _renderTimeListView(){

        if (this.props.tabType === -1){
            return null;
        }
        if (this.props.tabType === 1){
            return null;
        }
        //sd视频，并且是网格模式，不显示时间轴
        if (this.props.tabType === 2 && this.props.modeType === 0){
            return null;
        }

        //云存视频，并且是网格模式，不显示时间轴
        if (this.props.tabType === 0 && this.props.modeType === 0){
             return null;
        }

        return (
            <View style={{
                flex:1,
                backgroundColor: "#ffffff",
                width: '100%',
                height: '100%',
                flexDirection:"column",
            }}>
                <View style={{
                    paddingLeft: 5,
                    paddingRight: 5,
                    width: "100%"
                }}>

                    <TimeScaleView2
                        ref={(ref) => {
                            this.timelineView = ref;
                        }}
                        // onCenterValueChanged={this._onCenterValueChanged}
                        onScrolling={this._onScrolling}
                        onScrollEnd={this._onCenterValueChanged}
                        onTimeChangeListener={this._onTimeChangeListener}
                        isDisabled={ false}
                        landscape={false}
                        isCloud={this.props.tabType==0&&this.props.modeType ==1}
                        eventTypeFlags={this.state.eventTypeFlags}
                        canOperate={!this.props.recording}
                    />
                </View>
                {this._renderTimelineEvent()}
            </View>
        );
    }

    _renderTimelineEvent(){

        if (this.props.isFullScreen) {
            return null;
        }

        if (this.state.isEmpty) {
            return (
                <View style={{ flex: 1 }}>
                </View>
            );
        }

        return (
            <ScrollView
                contentContainerStyle={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                    justifyContent: 'flex-start'

                }}
                style={{
                    display: "flex",
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingTop: 15,
                    marginTop: 20
                    // backgroundColor: 'blue'
                }}
            >

                {
                    this.props.eventTimeline.map((item, i) => (
                        this._renderTimelineEventItem(item,i)
                    ))
                }

            </ScrollView>
        )

    }
    _renderTimelineEventItem(item,i){
        let isSelected = this.state.eventTypeFlags & item.type;
        let containerPadding = 15;
        let eventPadding = 10;
        let eventMargin = 12;

        if (kWindowHeight < 700) {
            containerPadding = 10;
            eventPadding = 7;
            eventMargin = 4;
        }
        let containerWidth = (kWindowWidth - 40 - eventMargin * 4) / 3;
        let eventMarginLeft = (kWindowWidth - containerWidth * 3 - eventMargin * 4) / 2;
        let textColorSelected = "#FFFFFF";
        let textColorUnselected = "#333333";
        return(
            <TouchableOpacity
                // style={{width:containerWidth,height:36, padding: eventPadding,marginHorizontal: eventMargin/2, marginBottom: 10, backgroundColor: (isFaceSelected ? EVENT_TYPE_COLOR.faceSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
                key={i}
                style={{ height: 40, padding: eventPadding, marginLeft: eventMarginLeft / 2, marginRight: eventMargin / 2, marginBottom: 15, backgroundColor: (isSelected ? item.color : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 8,borderWidth:(isSelected?0:1),borderColor:'#cccccc', display: "flex", flexDirection: "row", alignItems: "center", justifyContent: 'center' }}
                onPress={() => {
                    this.changeEventType(item.type);
                }}
            >
                <Image
                    style={{ width: 15, height: 15, marginRight: 5 }}
                    source={isSelected ? item.selectIcon : item.icon}
                />


                <Text style={{ color: (isSelected ? textColorSelected : textColorUnselected), fontSize: 12 }}>
                    {item.name}
                </Text>

            </TouchableOpacity>
        )
    }
    changeEventType(eventType){
        let eventTypeFlag = this.state.eventTypeFlags;
        let temp = eventTypeFlag & eventType;
        if (temp !== 0) { // 已经有了这个值  取反
            eventTypeFlag = eventTypeFlag & ~eventType;
        } else {
            eventTypeFlag = eventTypeFlag | eventType;
        }
        //标记，后面不需要再刷新事件筛选
        this.isInitEvent = true;
        this.setState({ eventTypeFlags: eventTypeFlag });
    }

    _onTimeChangeListener = (timestamp)=>{
        //滚动的时间，如果超出天需要更新中间日期tab

    }

    _onScrolling = (timestamp)=>{
        //当前滚动
        this.props.onScrollingListener && this.props.onScrollingListener(timestamp);
    }

    // 这里代表时间轴滚动了
    _onCenterValueChanged = (timestamp) => {

        console.log("滑动结束");
        console.log(`timestamp:${ timestamp }`);
        //直接回调出去，由上层去处理相关的播放逻辑
        this.props.onTimelineVideoTimestampChangeListener && this.props.onTimelineVideoTimestampChangeListener(timestamp);
        // if (this.timeIndicatorView == null) {
        //     return;
        // }
        //
        // this.timeIndicatorView.setState({ centerTimestamp: 0 });

    }




    _renderEmptyView(status, isEmpty) {
        console.log(' _renderEmptyView  isEmpty' + isEmpty + " status " + JSON.stringify(status));
        let icon = isEmpty ? iconEmpty : null;
        if (this.state.showGridError && GlobalUtil.isSdCanPullData){
            return this._renderDataPullError();
        }
        let text = status.text?status.text:status.moreText;
        if (!GlobalUtil.isSdCanPullData){
            text = I18n.t('housekeeping_no_event')
        }
        return (
            <View
                style={[{
                    flex: 1,
                    width: '100%',
                    backgroundColor: colors.white,
                    height: '100%',
                    justifyContent: 'center',
                    alignItems: 'center'
                }, imiThemeManager.Styles.topLine,{height: this.state.flatListHeight}]}>

                <XText style={[{color: '#919BA5', fontSize: 15, alignSelf: 'center',textAlign: "center"}]}
                       numberOfLines={3}
                       text={text}/>

            </View>
        )
    }

    _renderEmptyNewView(status, isEmpty) {
        let icon = isEmpty ? iconEmptyNew : null;
        return (
            <IMIDesignEmptyView rootStyle={{height: this.state.flatListHeight}} defaultIcon={icon}
                                defaultText={status.text}/>
        )
    }

    _assignTopSelectBarRoot = (component) => {
        this.topSelectBarRoot = component;
    };

    /**
     * 获取看护列表数据
     * @private
     */
    _getAlarmListData() {
        this._queryDataList(true);
    }

    _renderHeader = (item, index) => {
        let groupSelected = true;
        let data = item.data[0];
        for (let i = 0; i < data.length; i++) {
            if (!data[i].selected){
                groupSelected = false;
                break;
            }
        }
        return (
            <View style={styles.parentTitle}>
                {this.props.isEdit?
                    <TouchableOpacityImage
                        accessibilityLabel={"time_period_select_box"}
                        style={{marginRight:10}}
                        imageStyle={{width:25,height:25}}
                        source={groupSelected?require('../../resources/images/icon_choose_y.png'):require('../../resources/images/icon_choose_n.png')}
                        onPress={this._handlerGroup.bind(this,item.data,!groupSelected)}
                    />:null}

                <Text style={{textAlignVertical: 'center', height: '100%',flex:1,lineHeight:40}}>
                    {item.title}
                </Text>
            </View>
        )
    }
    /**
     * 绘制item view    **  可以进行子类重写 **
     * @param item
     * @param index item所在section的下标，是每个分组下面的每个item的下标
     * @private
     */
    _renderItem = (item, index,section) => {
        return (
            <View style={styles.listViewStyle}>
                {
                    item.map((itemChild, i) => (
                        this._renderItemChild(itemChild, index,i,section)
                    ))
                }
            </View>
        );
    };

    /**
     *
     * @param item
     * @param parentIndex 这个并不是分组所在的index，此项目中，一直为0
     * @param index
     * @returns {JSX.Element}
     * @private
     */
    _renderItemChild(item, parentIndex,index,section) {
        let {eventType, thumbUrl} = item;
        //console.log('当前imgUrl---',eventPicThumbUrl);
        let isSelected = item.selected;
        //选中标记，图片为pictureId,云存视频用fileName
        let currentTag;
        let timeUTC;
        let source;
        if (this.props.tabType == 2){
            timeUTC = item.startTime;
            currentTag = item.startTime;
            source = require("../../resources/images/pic_playback_thumb.png")
            thumbUrl = ''
        }else {
            let tag = `${item.pictureId}_${moment(item.pictureTimeUTC).valueOf()}`;
            timeUTC = item.isVideo?item.eventTime:item.pictureTimeUTC;
            currentTag = item.isVideo?item.fileName:tag;
            source = {uri: thumbUrl};
        }

        let time = moment(timeUTC).format('YYYY-MM-DD HH:mm:ss');
        let formatStarTime = time.substr(10);
        let topLineWidth = index === 0 ? 0 : 1;
        //云存视频时eventType是个数组
        if ( typeof eventType === 'number'){
            if (this.props.tabType == TabType.SD && eventType == AlarmSDType.COMMON){
                //回看表示没有事件
                eventType = [];
            }else {
                eventType = [eventType];
            }
        }
        //会出现没有eventType的图片，加个容错
        if (!eventType){
            eventType = [];
        }

        return (
            <XView raw={true} key={parentIndex+"_"+index} style={[styles.itemParent]}
                   onPress={this._onItemPress.bind(this, item, parentIndex,index,section,!isSelected)}
                   accessibilityLabel={"housekeeping_item_view_" + time}
            >
                <View style={currentTag === this.state.selectTag && !this.props.isEdit ? styles.itemSelect : null}>
                    {/*<Image style={currentTag === this.state.selectTag && !this.props.isEdit ?styles.itemSelectIcon:styles.itemIcon} source={source} imageCacheEnum={'reload'}/>*/}
                    <IMIImageView2 style={currentTag === this.state.selectTag && !this.props.isEdit ?styles.itemSelectIcon:styles.itemIcon} source={source} showBackground={true} imageUrl={thumbUrl}/>
                    <View style={styles.eventContainer}>
                        {
                            eventType.map((childEventType,index)=>{
                                return(
                                    <XImage raw={true} style={{marginLeft: 4, width: 15, height: 15}} icon={this._getEventIcon(childEventType)} key={index}/>
                                )
                            })
                        }
                    </View>

                    {item.isVideo ?
                        <Text style={styles.videoDuration}>{item.duration}</Text> : null}

                    {this.props.isEdit ?
                        <XImage raw={true} style={{position: "absolute", bottom: 0, right: 0, width: 25, height: 25}}
                                icon={isSelected ? require('../../resources/images/icon_choose_y.png') : require('../../resources/images/icon_choose_n.png')}/> : null}

                </View>

                <XView raw={true} style={{flex: 1, alignItems: 'center', flexDirection: 'row',}}>
                    <XText raw={true} style={styles.itemTitle} text={formatStarTime}/>
                </XView>
            </XView>
        )
    }

    _getEventIcon(eventType){
        let iconDot;
        switch (eventType) {
            case AlarmType.MOVE:
            case AlarmSDType.MOVE:
                iconDot = require('../../resources/images/pic_move.png');
                break;
            case AlarmType.PEOPLE:
            case AlarmSDType.PEOPLE:
                iconDot = require('../../resources/images/pic_person.png');
                break;
            case AlarmType.CRY:
            case AlarmSDType.CRY:
                iconDot = require('../../resources/images/pic_crying.png');
                break;
            case AlarmType.ABNORMAL_SOUND:
            case AlarmSDType.ABNORMAL_SOUND:
                iconDot = require('../../resources/images/pic_sound.png');
                break;
            case AlarmType.LAUGHTER:
            case AlarmSDType.NOBODY:
                //用笑声代替，无人
                iconDot = require('../../resources/images/pic_nobody.png');
                break;
            case AlarmType.CROSSING:
            case AlarmSDType.CROSSING:
                iconDot = require('../../resources/images/pic_fence.png');
                break;
            case AlarmType.KEY_AREA:
            case AlarmSDType.KEY_AREA:
                iconDot = require('../../resources/images/pic_key_area.png');
                break;
            case AlarmType.INTRUSION:
            case AlarmSDType.INTRUSION:
                iconDot = require('../../resources/images/pic_cross_line.png');
                break;
            case AlarmType.VEHICLE:
            case AlarmSDType.VEHICLE:
                iconDot = require('../../resources/images/pic_vehicle.png');
                break;
            case AlarmType.BIKE:
            case AlarmSDType.BIKE:
                iconDot = require('../../resources/images/pic_bike.png');
                break;
            default:
                iconDot = require('../../resources/images/pic_move.png');
                break;
        }
        return iconDot;
    }
    _renderEditView() {

        if (this.props.isEdit) {
            // return(<XView style={{position: "absolute",bottom:14,left:14,right:14,height:64,borderRadius:10,backgroundColor:'#496EE0', alignItems: 'center',justifyContent: 'center',flexDirection: 'row'}}>
            return (<XView
                style={{bottom: 0, height: 64, width: '100%', backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
                <View style={{height: 1, backgroundColor: "#F1F1F1"}}/>
                <XView style={{
                    height: 62,
                    width: '100%',
                    backgroundColor: '#FFFFFF',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'row'
                }}>
                    {/*<XText style={{fontSize:12,color:'#333333'}} iconSize={30} icon={require('../../resources/images/icon_alarm_down.png')} text={stringsTo('downLoadTitle')} onPress={()=>{*/}
                    {/*    if (this.props.dataArray.length>0){*/}

                    {/*        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {*/}
                    {/*            if (status === 0) {*/}
                    {/*                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {*/}
                    {/*                    if (status2 === 0) {*/}

                    {/*                        let dataAry = [];*/}
                    {/*                        for (let m = 0; m < this.props.dataArray.length;m ++){*/}
                    {/*                            let data = this.props.dataArray[m];*/}
                    {/*                            let item = this.state.dataList[data];*/}
                    {/*                            dataAry.push(item);*/}
                    {/*                        }*/}
                    {/*                        showLoading(stringsTo('alarm_download_downloading'),true);*/}
                    {/*                        this.props.onClickCancel&&this.props.onClickCancel(false);*/}
                    {/*                        this.downloadImageArray(0,dataAry);*/}
                    {/*                    } else if (status2 === -1) {*/}
                    {/*                        showToast(stringsTo('storage_permission_denied'));*/}
                    {/*                    }*/}
                    {/*                });*/}
                    {/*            } else if (status === -1) {*/}
                    {/*                showToast(stringsTo('storage_permission_denied'))*/}
                    {/*            }*/}
                    {/*        })*/}

                    {/*    }else {*/}
                    {/*        showToast(stringsTo('select_tip'));*/}
                    {/*    }*/}

                    {/*}}*/}
                    {/* accessibilityLabel={"housekeeping_download"}*/}
                    {/*/>*/}

                    <XText style={{fontSize: 12, color: '#333333'}} iconSize={30}
                           icon={require('../../resources/images/icon_delete_black.png')}
                           text={stringsTo('delete_title')} onPress={() => {
                               let {total,selectCount} = this._handlerDataForClick();
                               //视频的选中数据条数要限制在100条内
                               if (selectCount > 0) {
                                   this.setState({showDeleteTip: true});
                               } else {
                                   showToast(stringsTo('select_tip'));
                               }
                           }}
                           accessibilityLabel={"housekeeping_delete"}
                    />
                </XView>
            </XView>)
        }
        return null;
    }

    /**
     * 删除选中的数据
     * 图片或者看家视频
     * 每次删除的图片条数没有做限制
     * 视频每次上限为100条
     */
    deleteDataArray(){
        if (this.props.tabType ===0){
            //云存储视频
            this.deleteVideoArray();
        }else if (this.props.tabType === 1){
            //看家图片
            this.deleteImageArray();
        }
    }

    /**
     *
     * 删除云存视频
     * 删除有条数上限，上限为100条
     */
    deleteVideoArray(){
        let dataAry = [];
        let listGroup = this.state.dataGroupList;
        listGroup.forEach(p=>{
            let data = p.data[0];
            data.forEach(item=>{
                if (item.selected){
                    dataAry.push(item.fileName);
                }
            })
        })

        //每次云存视频删除，单次只能最多删除100条，如果大于100条，我们需要继续删除
        let deleteArr = CommonUtils.groupArr(dataAry,100);
        Promise.all(deleteArr.map((item) => {
            return this.deleteVideos(item);
        })).then((res) => {
            showLoading(stringsTo('delete_title_loading'), false);
            showToast(stringsTo('delete_success'));
            this.props.onClickCancel && this.props.onClickCancel(false);
            this._getAlarmListData();
            this.props.onCloudVideoStatusListener && this.props.onCloudVideoStatusListener({type:CLOUD_VIDEO_STATUS.DELETE,value:true});
        }).catch(err => {
            showLoading(stringsTo('delete_title_loading'), false);
            showToast(stringsTo('delete_failed'));
        });
    }

    getFirstVideo(){
        let videoList = this.state.eventVideoList;
        if (videoList && videoList .length>0){
            return videoList[0];
        }
        return null;
    }



    deleteVideos(dataAry){
        return new Promise((resolve, reject)=>{
            aliAlarmEventCloudApi.deleteAliVideoCloud(LetDevice.deviceID,dataAry).then(data=>{
                resolve(data);
            }).catch(error=>{
                reject(error);
            })
        })
    }

    deleteImageArray(page) {
        let dataAry = [];
        let listGroup = this.state.dataGroupList;
        listGroup.forEach(p=>{
            let data = p.data[0];
            data.forEach(item=>{
                if (item.selected){
                    dataAry.push(item.pictureId);
                }
            })
        })
        aliAlarmEventCloudApi.deleteAliPictureCloudImage(LetDevice.deviceID, dataAry).then(data => {

           showLoading(stringsTo('delete_title_loading'), false);
           showToast(stringsTo('delete_success'));
           this.props.onClickCancel && this.props.onClickCancel(false);
           this._getAlarmListData();
           console.log('deleteAliCloudImage-----', data);
        }).catch(error => {
            showLoading(stringsTo('delete_title_loading'), false);
            showToast(stringsTo('delete_failed'));
            this.props.onClickCancel && this.props.onClickCancel(false);
            this._getAlarmListData();
            console.log('deleteAliCloudImage--error---', error);
        });
    }

    downloadImageArray(page, list) {

        this.downloadFile(page, list).then((start, data) => {
            if (start == list.length - 1) {
                showLoading(stringsTo('alarm_download_downloading'), false);
                showToast(stringsTo('save_success'));
            } else {
                console.log('start--', start);
                this.downloadImageArray(start + 1, list);
            }
        }).catch((start, error) => {
            if (start == list.length - 1) {
                console.log('失败了走这里');
                showLoading(stringsTo('alarm_download_downloading'), false);
                showToast(stringsTo('save_success'));
            } else {
                console.log('失败了走这里下载其他的');
                this.downloadImageArray(start + 1, list);
            }
        });
    }

    downloadFile(downStart, dataList) {
        console.log(TAG + "start downloadFile  ===================  ");
        return new Promise((resolve, reject) => {
            let item = dataList[downStart];
            let {pictureTime, pictureUrl} = item;
            let fileName = `${pictureTime}.jpg`;

            IMIDownload.downloadToPath(EVENT_NAME, pictureUrl, LetDevice.deviceID, fileName);

            this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
                if (event.status === IMIDownload.STATUS_START) {
                }
                if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                }

                if (event.status === IMIDownload.STATUS_ERROR) {
                    reject(downStart, event.status);//IOS在错误时会一直显示加载

                    // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }
                if (event.status === IMIDownload.STATUS_CANCEL) {
                    // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }

                if (event.status === IMIDownload.STATUS_SUCCESS) {
                    const path = `${event.downloadPath}/${fileName}`;

                    IMIFile.saveImageToPhotosAlbum(path, LetDevice.deviceID).then((data) => {
                        resolve(downStart, data);
                    }).catch(error => {

                        reject(downStart, error);
                    });
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }
            });
        }).then((start, data) => {
            console.log(TAG + "start downloadFile ++++++++++++++++ ", data);
            return start;
        });
    }

    /**
     *
     * @param group 分组下的数据
     * @param isSelected 分组要改变的选中状态
     * @private
     */
    _handlerGroup(group,isSelected){
        let data = group[0];
        for (let i = 0; i < data.length; i++) {
            data[i].selected = isSelected;
        }
        let tempList = [...this.state.dataGroupList];
        this.setState({dataGroupList:tempList});
        let {total,isAllSelect} = this._handlerDataForClick(tempList);
        this.isAllSelect = isAllSelect;

    }

    _handlerDataForClick(dataList = this.state.dataGroupList){
        let counter = 0;
        let selectCounter = 0;
        let isAllSelect = true;
        this.selectArr = [];
        for (let i = 0; i < dataList.length; i++) {
            let data = dataList[i].data[0];
            for (let j = 0; j < data.length; j++) {
                if (data[j].selected){
                    selectCounter ++;
                    this.selectArr.push(data[j]);
                }else {
                    isAllSelect = false;
                }
                counter ++;
            }
        }
        return {total:counter,selectCount:selectCounter,isAllSelect:isAllSelect};
    }
    /**
     * item点击事件，网格布局：图片、视频点击
     * 点击后
     * 图片：展示大图
     * 云存视频：播放视频
     * 点击选中有选中样式
     * @param item
     * @param groupIndex 分组下标
     * @param index 分组中item下标
     */
    _onItemPress(item, groupIndex,index,section,select) {
        if (isEmpty(item)) {
            return
        }
        if (!this.props.isEdit) {

            let {pictureTime, pictureTimeUTC, isVideo} = item;
            if (this.props.tabType == 2){
                //回看SD卡视频，网格点击
                if (this.props.recording){
                    showToast(stringsTo('screen_recording'));
                }else {
                    IMILogUtil.uploadClickEventForCount("SDVideoBrowserNumber");
                    this.setState({selectTag:item.startTime})
                    this.props.pictureChangeListener && this.props.pictureChangeListener(item);
                }
            }else {
                //分看家图片、云存视频
                if (isVideo){
                    IMILogUtil.uploadClickEventForCount("CloudVideoBrowserNumber");
                    //更换视频播放
                    this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:videoStateType.VIDEO_CHANGE});
                    // this.videoItem = item;
                    let isFirstVideo = false;
                    if (groupIndex ==0 && index ==0){
                        isFirstVideo = true;
                    }
                    this.setState({
                        noVideo:false,
                        todayIsNoData:false,
                        isPlayFinish:false,
                        isPlay:false,
                        fileName:item.fileName,
                        selectTag:item.fileName
                    },()=>{

                        if (isIos()){
                            this.setState({isLoading:true,showPauseView:false});
                        }
                        if (isAndroid()){
                            this.delayToPlay && clearTimeout(this.delayToPlay);
                            this.delayToPlay = setTimeout(res=>{
                                this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:item.fileName,eventTime:item.eventTime,isFirstVideo:isFirstVideo});
                            },800);
                        }else {
                            this.props.videoStateChangeListener && this.props.videoStateChangeListener({type:VideoStateType.GET_FILENAME,fileName:item.fileName,eventTime:item.eventTime,isFirstVideo:isFirstVideo});
                        }
                    })
                    // this.getByEventName(item.fileName);
                }else {
                    let tag = `${item.pictureId}_${moment(item.pictureTimeUTC).valueOf()}`;
                    this.setState({selectTag:tag})
                    this.props.pictureChangeListener && this.props.pictureChangeListener(item);
                }
            }


        } else {
            // if (this.props.onClickSelect) {
            //     this.props.onClickSelect(index);
            // }
            //编辑时，item的点击事件

            // let groupList = this.state.dataGroupList;
            let group = section.data;
            // console.log("groupList",groupList,"group",group,groupIndex,index,select);
            group[0][index].selected = select;
            let groupList = [...this.state.dataGroupList]
            this.setState({dataGroupList:groupList});
            let {isAllSelect} = this._handlerDataForClick(groupList);
            //不向外部通知，自己管理这个状态，父组件全选/全不选的图标是一样的，不需要改变
            //后期如果需要更改，那边需要把这个状态通知出去，暂时向外暴露了通知方法
            this.props.onAllSelectChangeListener && this.props.onAllSelectChangeListener(isAllSelect);
            this.isAllSelect = isAllSelect;
        }

    }

    onSelectAllChanged() {
        let list = this.doItemsSelectedChange(!this.isAllSelect);
        let {isAllSelect} = this._handlerDataForClick(list);
        //不向外部通知，自己管理这个状态，父组件全选/全不选的图标是一样的，不需要改变
        //后期如果需要更改，那边需要把这个状态通知出去，暂时向外暴露了通知方法
        this.props.onAllSelectChangeListener && this.props.onAllSelectChangeListener(isAllSelect);
        this.isAllSelect = isAllSelect;
    }

    /**
     * 遍历改变所有的item
     * 修改选中状态
     */
    doItemsSelectedChange(selectValue){
        let tempList = [...this.state.dataGroupList];
        tempList.forEach(((value, index) => {
            value.data[0].forEach((item) =>{
                item.selected = selectValue;
                return item;
            });
            return value;
        }));
        this.setState({dataGroupList:tempList});
        return tempList;
    }

    /**
     *
     * @param value true 进入编辑模式 false退出编辑模式
     */
    enterOrExitEditMode(value,selectFileName){
        this.isAllSelect = false;
        this.selectArr = [];
        if (selectFileName){
            this.setState({selectTag:selectFileName})
        }
        if (!value){
            //退出编辑模式，重置item选中状态
            this.doItemsSelectedChange(false);
        }
    }

    /**
     * 保存当前播放视频到本地相册中
     * @private
     */
    _saveVideoToLocal() {
        this.waitingSaveVideo = true;

        showLoading(stringsTo('commWaitText'), true);

        console.log('_saveVideoToLocal', this.mCurItemLocal);
        if (this.mCurItemLocal != null) {
            console.log('_saveVideoToLocal22222222222222222222', this.mCurItemLocal);
            // IMIVideoUtils.downloadToPath(pathList)
        }
    }

    /**
     * 处理当前播放器事件
     */
    doHandleOnEventChange(data) {
        console.log(TAG + 'doHandleOnEventChange ', data);

        if (!data.hasOwnProperty('extra')) {
            return
        }

        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFER_FINISH) {
            this.mCurItemLocal = extra;

            //判断 此处下载完成后 用户是否需要保存视频
            if (this.waitingSaveVideo) {
                this._saveVideoToLocal();
            }
        }
    }

    /***           系統函數   START    ****/

    componentWillUnmount() {
        super.componentWillUnmount();
        showLoading(stringsTo('commWaitText'), false);
        this.delayTimeout && clearTimeout(this.delayTimeout);
        this.delayToPlay && clearTimeout(this.delayToPlay);
        this.loadingTimeout && clearTimeout(this.loadingTimeout);
        this._subscribe_focus_alarm && this._subscribe_focus_alarm();
        this._subscribe_blur_alarm && this._subscribe_blur_alarm();
    }

    componentDidMount() {
        console.log("imivideo:",this.props.IMIVideoView);
        //根据类型去请求数据，mount的时候不请求数据了
        //this._getAlarmListData();
        this._subscribe_focus_alarm = this.props.navigation.addListener('focus', () => {
            this.isForegroundPage = true;
        });

        this._subscribe_blur_alarm = this.props.navigation.addListener('blur', () => {
            this.isForegroundPage = false;
        });

    }

    /***           系統函數   END    ****/


}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        //position: 'absolute',
    },
    listViewStyle: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        // justifyContent: "space-around",
    },
    parentTitle: {
        width: getScreenWidth(),
        height: 40,
        alignItems: 'center',
        paddingLeft: 14,
        fontSize: 15,
        flexDirection:'row',
        color: '#7F7F7F'
    },
    itemParent: {
        // alignItems: 'center',
        paddingLeft: 14,
        paddingBottom: 10,
        // flexDirection: 'row',
        justifyContent:'center',
        backgroundColor: colors.white,
    },
    itemSelect: {
        borderWidth: 2,
        borderColor: "#4A70A5",
        borderRadius:6,
        // width: (windowWidth - 4 * 14) / 3+4,
        // height: (windowWidth - 4 * 14) / 3 * 57 / 101.33+4,
    },

    eventContainer: {
        position: "absolute",
        top: 4,
        right: 4,
        flexDirection: "row",
        justifyContent: 'flex-end'
    },
    itemSelectIcon: {
        borderRadius: 4,
        width: (getScreenWidth() - 4 * 14) / 3-4,
        height: (getScreenWidth() - 4 * 14) / 3 * 57 / 101.33-4,
    },
    itemIcon: {
        borderRadius: 4,
        width: (getScreenWidth() - 4 * 14) / 3,
        height: (getScreenWidth() - 4 * 14) / 3 * 57 / 101.33,
    },

    itemTitle: {
        fontSize: 12,

        // fontWeight: 'bold',
        color: colors.black,
        // paddingLeft: 14
    },
    videoDuration: {
        position: "absolute",
        bottom: 0,
        right: 0,
        width: "100%",
        height: 16,
        backgroundColor: "#00000055",
        color: "#ffffff",
        lineHeight: 16,
        textAlign: "right",
        paddingRight: 4,
        fontSize: 12,
        borderBottomRightRadius:4,
        borderBottomLeftRadius:4

    },

    itemDesc: {
        fontSize: 12,
        paddingLeft: 14,
        // marginTop: 10,
        color: colors.black,
    },
    videoToolBtn: {
        marginHorizontal: 15,
        width: 30,
        height: 30,
    },
});

