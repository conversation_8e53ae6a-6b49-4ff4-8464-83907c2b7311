import React from 'react';

import {
    StyleSheet,
    View,
    Image,
    Text,
    Platform,
    ScrollView,
    Modal,
    TouchableWithoutFeedback,
    Dimensions,
    <PERSON>er,
    FlatList,
    BackHandler
} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui'

import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import ChoiceItem from "../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem";
import ListItemWithPopMenu from "../../../../imilab-design-ui/src/widgets/settingUI/ListItemWithPopMenu";
import RoundedButtonView from "../../../../imilab-design-ui/src/widgets/settingUI/RoundedButtonView"
import Separator from "../../../../imilab-design-ui/src/widgets/settingUI/Separator"
import SlideGear from "../../../../imilab-design-ui/src/widgets/settingUI/SlideGear"
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog"
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText, XView} from "react-native-easy-app";
import Toast from "react-native-root-toast";
import {LetDevice, letDevice} from "../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {CameraMethod, LetIMIIotRequest} from "../../../../imilab-rn-sdk";
import {showToast} from "../../../../imilab-design-ui";
import {showLoading} from "../../../../imilab-design-ui";
import {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import ImageButton from "../../../../imi-rn-commonView/ImageButton/ImageButton";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import IMIHost from "../../../../imilab-rn-sdk/native/local-kit/IMIHost";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import getVoiceArr from "../utils/VoiceAlarmConstants";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";


let tempSpotlightSetting = {};

/**
 * 声光报警
 */
export default class AudioAlarmSetPageV2 extends BaseDeviceComponent {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = {
            lightSwitch: false,//声光报警
            onlyPeopleSwitch: true,//只侦测人形
            effectiveTimeStr: stringsTo('allDay_time'),
            startTime: '',//开始时间
            endTime: '',//结束时间
            effectiveTimeData: '',//生效时间数据
            voiceName: stringsTo('voice_for_warning'),//提示音
            spotlightMode: 1,//报警模式  0常亮 1闪烁 2不亮
            tempSpotlightMode: 1,//报警模式  0常亮 1闪烁 2不亮
            spotlightModeStr: stringsTo('potlight_flash'),
            containTime: 10,//持续时间
            tempContainTime: 10,//弹框中间变量
            showSpotlightModal: false,//聚光灯报警弹窗
            data: [
                {tit: stringsTo("potlight_not_bright"), value: 2},
                {tit: stringsTo("potlight_flash"), value: 1},
                {tit: stringsTo("potlight_long_bright"), value: 0}],
            allSpotlightData: '',//声光报警数据
            allVoiceArr: [],//所有音频数据 包括标准+自定义音频
            type: '',//标记，设置类型
        }
        this.location = 1;
        this.voiceArr = [];
    }

    UNSAFE_componentWillMount() {
        this.voiceArr = getVoiceArr();
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }

    componentWillUnmount() {
        this.timer && clearTimeout(this.timer);
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
        }
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };

    // 返回上一页
    _onPressBack = () => {
        if (this.props.route.params.callback) {
            //返回值，需要的参数，语音的名称
            let backParams = {
                totalSwitch: this.state.lightSwitch,
                voiceUrl: this.state.voiceUrl,
                containTime: this.state.containTime,//持续时间
                spotlightMode: this.state.spotlightMode,//报警模式  0常亮 1闪烁 2不亮
                sound_alarm: this.state.sound_alarm,
                light_alarm: this.state.light_alarm,
            }
            this.props.route.params.callback(backParams);
        }
        this.props.navigation.pop();
    };


    componentDidMount() {
        //获取页面传过来的值
        this.getRouteValue();
        this.getAllValue();
    }

    getRouteValue() {
        let params = this.props.route.params;
        let mode = (params.lightMode || params.lightMode == 0)?params.lightMode:1;
        let name = this.getModeName(mode);
        this.setState({
            location: params.location,
            voiceSwitch: params.voiceSwitch,
            voiceUrl: params.voiceUrl,
            spotlightMode: (params.lightMode || params.lightMode == 0)?params.lightMode:1,
            tempSpotlightMode: (params.lightMode || params.lightMode == 0)?params.lightMode:1,
            spotlightModeStr: name,
            containTime: params.containTime?params.containTime:10,
            tempContainTime: params.containTime?params.containTime:10,
            lightSwitch: params.soundLightSwitchValue,
            sound_alarm: params.sound_alarm,
            light_alarm: params.light_alarm,
            //进来的类型
            type: params.type,
            //这里的数据设置物模型的时候用

            repeat_time: params.repeat_time,
            repeat_week: params.repeat_week,
            detectSwitch: params.detectSwitch,
            push_flag: params.push_flag,
            active_time_switch: params.active_time_switch,
        })

    }

    getModeName(mode) {
        let modeArr = this.state.data;
        let name;
        for (let i = 0; i < modeArr.length; i++) {
            if (mode == modeArr[i].value) {
                name = modeArr[i].tit;
                break;
            }
        }
        return name;
    }

    /**
     * 获取自定义音频数据，
     * 并合并预置音频
     */
    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data) => {
            showLoading(false);
            let dataObject = JSON.parse(data);
            let stateProps = {};
            let tempAudioPath = "";
            console.log('获取所有数据--', dataObject);
            // 自定义音频
            if (dataObject.SoundLightCustomAudioList &&
                dataObject.SoundLightCustomAudioList.value.length > 0) {
                // 一键警告自定义音频数组
                console.log('自定义音频数组');
                let tempTotalArr = [];
                tempTotalArr = tempTotalArr.concat(this.voiceArr);
                //自定义语音
                let customArr = dataObject.SoundLightCustomAudioList.value
                let tempArr = [];
                console.log('当前自定义音频数组', customArr, customArr.length);
                for (let i = 0; i < customArr.length; i++) {
                    let tempStr = JSON.parse(customArr[i]);
                    console.log('tempStr---', tempStr, tempStr.url, tempStr.name);
                    let tempParams = {
                        name: tempStr.name,
                        url: tempStr.url,
                        index: tempStr.index,
                        time: tempStr.time,
                    }
                    tempArr.push(tempParams);
                }
                console.log('转换后的自定义音频数组', tempArr);
                if (tempArr.length > 1) {
                    tempArr.sort((a, b) => {
                        return a.index - b.index;
                    });// 排序一下
                }
                tempTotalArr = tempTotalArr.concat(tempArr);
                stateProps.allVoiceArr = tempTotalArr;
                console.log('声光报警所有自定义音频数据', stateProps.allVoiceArr);
                for (let voiceItem of tempTotalArr) {
                    let newAudioUrl = voiceItem.url;

                    if (this.state.voiceUrl == newAudioUrl) {
                        stateProps.voiceName = voiceItem.name;
                        stateProps.voiceSelect = voiceItem.index;
                        console.log('音频名称', voiceItem.name, stateProps.voiceName);
                        break;
                    }
                }

            } else {
                // 未设置自定义音频
                // //中国
                for (let voiceItem of this.voiceArr) {
                    let newAudioUrl = voiceItem.url;//提示音路径

                    if (this.state.voiceUrl == newAudioUrl) {
                        stateProps.voiceName = voiceItem.name;
                        stateProps.voiceSelect = voiceItem.index;
                        break;
                    }
                }
                stateProps.allVoiceArr = this.voiceArr;
            }
            // 统一设置从设备端获取的值
            this.setState(stateProps);
        }).catch(error => {
            console.log(JSON.stringify(error))
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
        });
    }


    render() {
        global.navigation = this.props.navigation;
        return (<View style={styles.container}>
            <NavigationBar
                title={stringsTo('soundLightAlarm')}
                left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
                right={[]}
            />
            {/*声光报警*/}
            <ListItmeWithSwitch
                accessibilityLabel={["sound_light_alarm_off","sound_light_alarm_on"]}
                title={stringsTo('soundLightAlarm')} value={this.state.lightSwitch}
                onValueChange={(value) => {
                    this.updateSoundLightValue(value)
                }}/>

            {this.state.lightSwitch ?
                <View style={{flex: 1}}>


                    <ListItem title={stringsTo("tip_voice_selected")}
                              value={this.state.voiceName}
                              onPress={() => {
                                  this.props.navigation.navigate('IntelligentVoiceSelect', {
                                      type: "areaVoice",
                                      location: this.location,
                                      voiceUrl: this.state.voiceUrl,
                                      callback: ((voiceUrl, voiceName) => {
                                          console.log('返回获取提示音--', voiceUrl, voiceName);
                                          this.setState({voiceName: voiceName, voiceUrl: voiceUrl}, callback => {
                                              console.log('更新提示音选择', this.state.voiceName, this.state.voiceUrl);
                                              this.updateSoundLightValue();
                                          });
                                      })
                                  })
                              }}/>

                    <ListItem title={stringsTo('potlight_alarm_mode')} value={this.state.spotlightModeStr}
                              onPress={() => {
                                  this.setState({showSpotlightModal: true});
                              }}/>
                </View> : null}
            {/*聚光灯弹窗*/}
            {this._spotlightModal()}

        </View>);
    }

    // 聚光灯报警弹窗
    _spotlightModal() {
        return (<View>
            <MessageDialog
                title={I18n.t('potlight_alarm_mode')}
                visible={this.state.showSpotlightModal}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({
                                showSpotlightModal: false,
                                tempSpotlightMode: this.state.spotlightMode,
                                tempContainTime: this.state.containTime
                            });
                            // this.setState(tempSpotlightSetting);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            //保存自己修改的值
                            let name = this.getModeName(this.state.tempSpotlightMode);
                            this.setState({
                                showSpotlightModal: false,
                                spotlightMode: this.state.tempSpotlightMode,
                                containTime: this.state.tempContainTime,
                                spotlightModeStr: name
                            }, () => {
                                this.updateSoundLightValue();
                                //声光报警模式打点
                                IMILogUtil.uploadClickEventValue({SpotlightMode: this.state.spotlightMode});
                                //声光报警时长
                                IMILogUtil.uploadClickEventValue({SpotlightTime: this.state.containTime});
                            });

                            // this.saveChangeValue();
                            // this.updateSoundLightValue(this.state.lightSwitch, this.state.onlyPeopleSwitch, 'spotlightMode');
                        }
                    },
                ]}
            >
                <FlatList
                    data={this.state.data}
                    renderItem={this._renderItem}
                    keyExtractor={(item, index) => `key_${index}`}
                    extraData={this.state}
                    onEndReachedThreshold={0.1}
                    // ItemSeparatorComponent={() => <View style={{alignItems: 'center', width: '100%'}}><View
                    //     style={{height: 0.5, width: '100%', backgroundColor: 'white'}}/></View>}
                />
                {this.state.tempSpotlightMode == 2 ? null :
                    <View style={{flex: 1}}>
                        <View style={{width: '100%', height: 60, backgroundColor: 'white'}}>
                            <Text style={{
                                flexGrow: 1,
                                fontSize: 15,
                                marginLeft: 20,
                                color: "#333333",
                                textAlign: 'left',
                                lineHeight: 56,
                            }}>{stringsTo('potlight_contain_time') + '(' + this.state.tempContainTime + 'S' + ')'}</Text>
                        </View>
                        <SlideGear
                            options={Array.from({length: 26}, (v, i) => i + 1)}
                            indicatorTextArray={[5, 10, 15, 20, 25, 30]}
                            value={this.state.tempContainTime - 5}
                            onSlidingComplete={(value)=>{
                                console.log("slide is complete",value)
                            }}
                            onValueChange={(index) => {
                                this.setState({
                                    tempContainTime: index + 5
                                });

                            }}
                        />
                    </View>}
            </MessageDialog>
        </View>)
    }

    _renderItem = ({item, index}) => {
        return (
            <TouchableWithoutFeedback
                onPress={this._itemPress.bind(this, item)}
            >
                <View style={{
                    flexDirection: 'column',
                    height: 60,
                    display: "flex",
                    alignItems: "center",
                    backgroundColor: "white"
                }}>
                    <View
                        style={{flexDirection: 'row', width: '100%', height: "100%", alignItems: "center"}
                        }>
                        <Text style={{
                            flexGrow: 1,
                            fontSize: 15,
                            marginLeft: 20,
                            color: this.state.tempSpotlightMode == item.value ? "#496EE0" : "#333333",
                            textAlign: 'left'
                        }}>{item.tit}</Text>
                        <Image style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain',
                            display: this.state.tempSpotlightMode == item.value ? "flex" : 'none'}} source={require('../../resources/images/icon_select_s.png')}/>

                    </View>
                </View>
            </TouchableWithoutFeedback>
        );
    }

    _itemPress = (item) => {
        console.log("item=", JSON.stringify(item));
        this.setState({tempSpotlightMode: item.value});
    }


    //设置声光报警
    updateSoundLightValue(lightValue = true) {
        if (this.state.type == 'moveDetection'
            || this.state.type == 'peopleDetection'
            || this.state.type == 'soundDetection'
            || this.state.type == 'motorVehicleDetection'
            || this.state.type == 'nonVehicleDetection') {
            //移动、人形、异响,设置声光报警
            this.setAlarm(lightValue);
        } else {
            this.setState({lightSwitch: lightValue});
        }

        IMILogUtil.uploadClickEventValue({SpotlightSwitch: lightValue?"1":"0"});

    }

    setAlarm(lightValue) {
        let soundStr = {
            switch: lightValue ? 1 : 0,
            name: this.state.voiceUrl
        };
        let sound_alarm = JSON.stringify(soundStr);
        //闪光灯
        let param = [this.state.spotlightMode, this.state.containTime, 50, 500, 500];
        let lightStr = {
            switch: lightValue ? 1 : 0,
            param: JSON.stringify(param),
        }
        let light_alarm = JSON.stringify(lightStr);
        let value = {
            switch: this.state.detectSwitch ? 1 : 0,
            push_flag: this.state.push_flag ? 1 : 0,
            active_time_switch: this.state.active_time_switch ? 1 : 0,
            repeat_time: this.state.repeat_time,
            repeat_week: this.state.repeat_week,
            sound_alarm: sound_alarm,
            light_alarm: light_alarm
        };
        let params;


        if (this.state.type == 'moveDetection') {
            params = {MoveDetectionAttr: value}
        } else if (this.state.type == 'peopleDetection') {
            params = {PeopleDetectionAttr: value}
        }else if(this.state.type == 'motorVehicleDetection'){
            params = {MotorVehicleDetectionAttr: value}
        }else if(this.state.type == 'nonVehicleDetection'){
            params = {NonMotorVehicleDetectionAttr: value}
        }else{
            params = {LoudDetectionAttr: value}
        }
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            showLoading(false);
            showToast(I18n.t('settings_set_success'));
            this.setState({
                lightSwitch: lightValue,
                sound_alarm: sound_alarm,
                light_alarm: light_alarm,
            });
        }).catch((error) => {
            showLoading(false);
            showToast(I18n.t('waitFailedTip'));
        });
    }

}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: imiThemeManager.theme.pageBg,
    }
});
