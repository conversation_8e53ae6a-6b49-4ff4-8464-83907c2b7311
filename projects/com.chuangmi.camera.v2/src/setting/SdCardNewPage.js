import React, {PureComponent} from 'react';
import {StyleSheet, View, Image, Text, Dimensions, ActivityIndicator, ScrollView, ImageBackground} from 'react-native';
import {imiThemeManager, ListItem, RoundedButtonView, Separator} from '../../../../imilab-design-ui'
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar"
import {LetDevice} from "../../../../imilab-rn-sdk";
import {showToast, MessageDialog, showLoading, ChoiceItem, SingleChoiceItem} from "../../../../imilab-design-ui";
import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";

import I18n, {stringsTo} from "../../../../globalization/Localize";

import {TouchableOpacity} from "react-native-gesture-handler";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import NeedFormatType from "../utils/NeedFormatType";
import GlobalUtil from "../utils/GlobalUtil";

const ScreenHeight = Dimensions.get('window').height;
export default class SdCardNewPage extends PureComponent {

    constructor(props, context) {
        super(props, context);
        this.state = {
            storageTotalCapacity:0, //SD卡总容量
            storageRemainCapacity:0, //SD卡剩余容量
            storageUsedCapacity:0, //SD卡已用容量
            usedVolumeStickWidth:0, //表示SD卡已用容量的View长度  //没什么用
            sdCardStatus:-1, //SD卡状态，-1表示数据加载中
            dialogVisible:false, //格式化SD卡对话框的可见性
            injectDialogVisible:false, //弹出SD卡确认对话框的可见性
            loopQueryType:0, //0:初始化 1:退出SD卡 2:格式化SD卡
            showCalibrationModal:false,//录制模式弹窗
            storageRecordMode:2,//录制模式
            currenStorageRecordMode:2,// 录制模式
            switchStorage:true,//默认打开存储
        }
        this.readyToDealSDData = true;
    }

    componentDidMount() {
        this.getRecordMode()
        // this.getSdStatusAndStorageInfo(this.state.loopQueryType);
        // this.intervalQuery = setInterval(() => this.getSdStatusAndStorageInfo(this.state.loopQueryType), 2500);
        this.getSdStatusAndStorageInfoNew(this.state.loopQueryType);
        this.intervalQuery = setInterval(() => this.getSdStatusAndStorageInfoNew(this.state.loopQueryType), 2500);
        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            showLoading(stringsTo('storageCardFormating'), false);
            this.intervalQuery && clearInterval(this.intervalQuery);
        });
        if (this.props.route.params && this.props.route.params.toFormateSDCard){
            //需要进来就开始格式化
            this._formatSdCard();
        }

    }

    componentWillUnmount() {
        this.intervalQuery && clearInterval(this.intervalQuery);
        this.waitDealDataTimeout && clearTimeout(this.waitDealDataTimeout);
    }
    //获取摄像机录制模式
    getRecordMode() {
        LetDevice.getPropertyCloud('StorageRecordMode').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            this.setState({
                storageRecordMode: value, currenStorageRecordMode: value,
                switchStorage: value == 0 ? false : true,
            });
        })

    }

    /**
     * 轮询SD卡的相关状态
     * @param type 0正常 1推出SD卡  2格式化SD卡
     */
    getSdStatusAndStorageInfoNew(type = 0) {

        LetDevice.updateAllPropertyCloud().then((data) => {
            if (!this.readyToDealSDData){
                //解决点击格式化后，会立即提示格式化成功，再显示格式化中问题
                return;
            }
            let dataPackage = JSON.parse(data);

            let needFormatValue = NeedFormatType.NORMAL;
            if (dataPackage.NeedFormatStorageMedia){
                needFormatValue = dataPackage.NeedFormatStorageMedia.value;
            }
            let value = -1;
            if (dataPackage.StorageStatus){
                value = parseInt(dataPackage.StorageStatus.value);
            }

            if (needFormatValue == NeedFormatType.NORMAL
                || needFormatValue == NeedFormatType.HAS_OLD_INDEX){
                //1、新索引正常 2、老索引-如果SD卡状态正常的情况下也需要获取SD卡容量等相关信息
                if (value == 0) { //未插卡
                    showLoading(stringsTo('storageCardFormating'), false);
                    if (type == 1) {
                        showToast(I18n.t("injectSuccess"));
                        this.setState({loopQueryType: 0});
                    } else {
                        this.setState({sdCardStatus: value});
                    }
                } else if (value == 1 || value == 2) { //正常使用中 和 未格式化(格式化失败为此状态)
                    showLoading(stringsTo('storageCardFormating'), false);
                    let dataPackage = JSON.parse(data);
                    let stateProps = {};

                    if (dataPackage.StorageTotalCapacity) {
                        let totalCapacity = dataPackage.StorageTotalCapacity.value;
                        stateProps.storageTotalCapacity = (totalCapacity / 1000).toFixed(2);
                    }

                    if (dataPackage.StorageRemainCapacity) {
                        let remainCapacity = dataPackage.StorageRemainCapacity.value;
                        stateProps.storageRemainCapacity = (remainCapacity / 1000).toFixed(2);
                    }

                    if (stateProps.storageTotalCapacity != 0 && stateProps.storageRemainCapacity != 0) {
                        stateProps.storageUsedCapacity = (stateProps.storageTotalCapacity - stateProps.storageRemainCapacity).toFixed(2);
                    }

                    if (stateProps.storageUsedCapacity >= 0.01) { //格式化后，可用存储也许不为0，允许10MB偏差
                        let screenWidth = Dimensions.get('window').width;
                        stateProps.usedVolumeStickWidth = (screenWidth - 60) * stateProps.storageUsedCapacity / stateProps.storageTotalCapacity;
                        if (stateProps.usedVolumeStickWidth < 15) { //View宽度不够15，设置为15，防止绘制圆角View失败
                            stateProps.usedVolumeStickWidth = 15;
                        }
                        if (stateProps.usedVolumeStickWidth > screenWidth - 60) {
                            stateProps.usedVolumeStickWidth = screenWidth - 60;
                        }

                    } else {
                        stateProps.usedVolumeStickWidth = 0;
                        if (value == 1 && type == 2) { //卡状态正常，已用内容为0，且处于格式化后的轮询状态，则格式化成功
                            //存在刚格式化，sdCardStatus状态返回1的情况
                            showToast(I18n.t("sdcard_format_success"));
                            GlobalUtil.isSdShouldFormat = false;
                            GlobalUtil.isSdCanPullData = true;
                            this.setState({loopQueryType: 0});
                        }
                    }
                    if (value == 1) {
                        stateProps.sdCardStatus = value;
                    } else {
                        if (value == 2 && type == 2) { //状态为未格式化，且处于格式化后的轮询状态，则格式化失败
                            showToast(I18n.t("sdcard_format_fail"));
                            this.setState({loopQueryType: 0});
                        } else {
                            stateProps.sdCardStatus = value;
                        }
                    }
                    stateProps.NeedFormatStorageMedia = needFormatValue;
                    console.log("物模型云端数据--------", stateProps);
                    this.setState(stateProps);

                } else if (value == 3) { //正在格式化
                    // showLoading(stringsTo('storageCardFormating'), true,true);
                    //格式化时轮循到，不弹提示，因为有loading框
                    this.setState({sdCardStatus: value,NeedFormatStorageMedia:needFormatValue});
                } else if (value == 4) { //SD卡损坏
                    this.setState({sdCardStatus: value,NeedFormatStorageMedia:needFormatValue});
                    showLoading(stringsTo('storageCardFormating'), false);
                }else if(value == 5){
                    showLoading(stringsTo('storageCardFormating'), false);
                    this.setState({sdCardStatus: value,NeedFormatStorageMedia:needFormatValue});
                }
            }else {
                this.setState({
                    NeedFormatStorageMedia:needFormatValue,
                    sdCardStatus:value
                })
            }


        }).catch(err=>{
            console.log(JSON.stringify(err))
        });
    }

    getRecordModeStatus() {
        let comment = "";
        if (this.state.storageRecordMode == 1) {
            comment = stringsTo('setting_record_model_event');
        } else if (this.state.storageRecordMode == 2) {
            comment = stringsTo('setting_record_model_always');
        } else {
            comment = stringsTo('setting_record_model_close');
        }
        return comment;
    }

    /*弹出SD卡*/
    _injectSdCard() {
        showLoading(stringsTo('commLoadingText'), true);
        LetDevice.sendDeviceServerRequest("UmountStorageMedium", JSON.stringify({})).then((data) => {
            console.log(' UnmountStorageMedium  then-> ' + JSON.stringify(data));
            showLoading(stringsTo('commLoadingText'), false);
            this.setState({loopQueryType: 1, injectDialogVisible: false});
        }).catch((error) => {
            // alert('UnmountStorageMedium error ' + error)
            showLoading(stringsTo('commLoadingText'), false);
            this.setState({injectDialogVisible: false});
            showToast(I18n.t("injectFailed"));
        });

    }

    /*格式化SD卡*/
    _formatSdCard() {

        LetDevice.sendDeviceServerRequest("FormatStorageMedium", JSON.stringify({})).then((data) => {
            console.log(' FormatStorageMedium  then-> ', JSON.stringify(data));
            showLoading(stringsTo('storageCardFormating'), true,true);
            //先结束2.5s的请求SD卡数据,然后再重新请求
            //结束也不能解决正好到时间，已经请求SD卡信息，错误提示格式化成功后，又展示格式化中的问题
            //SD卡数据需要重新拉取
            GlobalUtil.needClearSDData = true;
            this.intervalQuery && clearInterval(this.intervalQuery);
            this.setState({loopQueryType: 2, dialogVisible: false},()=>{
                //增加一个标记
                this.readyToDealSDData = false;
                this.waitDealDataTimeout && clearTimeout(this.waitDealDataTimeout);
                this.waitDealDataTimeout = setTimeout(() => this.readyToDealSDData = true, 2400);

                this.getSdStatusAndStorageInfoNew(this.state.loopQueryType);
                this.intervalQuery = setInterval(() => this.getSdStatusAndStorageInfoNew(this.state.loopQueryType), 2500);
            });
        }).catch((error) => {
            console.log('FormatStorageMedium error ' + error)
            this.setState({dialogVisible: false});
            showToast(I18n.t("sdcard_format_fail"));
            showLoading(stringsTo('storageCardFormating'), false);
        });

    }

    /*页面加载中*/
    _renderLoading() {
        if (this.state.sdCardStatus != -1) {
            return null;
        }
        return (<View style={styles.loadingContainer}>
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#4A70A5"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: "black",
                    fontSize: 15,
                    marginTop: 20
                }}>
                {stringsTo("commLoadingText")}</Text>
        </View>);
    }

    /*未插卡时SD卡页面*/
    _renderEmptySdCard() {
        if (this.state.sdCardStatus != 0) {
            return null;
        }
        return (
            <View
                style={[{
                    flex: 1,
                    width: '100%',
                    height: '100%',
                    justifyContent: 'center',
                    alignItems: 'center'
                }, imiThemeManager.Styles.topLine]}>
                <Image
                    style={{width: 300, height: 200, marginTop: 80}}
                    source={require("../../resources/images/sd_empty_icon.png")}/>
                <Text
                    style={[{color: '#919BA5', fontSize: 15, alignSelf: 'center', textAlign: "center", marginTop: 20}]}
                    numberOfLines={3}>
                    {stringsTo('noSdCardTitle')}
                </Text>
            </View>

        )
    }


    // 存储卡页面
    _renderNormalSdCardNew() {
        if (this.state.sdCardStatus == 1 || this.state.sdCardStatus == 3) {

            return (
                <View style={{width: '100%', backgroundColor: '#F7F7F7', flex: 1}}>
                    <View style={styles.sdcardNewStyle}>
                        {
                            this.state.sdCardStatus == 3?
                                <ImageBackground style={{display: "flex", width: 208, height: 208, flexDirection: "column",alignItems:"center",justifyContent:'center'}}
                                                 source={this.state.switchStorage?require('../../resources/images/sd_cord_top_bg.png'):require('../../resources/images/sd_card_top_bg_no.png')} >
                                    <Text style={{fontSize: 17, fontWeight: '500', color: 'white',width:190,textAlign:'center'}}
                                          numberOfLines={2}>{stringsTo("sdcard_status4")}</Text>
                                </ImageBackground>:
                                <ImageBackground style={{
                                    display: "flex",
                                    width: 208,
                                    height: 208,
                                    flexDirection: "column",
                                    alignItems: "center"
                                }}
                                                 source={this.state.switchStorage ? require('../../resources/images/sd_cord_top_bg.png') : require('../../resources/images/sd_card_top_bg_no.png')}>
                                    <Text style={styles.stateCoverTitle}
                                          numberOfLines={1}>{this.state.switchStorage ? I18n.t('sdcard_status_normal_new') : I18n.t('sd_suspended')}</Text>
                                    <View style={styles.stateCoverSeprate}></View>
                                    <Text style={styles.stateCoverDetail}>{I18n.t('sdcard_status_more_new')}</Text>
                                    <Text style={styles.stateCoverDetail1}>{this.state.storageRemainCapacity + 'GB'}</Text>
                                </ImageBackground>
                        }


                    </View>
                    {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
                    {/*<ListItmeWithSwitch title={stringsTo('sd_storage_switch')} value={this.state.switchStorage}
                                    onValueChange={(value) => {

                                       LetDevice.setPropertyCloud({"StorageRecordMode":  value?2:0}).then(data => {
                                            this.setState({storageRecordMode: value?2:0,
                                                currenStorageRecordMode: value?2:0,
                                                switchStorage:value,
                                            });
                                        }).catch(error => {

                                        });

                                    }}

                />*/}
                    <ListItem title={stringsTo('setting_record_model')} accessibilityLabel={"setting_record_model"} disabled={this.state.sdCardStatus != 1} value={this.getRecordModeStatus()} onPress={() => {
                        if (this._isShareUser()) return;
                        this.setState({showCalibrationModal: true}, callback => {
                        });
                    }}/>

                    {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}

                    <ListItem title={stringsTo('storageCardFormat')} accessibilityLabel={"storage_card_format"} disabled={this.state.sdCardStatus != 1} onPress={() => {
                        if (this._isShareUser()) return;
                        this.setState({dialogVisible: true});
                    }}/>
                    <ListItem title={stringsTo('sdcard_exit_new')} accessibilityLabel={"sdcard_exit"} disabled={this.state.sdCardStatus != 1} onPress={() => {
                        if (this._isShareUser()) return;
                        this.setState({injectDialogVisible: true})
                    }}/>
                    <View style={{width: '100%', height: 150, backGroundColor: '#ffffff'}}>
                        <Text style={{
                            marginLeft: 14,
                            marginTop: 14,
                            marginRight: 14,
                            color: '#7F7F7F',
                            fontSize: 12
                        }}>{I18n.t('sdcard_tip1_new')}</Text>
                        <Text style={{
                            marginLeft: 14,
                            marginTop: 6,
                            marginRight: 14,
                            color: '#7F7F7F',
                            fontSize: 12,
                            lineHeight: 16
                        }} allowFontScaling={false}>{I18n.t('sdcard_tip2_new')}</Text>
                    </View>

                </View>
            );
        }else {
            return null;
        }

    }

    /*SD推出UI*/
    _renderLaunchSdCard() {
        if (this.state.sdCardStatus != 4) {
            return null;
        }
        return (
            <View style={{width: '100%'}}>
                <View style={styles.sdcardNewStyle}>
                    <ImageBackground style={{
                        display: "flex",
                        width: 208,
                        height: 208,
                        flexDirection: "column",
                        alignItems: "center"
                    }}
                                     source={require('../../resources/images/sd_error.png')}>
                        <Text style={[styles.stateCoverTitle, {textAlign: "center",width:190}]}
                              numberOfLines={2}>{I18n.t('sdcard_out_already')}</Text>
                        <View style={styles.stateCoverSeprate}></View>
                    </ImageBackground>
                </View>


            </View>
        );

    }

    /**
     * 060A01暂时保持原样
     * 040A03项目使用新SD卡方案
     * sdCardStatus
     * 5 SD卡异常
     * 2 未格式化
     */
    _renderAbnormalSdCard() {
        if ((this.state.NeedFormatStorageMedia == NeedFormatType.NORMAL
            || this.state.NeedFormatStorageMedia == NeedFormatType.HAS_OLD_INDEX)
            && (this.state.sdCardStatus == 5 || this.state.sdCardStatus == 2)) {
            //使用新T卡方案
            return (
                <View style={{width: '100%', flex: 1}}>
                    <View style={styles.sdcardNewStyle}>
                        <ImageBackground style={{
                            display: "flex",
                            width: 208,
                            height: 208,
                            flexDirection: "column",
                            alignItems: "center"
                        }}
                                         source={require('../../resources/images/sd_error.png')}>
                            <Text style={styles.stateCoverTitle}
                                  numberOfLines={1}>{I18n.t('sdcard_status_abnormal')}</Text>
                            <View style={styles.stateCoverSeprate}></View>
                        </ImageBackground>
                    </View>


                    <View style={{flex: 1}}></View>
                    <View style={{
                        width: '100%',
                        marginBottom: 20,
                        flexDirection: "row",
                        height: 45,
                        justifyContent: "center",
                        alignItems: 'center',
                    }}>
                        <TouchableOpacity style={[{
                            width: 300,
                            height: 45,
                            backgroundColor: "#F7F7F7",
                            borderRadius: 10,
                            justifyContent: "center",
                            alignItems: 'center',
                        }]}
                                          onPress={() => {
                                              if (this._isShareUser()) return;
                                              this.setState({dialogVisible: true});
                                          }}
                        >
                            <Text style={{fontSize: 15, fontWeight: "500", color: "#E74D4D"}}>
                                {I18n.t('storageCardFormat')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            );
        }else {
            return null;
        }


    }

    _renderNotFormatSdCard() {
        if (this.state.NeedFormatStorageMedia != NeedFormatType.NO_INDEX
            || this.state.sdCardStatus == 0){
            return null;
        }
        if (this.state.NeedFormatStorageMedia == NeedFormatType.NO_INDEX && this.state.sdCardStatus == 3){
            return null;
        }
        //使用新T卡方案
        return (
            <View style={{width: '100%', flex: 1}}>
                <View style={styles.sdcardNewStyle}>
                    <ImageBackground style={{
                        display: "flex",
                        width: 208,
                        height: 208,
                        flexDirection: "column",
                        alignItems: "center"
                    }}
                                     source={require('../../resources/images/sd_error.png')}>
                        <Text style={[styles.stateCoverTitle,{width:208,textAlign:'center'}]}
                              numberOfLines={2}>{I18n.t('sdcard_status_not_format')}</Text>
                        <View style={styles.stateCoverSeprate}></View>
                    </ImageBackground>
                    <Text style={{marginTop:15,textAlign:'center',paddingHorizontal:14,color:'#333333',fontSize:15}}>{I18n.t('sdcard_status_not_format_warning')}</Text>
                </View>


                <View style={{flex: 1}}></View>
                <View style={{
                    width: '100%',
                    marginBottom: 20,
                    flexDirection: "row",
                    height: 45,
                    justifyContent: "center",
                    alignItems: 'center',
                }}>
                    <TouchableOpacity
                        style={[{
                            width: 300,
                            height: 45,
                            backgroundColor: "#F7F7F7",
                            borderRadius: 10,
                            justifyContent: "center",
                            alignItems: 'center',
                        }]}
                        onPress={() => {
                            if (this._isShareUser()) return;
                            this.setState({dialogVisible: true});
                        }}
                    >
                        <Text style={{fontSize: 15, fontWeight: "500", color: "#E74D4D"}}>
                            {I18n.t('storageCardFormat')}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        );

    }

    _renderIsFormatSdCard() {
        if ((this.state.NeedFormatStorageMedia == NeedFormatType.NO_INDEX
            || this.state.NeedFormatStorageMedia == NeedFormatType.NORMAL)
            && this.state.sdCardStatus == 3) {
            //使用新T卡方案
            return (
                <View style={{width: '100%', flex: 1}}>
                    <View style={styles.sdcardNewStyle}>
                        <ImageBackground style={{
                            display: "flex",
                            width: 208,
                            height: 208,
                            flexDirection: "column",
                            alignItems: "center"
                        }}
                                         source={require('../../resources/images/sd_error.png')}>
                            <Text style={styles.stateCoverTitle}
                                  numberOfLines={1}>{I18n.t('storageCardFormating')}</Text>
                            <View style={styles.stateCoverSeprate}></View>
                        </ImageBackground>
                    </View>
                </View>
            );
        }else {
            return null;
        }


    }

    /*录制模式弹窗*/
    _renderRecordAlert() {
        if (!this.state.showCalibrationModal) {
            return null;
        }
        return (<MessageDialog
                title={I18n.t("setting_record_model")}
                message={''}
                visible={this.state.showCalibrationModal}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => this.setState({showCalibrationModal: false})
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({showCalibrationModal: false});
                            let params = {"StorageRecordMode": this.state.currenStorageRecordMode};
                            IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params)).then(data => {
                                // console.log(' 切换录制模式成功',params);
                                this.setState({
                                    storageRecordMode: this.state.currenStorageRecordMode,
                                    switchStorage: this.state.currenStorageRecordMode == 0 ? false : true,
                                });
                            }).catch(error => {
                                this.setState({currenStorageRecordMode: this.state.storageRecordMode});
                            });
                        }
                    }
                ]}
                onDismiss={() => this.setState({showCalibrationModal: false})}
            >
                <SingleChoiceItem
                    title={stringsTo('setting_record_model_always')}
                    subtitle={stringsTo('setting_record_model_always_title')}
                    titleColor={"#333333"}
                    subtitleColor={"#7f7f7f"}
                    onlyShowCheckedIcon={true}
                    containerStyle={{margin: 14, marginTop: 0, height: 70}}
                    checked={this.state.currenStorageRecordMode == 2 ? true : false}
                    onValueChange={(value) => {
                        this.setState({
                            currenStorageRecordMode: value ? 2 : this.state.storageRecordMode,
                        })
                    }}/>

                <SingleChoiceItem
                    title={stringsTo('setting_record_model_event')}
                    subtitle={stringsTo('setting_record_model_event_title')}
                    titleColor={"#333333"}
                    subtitleColor={"#7f7f7f"}
                    onlyShowCheckedIcon={true}
                    containerStyle={{margin: 14, marginTop: 0, height: 70}}
                    checked={this.state.currenStorageRecordMode == 1 ? true : false}
                    onValueChange={(value) => {
                        this.setState({
                            currenStorageRecordMode: value ? 1 : this.state.storageRecordMode,
                        })
                    }}/>
                <SingleChoiceItem
                    title={stringsTo('setting_record_model_close')}
                    subtitle={stringsTo('setting_record_model_close_title')}
                    titleColor={"#333333"}
                    subtitleColor={"#7f7f7f"}
                    onlyShowCheckedIcon={true}
                    containerStyle={{margin: 14, marginTop: 0, height: 70}}
                    checked={this.state.currenStorageRecordMode == 0 ? true : false}
                    onValueChange={(value) => {
                        this.setState({
                            currenStorageRecordMode: value ? 0 : this.state.storageRecordMode,
                        })
                    }}/>
            </MessageDialog>
        );
    }

    /*格式化SD卡二次确认对话框*/
    _rendDialog() {
        if (!this.state.dialogVisible) {
            return null;
        }
        return (<AlertDialog
            title={I18n.t("formatTitle")}
            message={I18n.t("formatMessage")}
            visible={this.state.dialogVisible}
            canDismiss={false}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    callback: _ => this.setState({dialogVisible: false})
                },
                {
                    text: I18n.t("ok_button"),
                    callback: _ => this._formatSdCard()
                }
            ]}
            onDismiss={() => this.setState({dialogVisible: false})}
        />);
    }

    _renderInjectDialog() {
        return (<AlertDialog
            showTitle={false}
            visible={this.state.injectDialogVisible}
            message={I18n.t("storageCardHint")}
            messageStyle={{
                marginTop: 20,
                marginBottom: 26,
                fontSize: 17,
                fontWeight: '700',
                color: "#333333"
            }}
            ignoreHint={this.state.isNeverRemind}
            onCheckValueChange={(checked) => {
                this.setState({isNeverRemind: checked});
            }}
            canDismiss={false}
            onDismiss={() => {
                this.setState({injectDialogVisible: false});
            }}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    callback: _ => {
                        this.setState({injectDialogVisible: false});
                    }
                },
                {
                    text: I18n.t("ok_button"),
                    callback: _ => {
                        this._injectSdCard();
                    }
                },
            ]}
        />);
    }

    _isShareUser() {
        if (LetDevice.isShareUser) {
            showToast(stringsTo('shareUser_tip'));
            return true;
        }
        return false;
    }

    render() {
        global.navigation = this.props.navigation;

        return (<View style={styles.container}>
            <NavigationBar
                title={I18n.t('record_files_sdcard')}
                left={[{key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop(),accessibilityLabel:"sdcard_storage_go_back"}]}
                right={[]}
            />
            {/*<Separator/>*/}

            {this._renderLoading()}

            {this._renderEmptySdCard()}

            <ScrollView
                contentContainerStyle={{flexDirection: "column", flexGrow: 1}}
                style={{flex: 1}} showsVerticalScrollIndicator={false}>

                {this._renderNormalSdCardNew()}
                {this._renderLaunchSdCard()}
                {this._renderRecordAlert()}
                {this._renderAbnormalSdCard()}
                {this._renderNotFormatSdCard()}
                {/*{this._renderIsFormatSdCard()}*/}
            </ScrollView>


            {/*  {this._renderAbnormalSdCard()}*/}

            {this._rendDialog()}
            {this._renderInjectDialog()}

            {/*{this._renderRecordAlert()}*/}

        </View>);
    }
}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#FFFFFF"
    },
    sdVolumeContainer: {
        height: 170,
        marginTop: 14,
        marginBottom: 20,
        marginHorizontal: 14,
        paddingHorizontal: 16,
        backgroundColor: "#496EE04C",
        borderRadius: 10,
        alignItems: "center",
    },
    sdcardNewStyle: {
        paddingTop: 42,
        width: '100%',
        minHeight: 300,
        alignItems: "center",
        paddingBottom: 42,
        backgroundColor: 'white',
    },
    // stateCover: {
    //     position: 'absolute',
    //     width: 208,
    //     height: 208,
    //     alignItems: 'center',
    //     backgroundColor:'orange',
    //     borderRadius:104,
    // },
    stateCoverTitle: {
        marginTop: 76,
        fontSize: 17,
        fontWeight: '500',
        color: 'white',
    },
    stateCoverSeprate: {
        marginTop: 20,
        backgroundColor: 'white',
        height: 1,
        width: 208 - 28 * 2,
        marginLeft: 28,
        marginRight: 28,
    },
    stateCoverDetail: {
        marginTop: 10,
        fontSize: 12,
        color: 'white',
    },
    stateCoverDetail1: {
        marginTop: 7,
        fontSize: 15,
        color: 'white',
    },
    imageStyle: {
        width: 60,
        height: 60,
        marginTop: 20,
        marginBottom: 24
    },
    volumeStickContainer: {
        width: "100%",
        height: 20,
        backgroundColor: "#FFFFFF",
        borderRadius: 10
    },
    volumeStickStyle: {
        position: "absolute",
        height: 20,
        backgroundColor: "#9E8BEF",
        borderRadius: 10
    },
    textContainer: {
        flexDirection: "row",
        justifyContent: "space-around",
    },
    volumeTextStyle: {
        flex: 1,
        fontSize: 14,
        color: "#4A70A5",
        marginTop: 10
    },
    destroyHintTextStyle: {
        width: "100%",
        fontSize: 14,
        color: "#FFFFFF",
        marginTop: 10,
        textAlign: "left"
    },
    loadingContainer: {
        width: "100%",
        height: "100%",
        position: "absolute",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "transparent"
    },

});
