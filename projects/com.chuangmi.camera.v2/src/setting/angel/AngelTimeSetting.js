
import React from 'react';
import {
    StyleSheet,
    View,
    ScrollView,
    Dimensions,
    BackHandler,
    TouchableWithoutFeedback,
    Text,
    Image,
    Platform,
    Picker,
    FlatList
} from 'react-native';
import ListItem from "../../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import I18n, {stringsTo} from '../../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {LetDevice} from "../../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import DatePicker from "../../../../../imilab-design-ui/src/widgets/settingUI/DatePicker";
import {showToast} from "../../../../../imilab-design-ui/src/widgets/IMIToast";
import {showLoading} from "../../../../../imilab-design-ui/src/widgets/Loading";
import NavigationBar from "../../../../com.chuangmi.door/src/CommonView/NavigationBar/NavigationBar";
import {isAndroid} from "../../../../../imilab-rn-sdk/utils/Utils";
import MessageDialog from "../../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog";
import DeviceTemplatesUtils from "../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import IMIHourPicker from "../../../../../imilab-design-ui/src/widgets/settingUI/IMIHourPicker";
import ListItmeWithSwitch from "../../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import IMIHourPickerV2 from "../../../../../imilab-design-ui/src/widgets/settingUI/IMIHourPickerV2";
import IMILogUtil from "../../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";

const screen_width = Dimensions.get('window').width;


/**
 * 生效时间页面
 */

export default class EffectiveTimeSetPage extends BaseDeviceComponent {

    static propTypes = {};

    constructor(props, context) {
        super(props, context);
        this.state={
            replyArr: [],//重复周期数组
            weekArr: [],//自定义弹窗数组
            selectWeek: [],// 自定义选中日期数组
            isPickerVisiable: false,
            isStartPicker: true,
            startTime: 12,
            startTimeStr: '',
            endTimeStr: '',
            endTime: 12,
            pickerCurrentTime: '',
            selectIndex: 0,
            showCustomDialog: false,
            isUnselectedAll: false,
            isShowCustomTit: false,
            weekTit: stringsTo("do_everyday"),
            firstShow: false,
            tempTimeStr: '',
            tempWeek: [],
            isShowOKDialog: false,
            msgDialogMsg: "",
            timeSelectIndex:0,//时间选择
            timeStr:"00:00-24:00",//自定义时间
            isShowTimeCustomTit:false,//是否显示自定义时间
            timeSwitch:this.props.route.params.timeSwitch,//默认是关闭

            // location:this.props.route.params.location,// 位置
            // isEdit:this.props.route.params.isEdit,//添加或编辑
            // areaAllArr:this.props.route.params.areaAllArr,//所有数据
            typeStr: this.props.route.params.type,
        }
        this.location = this.props.route.params.location - 1;
        this.areaAllArr = this.props.route.params.areaAllArr;
        // this.isEdit = this.props.route.params.isEdit;
    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
        console.log("jeff Mount params:",this.props.route.params);
    }

    componentWillUnmount() {
        if (this.state.typeStr == 'lightEffectTime') {
            this.timer && clearTimeout(this.timer);
        }
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress',this.onBackHandler);
        }
    }

    componentDidMount() {
        this.initArrData();
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };

    // 返回上一页
    _onPressBack = () => {
        this.props.navigation.pop();
    };

    //整形转bit字符串
    Int2bin(dec) {
        return (dec >>> 0).toString(2);
    }

    //bit字符串转整形
    bin2Int(dec) {
        return parseInt(dec, 2);
    }

    initArrData() {
        let arrData = [
            {name: stringsTo("do_everyday"), frequence: "*", isSelected: false},
            {name: stringsTo("do_weekday"), frequence: "1,2,3,4,5", isSelected: false},
            {name: stringsTo("do_weekend"), frequence: "6,7", isSelected: false},
            {name: stringsTo("do_custom"), frequence: "-1", isSelected: false}
        ];

        // let customStr = stringsTo("alarm_direction_custom") +' (00:00-23:59)';
        let customStr = stringsTo("alarm_direction_custom");
        let timeArrData = [
            {name: stringsTo("allDay_time"),isSelected: false},
            {name: stringsTo("day_time"),isSelected: false},
            {name: stringsTo("night_time"),isSelected: false},
            {name: customStr,isSelected: false}
        ];

        let weekData = [
            {name: stringsTo("week_new_1"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_2"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_3"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_4"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_5"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_6"), isSelectedDay: false, isDisabledDay: false},
            {name: stringsTo("week_new_7"), isSelectedDay: false, isDisabledDay: false},
        ];
        this.setState({replyArr: arrData,timeData:timeArrData});

        //编辑
        let item = this.areaAllArr[this.location];
        console.log('当前item', item);
        this.getRepeatTit(item,weekData,true);

        if(this.state.typeStr === "moveEffectTime" || this.state.typeStr === "peopleEffectTime"
            || this.state.typeStr === "loudEffectTime"){//移动，人形,侦测
            let item = this.areaAllArr[this.location];
            this.setState({
                detectSwitch:item.detectSwitch,
                push_flag:item.push_flag,
                sound_alarm:item.sound_alarm,
                light_alarm:item.light_alarm,
            });
        }
    }

    getRepeatTit(item,weekData,value) {
        console.log('当前item,value',item,value,weekData);
        let startTime = item.startTime;
        let endTime = item.endTime;
        let repeatStr = item.repeatTime;
        let repeatArr = repeatStr.split(',');
        let tempStr  = '';

        let startTimeStr;
        if(startTime.length===4){
            startTimeStr = `${startTime.substring(0,2)}:${startTime.substring(2,4)}`;
        }else{
            startTimeStr = startTime > 9 ? startTime + ':00' : '0' + startTime + ':00';
            startTime = (startTime > 9 ? startTime: '0' + startTime)+"00"
        }
        let endTimeStr;
        if(endTime.length===4){
            endTimeStr = `${endTime.substring(0,2)}:${endTime.substring(2,4)}`;
        }else{
            endTimeStr = endTime > 9 ? endTime + ':00' :'0' + endTime + ':00';
            endTime = (endTime > 9 ? endTime: '0' + endTime)+"00"
        }

        tempStr = startTimeStr+'-'+endTimeStr;
        console.log('编辑进入显示的字符串',tempStr);
        if (value){
            this.setState({startTime: startTime, endTime: endTime, startTimeStr: startTimeStr, endTimeStr: endTimeStr});
            if (startTime == "0000" && endTime == "2400"){
                this.setState({timeSelectIndex:0,timeStr:tempStr})
            }else if (startTime == "0800" && endTime == "2000"){
                this.setState({timeSelectIndex:1,timeStr:tempStr})
            }else if (startTime == "2000" && endTime == "0800"){
                this.setState({timeSelectIndex:2,timeStr:tempStr})
            } else {

                if (endTime == "2400"){
                    tempStr = startTimeStr+'-23:59';
                }
                this.setState({timeSelectIndex:3,timeStr:tempStr,isShowTimeCustomTit:true})
            }
        }

        console.log('重复周期数组---', repeatArr);
        let tempWeekArr = [];
        let tempSelectWeek = [];
        for (let i = 0; i < repeatArr.length; i++) {
            let tempValue = parseInt(repeatArr[i]);
            console.log('当前重复周期---', tempValue);
            if (tempValue == 1) {
                tempWeekArr.push(i);
                tempSelectWeek.push(1);
            } else {
                tempSelectWeek.push(0);
            }
        }

        if (tempWeekArr.length > 0) {
            tempWeekArr.sort(((a, b) => {
                return a - b
            }))
        }

        console.log('当前设置的重复周期', tempWeekArr, tempWeekArr.length);
        if ((tempWeekArr.length == 2) && (tempWeekArr[0] == 5) && (tempWeekArr[1] == 6)) {
            // 周末
            if (value){
                this.setState({selectIndex: 2, weekTit: stringsTo("do_weekend"), weekArr: weekData});
            }else {
                let tempStr =  stringsTo("do_weekend") + startTimeStr + '-' + endTimeStr;
                let tempLastStr = stringsTo("tip_time_repeat")+'\n'+tempStr;
                this.setState({msgDialogMsg:tempLastStr,isShowOKDialog:true});
                // Toast.show(tempLastStr);
            }
        } else if ((tempWeekArr.length == 5) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4)) {
            // 周一到周五
            if (value){
                this.setState({selectIndex: 1, weekTit: stringsTo("do_weekday"), weekArr: weekData});
            }else {
                let tempStr =  stringsTo("do_weekday") + startTimeStr + '-' + endTimeStr;
                let tempLastStr = stringsTo("tip_time_repeat")+'\n'+tempStr;
                this.setState({msgDialogMsg:tempLastStr,isShowOKDialog:true});
                // Toast.show(tempLastStr);
            }
        } else if ((tempWeekArr.length == 7) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4) && (tempWeekArr[5] == 5) && (tempWeekArr[6] == 6)) {
            // 每天
            if (value){
                this.setState({selectIndex: 0, weekTit: stringsTo("do_everyday"), weekArr: weekData});
            }else {
                let tempStr =  stringsTo("do_everyday") + startTimeStr + '-' + endTimeStr;
                let tempLastStr = stringsTo("tip_time_repeat")+'\n'+tempStr;
                this.setState({msgDialogMsg:tempLastStr,isShowOKDialog:true});
                // Toast.show(tempLastStr);
            }
        } else {
            // 自定义
            console.log('走自定义', tempWeekArr);
            let tempSelectWeek = [];
            for (let j = 0; j < repeatArr.length; j++) {
                let tempValue = parseInt(repeatArr[j]);
                let item = weekData[j];
                console.log('当前item', item);
                if (tempValue == 1) {
                    if (value){
                        item.isSelectedDay = true;
                    }
                    tempSelectWeek.push(1);
                } else {
                    if (value){
                        item.isSelectedDay = false;
                    }
                    tempSelectWeek.push(0);
                }
            }
            console.log('临时选中的selectWeek', tempSelectWeek, weekData);
            let weekTxt = '';
            if (tempWeekArr.includes(0)) {
                weekTxt += " " + stringsTo("week_new_1");
            }
            if (tempWeekArr.includes(1)) {
                weekTxt += " " + stringsTo("week_new_2");
            }
            if (tempWeekArr.includes(2)) {
                weekTxt += " " + stringsTo("week_new_3");

            }
            if (tempWeekArr.includes(3)) {
                weekTxt += " " + stringsTo("week_new_4");
            }
            if (tempWeekArr.includes(4)) {
                weekTxt += " " + stringsTo("week_new_5");
            }
            if (tempWeekArr.includes(5)) {
                weekTxt += " " + stringsTo("week_new_6");
            }
            if (tempWeekArr.includes(6)) {
                weekTxt += " " + stringsTo("week_new_7");
            }

            if (value){
                this.setState({
                    selectWeek: tempSelectWeek,
                    weekTit: weekTxt,
                    selectIndex: 3,
                    isShowCustomTit: true,
                    weekArr: weekData
                });
            }else {
                let tempStr =  weekTxt + startTimeStr + '-' + endTimeStr;
                let tempLastStr = stringsTo("tip_time_repeat")+'\n'+tempStr;
                this.setState({msgDialogMsg:tempLastStr,isShowOKDialog:true});
                // Toast.show(tempLastStr);
            }
        }
    }

    // 先获取在线生效时间之后然后再保存
    updateTime() {
        if (this.state.typeStr === "moveEffectTime" || this.state.typeStr === "peopleEffectTime"
            || this.state.typeStr === "loudEffectTime" || this.state.typeStr === "fenceEffectTime"
            || this.state.typeStr === "keyAreaEffectTime") {
            //移动，人形,异响，越界
            this._saveTime();
        }else {
            showLoading(stringsTo('commWaitText'), true);
            LetDevice.getPropertyCloud('FavAreaAttr').then((data) => {

                data = typeof(data) === 'string'?JSON.parse(data):data;
                if (data.length > 0) {
                    //有值
                    // let tempData = JSON.parse(data);
                    console.log('常用角度-tempData', data);
                    for (let i = 0; i < data.length; i++) {
                        let singleArea = data[i];
                        this.getNewData(singleArea);
                    }
                }
                this._saveTime();
            }).catch(error => {
                console.log("jeff 常用角度" + error);
                showToast(I18n.t('waitFailedTip'));
                showLoading(false);
                // this._saveTime();
            });
        }
    }

    getNewData(singleArea) {
        console.log('从物模型获取的angel',singleArea);
        let startTime = 0;
        let endTime = 0;
        if (singleArea.repeat_time){
            // 已编辑
            let timeStr = singleArea.repeat_time;
            let timeArr = timeStr.split(',');
            startTime = timeArr[0];
            endTime = timeArr[1];
        }

        for (let j = 0;j<this.areaAllArr.length;j++){
            let normalArea = this.areaAllArr[j];
            if (singleArea.idx == j+1){
                normalArea.active_time = singleArea.active_time;
                normalArea.startTime = startTime;
                normalArea.endTime = endTime;
                normalArea.repeatTime = singleArea.repeat_week;
                normalArea.active_time_switch = singleArea.active_time_switch == 1?true:false;
                normalArea.hasImg = true;
                normalArea.monPosition = singleArea.pos;
                normalArea.repeatTimeTit = this.getTimeTit(startTime, endTime, singleArea.repeat_week)
                console.log('当前norm--',normalArea.index,normalArea,normalArea.startTime,normalArea.active_time);
            }
        }
    }

    getTimeTit(timeStart, timeEnd, timeRepeat) {
        // console.log('当前设置时间标题', item);
        let startTime = timeStart;
        let endTime = timeEnd;
        let repeatStr = timeRepeat;
        let repeatArr = repeatStr.split(',');
        console.log('当前获取重复标题', startTime, endTime, repeatStr);
        let startTimeStr;
        if(startTime.length===4){
            startTimeStr = `${startTime.substring(0,2)}:${startTime.substring(2,4)}`;
        }else{
            startTimeStr = startTime > 9 ? startTime + ':00' : '0' + startTime + ':00';
        }
        let endTimeStr;
        if(endTime.length===4){
            endTimeStr = `${endTime.substring(0,2)}:${endTime.substring(2,4)}`;
        }else{
            endTimeStr = endTime > 9 ? endTime + ':00' :'0' + endTime + ':00';
        }
        // let startTimeStr = startTime + ':00';
        // let endTimeStr = endTime + ':00';

        console.log('重复周期数组---', repeatArr);
        let tempWeekArr = [];
        let tempSelectWeek = [];
        for (let i = 0; i < repeatArr.length; i++) {
            let tempValue = parseInt(repeatArr[i]);
            // console.log('当前重复周期---', tempValue);
            if (tempValue == 1) {
                tempWeekArr.push(i);
                tempSelectWeek.push(1);
            } else {
                tempSelectWeek.push(0);
            }
        }

        if (tempWeekArr.length > 0) {
            tempWeekArr.sort(((a, b) => {
                return a - b
            }))
        }

        console.log('当前设置的重复周期', tempWeekArr, tempWeekArr.length);
        if ((tempWeekArr.length == 2) && (tempWeekArr[0] == 5) && (tempWeekArr[1] == 6)) {
            // 周末
            let tempStr = stringsTo("do_weekend") + startTimeStr + '-' + endTimeStr;
            // item.repeatTimeTit = tempStr;
            return tempStr;
        } else if ((tempWeekArr.length == 5) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4)) {
            // 周一到周五
            let tempStr = stringsTo("do_weekday") + startTimeStr + '-' + endTimeStr;
            return tempStr;
            // item.repeatTimeTit = tempStr;

        } else if ((tempWeekArr.length == 7) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4) && (tempWeekArr[5] == 5) && (tempWeekArr[6] == 6)) {
            // 每天
            let tempStr = stringsTo("do_everyday") + startTimeStr + '-' + endTimeStr;
            return tempStr;
            // item.repeatTimeTit = tempStr;
        } else {
            // 自定义
            console.log('走自定义', tempWeekArr, repeatArr);
            let tempSelectWeek = [];
            for (let j = 0; j < repeatArr.length; j++) {
                let tempValue = parseInt(repeatArr[j]);
                if (tempValue == 1) {
                    tempSelectWeek.push(1);
                } else {
                    tempSelectWeek.push(0);
                }
            }
            console.log('临时选中的selectWeek', tempSelectWeek);
            let weekTxt = '';
            if (tempWeekArr.includes(0)) {
                weekTxt += " " + stringsTo("week_new_1");
            }
            if (tempWeekArr.includes(1)) {
                weekTxt += " " + stringsTo("week_new_2");
            }
            if (tempWeekArr.includes(2)) {
                weekTxt += " " + stringsTo("week_new_3");

            }
            if (tempWeekArr.includes(3)) {
                weekTxt += " " + stringsTo("week_new_4");
            }
            if (tempWeekArr.includes(4)) {
                weekTxt += " " + stringsTo("week_new_5");
            }
            if (tempWeekArr.includes(5)) {
                weekTxt += " " + stringsTo("week_new_6");
            }
            if (tempWeekArr.includes(6)) {
                weekTxt += " " + stringsTo("week_new_7");
            }
            let tempStr = weekTxt + startTimeStr + '-' + endTimeStr;
            console.log('自定义str', tempStr);
            return tempStr;
            // item.repeatTimeTit = tempStr;
        }
    }

    // 保存最终时间数据
    _saveTime() {
        console.log('当前选择的重复周期--', this.state.weekTit);
        let startTime = "0000";
        let endTime = "2400";
        let tempTimeStr = "00:00:-24:00";
        if (this.state.timeSelectIndex == 0){
            startTime = "0000";
            endTime  = "2400";
            tempTimeStr = "00:00-24:00";
        }else if (this.state.timeSelectIndex == 1){
            startTime = "0800";
            endTime  = "2000";
            tempTimeStr = "08:00-20:00";
        }else if (this.state.timeSelectIndex == 2){
            startTime = "2000";
            endTime  = "0800";
            tempTimeStr = "20:00-8:00";
        }else if (this.state.timeSelectIndex == 3){

            // startTime = parseInt(this.state.startTime, 10);
            // endTime = parseInt(this.state.endTime, 10);
            startTime = this.state.startTime;
            endTime = this.state.endTime;
            let startTimeHour = startTime.substring(0,2);
            let startTimeMinute = startTime.substring(2,4);
            let endTimeHour = endTime.substring(0,2);
            let endTimeMinute = endTime.substring(2,4);
            //在常用角度中用到
            tempTimeStr = startTimeHour+":"+startTimeMinute+'-'+endTimeHour+":"+endTimeMinute;
        }

        // let startTime = parseInt(this.state.startTime, 10);
        // let endTime = parseInt(this.state.endTime, 10);

        let tempIndex = this.state.selectIndex;
        let tempWeekStr = '';
        if (tempIndex == 0) {
            // 每天
            tempWeekStr = '1,1,1,1,1,1,1';
        } else if (tempIndex == 1) {
            // 周一到周五
            tempWeekStr = '1,1,1,1,1,0,0'
        } else if (tempIndex == 2) {
            // 周末
            tempWeekStr = '0,0,0,0,0,1,1'
        } else {
            // 自定义
            let tempWeek = this.state.selectWeek;
            tempWeekStr = tempWeek.toString();
        }
        //判读是否设置了重复的生效时间
        //1、判断设置的时间是否有重叠
        //   与其他设置的生效时间都没有重叠，不用考虑重复周期，可直接设置
        //   与其他设置的生效时间有重叠，判断重复周期
        //2、周期是否有重叠 有-不可设置   无-可设置
        //处理时间段是否有交集
        let hasMix = false;
        let startInt = parseInt(startTime);
        let endInt = parseInt(endTime);
        let repeatIndex = -1;
        console.log("areaAllArr",this.areaAllArr,this.location)
        for (let i = 0; i < this.areaAllArr.length; i++) {
            let curArea = this.areaAllArr[i];
            if (this.location != i && curArea.active_time_switch) {
                //不是自己的常用角度
                let curStartInt = parseInt(curArea.startTime);
                let curEndInt = parseInt(curArea.endTime);
                if (curStartInt<curEndInt){
                    //开始时间小于结束时间
                    if (startInt<endInt){
                        //需要添加的开始时间小于结束时间
                        //0800-2000 0700-1900
                        if ((curStartInt <= startInt && startInt < curEndInt)
                            || (curStartInt < endInt && endInt <= curEndInt)){
                            //新添加的时间，开始时间或者结束时间，处于已添加生效时间内
                            let isRepeat = this.checkWeek(tempWeekStr,i);
                            if (isRepeat){
                                repeatIndex = i;
                                hasMix = true;
                                break;
                            }
                        }

                        if ((startInt <= curStartInt && curStartInt < endInt)
                            || (startInt < curEndInt && curEndInt <= startInt)){
                            //已添加的时间，开始时间或者结束时间，处于新添加生效时间内
                            let isRepeat = this.checkWeek(tempWeekStr,i);
                            if (isRepeat){
                                repeatIndex = i;
                                hasMix = true;
                                break;
                            }
                        }
                    }else {
                        //开始时间大于结束时间
                        if (startInt < curStartInt
                            || (curStartInt< startInt && startInt< curEndInt)
                            || endInt > curEndInt
                            || (curStartInt < endInt && endInt < curEndInt)){
                            let isRepeat = this.checkWeek(tempWeekStr,i);
                            if (isRepeat){
                                repeatIndex = i;
                                hasMix = true;
                                break;
                            }
                        }
                    }

                }else {
                    //开始时间大于结束时间

                    if (startInt>endInt){
                        //20:00 --- 0800   19:00 --- 14:00
                        if (startInt >= curStartInt
                            || startInt <= curEndInt
                            || endInt >= curEndInt){
                            let isRepeat = this.checkWeek(tempWeekStr,i);
                            if (isRepeat){
                                repeatIndex = i;
                                hasMix = true;
                                break;
                            }
                        }
                    }else {
                        //20:00 --- 08:00   00:00 --- 00:01
                        if (startInt >= curStartInt
                            || startInt <= curEndInt
                            || endInt >= curStartInt
                            || endInt <= curEndInt){
                            let isRepeat = this.checkWeek(tempWeekStr,i);
                            if (isRepeat){
                                repeatIndex = i;
                                hasMix = true;
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (hasMix && repeatIndex !=-1){
            showLoading(false);
            let curArea = this.areaAllArr[repeatIndex];
            this.getRepeatTit(curArea,this.state.weekArr,false);
            return;
        }

        let timeArr = []
        let tempData = {
            active_time: timeArr,
            showTit: this.state.weekTit + " " + tempTimeStr,
            startTime: startTime,
            endTime: endTime,
            repeatTime: tempWeekStr,
            timeSwitch:this.state.timeSwitch,
        }
        let tempBackData = JSON.stringify(tempData);
        showLoading(false);
        this.setEffectTime(timeArr,startTime,endTime,tempWeekStr);
        if (this.props.route.params.callback) {
            this.props.route.params.callback(tempBackData);
        }
        this.props.navigation.pop();
    }

    checkWeek(tempWeekStr,index){
        //有重复时间
        //判断周期是否有重复，如果周期也有重复，提示时间段重复
        //周一周二周三  0800---2000  周四周五 1300----1400
        let tempWeekArr = tempWeekStr.split(",");
        let curArea = this.areaAllArr[index];
        if (curArea.active_time_switch) {
            let repeatWeek = curArea.repeatTime.split(",");
            for (let i = 0; i < tempWeekArr.length; i++) {
                if (tempWeekArr[i] == repeatWeek[i] && repeatWeek[i] == 1){
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 设置智能语音提示
     */
    setEffectTime(active_time,startTime,endTime,repeatTime){
        console.log('jeff typeStr:',this.state.typeStr);
        if (this.state.typeStr === "moveEffectTime" || this.state.typeStr === "peopleEffectTime"
            || this.state.typeStr === "loudEffectTime" || this.state.typeStr == 'motorVehicleEffectTime'
            || this.state.typeStr == 'nonVehicleEffectTime') {//移动，人形,侦测
            this.updateEffectTimeMsg(this.state.detectSwitch,this.state.push_flag,this.state.timeSwitch,
                '['+active_time+']',startTime+","+endTime,repeatTime,this.state.sound_alarm,this.state.light_alarm);
        }
    }
    /**
     * 获取事件属性
     * 参数名称：switch 开关
     * 参数名称：push_flag 推送信息开关
     * 参数名称：active_time_switch 定时生效开关
     * 参数名称：active_time 生效时间
     * 参数名称：repeat_time 重复时间
     * 参数名称：repeat_week 重复周期
     * 参数名称：sound_alarm 智能语音提示
     * 参数名称：light_alarm 声光
     */
    updateEffectTimeMsg(switch_on,push_flag,active_time_switch,active_time,repeat_time,repeat_week,sound_alarm,light_alarm) {
        showLoading(stringsTo('commWaitText'), true);
        let {showSoundLightAlarm} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let value;
        let params;
        if (this.state.typeStr == "moveEffectTime"){
            value = {switch:switch_on ? 1 : 0,push_flag:push_flag ? 1 : 0,active_time_switch:active_time_switch?1:0,
                active_time:active_time,repeat_time:repeat_time,repeat_week:repeat_week,sound_alarm:sound_alarm};
            if (showSoundLightAlarm){
                //声光报警，声光
                value['light_alarm'] = light_alarm;
            }
            params = {MoveDetectionAttr:value};

        }else if(this.state.typeStr == "peopleEffectTime"){
            value = {switch:switch_on ? 1 : 0,push_flag:push_flag ? 1 : 0,active_time_switch:active_time_switch?1:0,
                    active_time:active_time,repeat_time:repeat_time,repeat_week:repeat_week,sound_alarm:sound_alarm};
            if (showSoundLightAlarm){
                //声光报警，声光
                value['light_alarm'] = light_alarm;
            }
            params = {PeopleDetectionAttr:value};
        }else if(this.state.typeStr == "loudEffectTime"){
            value = {switch:switch_on ? 1 : 0,push_flag:push_flag ? 1 : 0,active_time_switch:active_time_switch?1:0,
                    active_time:active_time,repeat_time:repeat_time,repeat_week:repeat_week};
            if (showSoundLightAlarm){
                //声光报警，声光
                value['light_alarm'] = light_alarm;
            }
            params = {LoudDetectionAttr:value};
        }else if(this.state.typeStr == "motorVehicleEffectTime"){
            value = {switch:switch_on ? 1 : 0,push_flag:push_flag ? 1 : 0,active_time_switch:active_time_switch?1:0,
                active_time:active_time,repeat_time:repeat_time,repeat_week:repeat_week,sound_alarm:sound_alarm};
            if (showSoundLightAlarm){
                //声光报警，声光
                value['light_alarm'] = light_alarm;
            }
            params = {MotorVehicleDetectionAttr:value};
        }else if(this.state.typeStr == "nonVehicleEffectTime"){
            value = {switch:switch_on ? 1 : 0,push_flag:push_flag ? 1 : 0,active_time_switch:active_time_switch?1:0,
                active_time:active_time,repeat_time:repeat_time,repeat_week:repeat_week,sound_alarm:sound_alarm};
            if (showSoundLightAlarm){
                //声光报警，声光
                value['light_alarm'] = light_alarm;
            }
            params = {NonMotorVehicleDetectionAttr:value};
        }

        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
        }).catch((error) => {
            console.log('jeff 失败----',error);
            showToast(I18n.t('waitFailedTip'));
            this.setState({timeSwitch: !this.state.timeSwitch});
            showLoading(false);
        });
    }
    _renderMsgDialog() {
        return (
            <MessageDialog
                showTitle={false}
                visible={this.state.isShowOKDialog}
                containerStyle={{marginBottom:15}}
                message={this.state.msgDialogMsg}
                messageStyle={{textAlign: 'center'}}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({
                                isShowOKDialog: false,
                            });
                        }
                    }
                ]}
            >
            </MessageDialog>
        );
    }




    render(){
        return(<View style = {{flex: 1, backgroundColor: '#FFFFFF'}}>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                title={stringsTo("timed_effective")}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this._onPressBack()}]}
                right={[{
                key: NavigationBar.ICON.CUSTOM,
                n_source:require('../../../resources/images/newLive/icon_angel_save.png'),
                onPress: _=> {
                    console.log("添加生效时间");
                    this.updateTime();
                },
                accessibilityLabel: "add_time_save",
            }]}
            />

            <ScrollView showsVerticalScrollIndicator={false}>
                <ListItmeWithSwitch title={stringsTo("timed_effective")}
                                    value={this.state.timeSwitch}
                                    onValueChange={(value) => {
                                        this.setState({timeSwitch:value})
                                    }}
                                    accessibilityLabel={["time_off","time_on"]}
                />
                {
                  this.state.timeSwitch ? <View>
                      <View style={{height:14,backgroundColor: "#F7F7F7"}} />
                      <View style={{width: '100%', height: 30,backgroundColor:'#FFFFFF'}}>
                          <Text style={{marginLeft: 14, lineHeight: 29, textAlign: 'left', color: '#7F7F7F', fontSize: 12}}>
                              {stringsTo("effective_time")}</Text>
                      </View>
                      {this._renderTimeList()}
                      <View style={{height:14,backgroundColor: "#F7F7F7"}} />
                      <View style={{width: '100%', height: 30,backgroundColor:'#FFFFFF'}}>
                          <Text style={{marginLeft: 14, lineHeight: 29, textAlign: 'left', color: '#7F7F7F', fontSize: 12}}>
                              {stringsTo("repeat_period")}</Text>
                      </View>
                      <FlatList
                          data={this.state.replyArr}
                          renderItem={this._renderItem}
                          keyExtractor={(item, index) => `key_${index}`}
                          extraData={this.state}
                          // ItemSeparatorComponent={() => <View style={{alignItems: 'center', width: '100%'}}><View
                          //     style={{height: 0.5, width: '100%', backgroundColor: '#CFCFCF'}}/></View>}
                      />
                  </View> : null
                }
            </ScrollView>
            {/*时间段选择*/}
            {this.renderHourPicker()}
            {this.renderCustomView()}
            {this._renderMsgDialog()}
        </View>)
    }

    _renderItem = ({item, index}) => {
        let label;
        if (index == 0){
            label = "do_everyday";
        }else if (index == 1){
            label = "do_weekday";
        }else if (index == 2){
            label = "do_weekend";
        }else {
            label = "do_custom";
        }
        return (
            <TouchableWithoutFeedback
                disabled={item.isSelected}
                accessibilityLabel={label}
                onPress={() => {
                    console.log('当前重复点击idx = ' + index);
                    if (index === 3) {
                        // this._showTempSelectDay();
                        console.log('点击自定义前的weekArr',this.state.weekArr);
                        this.setState({showCustomDialog: true, firstShow: false});
                    } else {

                        let weekTitle = stringsTo("do_everyday");
                        if (index == 0) {
                            weekTitle = stringsTo("do_everyday");
                        } else if (index == 1) {
                            weekTitle = stringsTo("do_weekday");
                        } else if (index == 2) {
                            weekTitle = stringsTo("do_weekend");
                        }
                        this.setState({
                            selectIndex: index,
                            isShowCustomTit: false,
                            firstShow: false,
                            weekTit: weekTitle
                        });
                        console.log('hhhh', this.state.selectIndex);
                    }
                }}
            >
                <View style={{
                    flexDirection: 'column',
                    height: 60,
                    display: "flex",
                    alignItems: "center",
                    backgroundColor: 'white'
                }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            width: '100%',
                            height: "100%",
                            backgroundColor: 'white',
                            alignItems: "center"
                        }
                        }>
                        <Text style={{
                            flexGrow: 1,
                            marginLeft: 14,
                            fontSize: 16,
                            color: item.isSelected ? "#7F7F7F" : (index === this.state.selectIndex ? "#4A70A5" : "#000000"),
                            textAlign: 'left'
                        }}>{index === 3 ? (this.state.isShowCustomTit ? (stringsTo("do_custom") + '(' + this.state.weekTit + ' ' + ')') : stringsTo("do_custom")) : item.name}</Text>
                        <Image style={{height: 25, width: 25, marginRight: 14, resizeMode: 'contain', display: index === this.state.selectIndex ? "flex" : 'none'}}
                               source={require('../../../resources/images/newLive/icon_choose_sel.png')}/>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        );
    }

    _renderTimeList() {
        return(
            <View style={{width:"100%",height:60*4}}>
                <FlatList
                    data={this.state.timeData}
                    renderItem={this._renderTimeItem}
                    keyExtractor={(item, index) => `key_time_${index}`}
                    extraData={this.state}
                    // ItemSeparatorComponent={() => <View style={{alignItems: 'center', width: '100%'}}><View
                    //     style={{height: 0.5, width: '100%', backgroundColor: '#CFCFCF'}}/></View>}
                />
            </View>
        )
    }
    _renderTimeItem = ({item, index}) => {
        return (
            <TouchableWithoutFeedback
                disabled={item.isSelected}
                accessibilityLabel={"time_item"}
                onPress={() => {
                    console.log('当前idx = ' + index);
                    if (index === 3) {
                        // this._showTempSelectDay();
                        this.setState({isPickerVisiable: true});
                    } else {
                        this.setState({
                            timeSelectIndex:index,
                        });
                        console.log('时间选择---hhhh', this.state.timeSelectIndex);
                    }
                }}
            >
                <View style={{
                    flexDirection: 'column',
                    height: 60,
                    display: "flex",
                    alignItems: "center",
                    backgroundColor:'white'
                }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            width: '100%',
                            height: "100%",
                            backgroundColor:'white',
                            alignItems: "center"
                        }
                        }>
                        <Text style={{
                            flexGrow: 1,
                            marginLeft: 14,
                            fontSize: 16,
                            color: item.isSelected ? "#7F7F7F" : (index === this.state.timeSelectIndex ? "#4A70A5" : "#000000"),
                            textAlign: 'left'
                        }}>{index === 3 ? (this.state.isShowTimeCustomTit ? (stringsTo("do_custom") + '(' + this.state.timeStr + ')') : item.name) : item.name}</Text>
                        <Image style={{height: 25, width: 25, marginRight: 14, resizeMode: 'contain', display: index === this.state.timeSelectIndex ? "flex" : 'none'}}
                               source={require('../../../resources/images/newLive/icon_choose_sel.png')}/>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        );
    }

    renderHourPicker() {
        let tempArr = ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00",
            "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00", "24:00"];
        let newArr = [tempArr,tempArr];
        let tempStartTime = this.state.startTime > 9 ? this.state.startTime + ":00" : ("0"+this.state.startTime) + ":00";
        let tempEndTime = this.state.endTime > 9 ? this.state.endTime + ":00" : ("0"+this.state.endTime) + ":00";
        let startTimeHour;
        let startTimeMinute;
        let endTimeHour;
        let endTimeMinute;
        if (this.state.startTime.length === 4){
            startTimeHour = this.state.startTime.substring(0,2);
            startTimeMinute = this.state.startTime.substring(2,4);
        }else {
            startTimeHour = this.state.startTime > 9 ? (this.state.startTime +""): "0"+this.state.startTime
            startTimeMinute = "00";
        }

        if (this.state.endTime.length === 4){
            endTimeHour = this.state.endTime.substring(0,2);
            endTimeMinute = this.state.endTime.substring(2,4);
        }else {
            endTimeHour = this.state.endTime > 9 ? this.state.endTime +"": "0"+this.state.endTime
            endTimeMinute = "00";
        }

        if (endTimeHour==24){
            endTimeHour = "23";
            endTimeMinute = "59";
        }

        console.log('当前开始时间和结束时间',this.state.startTime,this.state.endTime,tempStartTime,tempEndTime);
        let curArr = [tempStartTime,tempEndTime];
        let startArr = [startTimeHour,startTimeMinute];
        let endArr = [endTimeHour,endTimeMinute];
        console.log('当前选中数组',curArr,startArr,endArr);
        return (
            <IMIHourPickerV2
                visible={this.state.isPickerVisiable}
                isShowCenterTxt={false}
                startTimeArr={startArr}
                endTimeArr={endArr}
                currentArr={curArr}
                onConfirm={(start,end) => {
                    console.log("_onIntervalChanged,value=", start);

                    if (start == "" || start == undefined) {
                        start = startArr;
                    }
                    if (end == "" || end == undefined){
                        end = endArr;
                    }
                    console.log('当前value值===', start);
                    this._onIntervalChanged(start,end);
                }
                }
                onDismiss={() =>
                    this.setState({
                        isPickerVisiable: false
                    })
                }
            />
        )
    }

    _onIntervalChanged(start,end) {
        console.log('时间选择Value数组---', start,end);
        // let timeArr = value;
        let startTime = start.join("");
        let endTime = end.join("");
        //需要展示的时间
        let tempTimeStr = start[0]+':'+start[1]+ '-' + end[0]+':'+end[1];
        console.log('临时时间字符串',tempTimeStr);
        if (startTime === endTime){
            showToast(I18n.t('time_equal'));
            return;
        }
        this.setState({isPickerVisiable: false, startTime: startTime, endTime:endTime,timeSelectIndex:3,isShowTimeCustomTit:true,timeStr: tempTimeStr,});
    }

    // 自定义
    renderCustomView() {
        return (
            <MessageDialog
                title={I18n.t("do_custom")}
                visible={this.state.showCustomDialog}
                containerStyle={{marginBottom:15}}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            console.log('当前选中的selectWeek', this.state.selectWeek);
                            if (this.state.selectWeek.length > 0) {
                                for (let i = 0; i < this.state.selectWeek.length; i++) {
                                    let tempValue = this.state.selectWeek[i];
                                    let item = this.state.weekArr[i];
                                    if (tempValue == 1) {
                                        item.isSelectedDay = true;
                                    } else {
                                        item.isSelectedDay = false;
                                    }
                                }
                            }

                            this.setState({
                                showCustomDialog: false,
                            });
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {this._sureWeekDay()}
                    },
                ]}
            >
                <FlatList
                    data={this.state.weekArr}
                    renderItem={this._renderWeekItem}
                    keyExtractor={(item, index) => `key_week_${index}`}
                    extraData={this.state}
                    // ItemSeparatorComponent={() => <View style={{alignItems: 'center', width: '100%'}}><View
                    //     style={{height: 0.5, width: '100%', backgroundColor: '#CFCFCF'}}/></View>}
                />
            </MessageDialog>
        );
    }

    _renderWeekItem = ({item, index}) => {
        return (
            <TouchableWithoutFeedback
                disabled={item.isDisabledDay}
                onPress={() => {
                    console.log('当前idx = ' + index);
                    item.isSelectedDay = !item.isSelectedDay;
                    this.setState({});
                }}
            >
                <View style={{
                    flexDirection: 'column',
                    width: '100%',
                    height: 60,
                    display: "flex",
                    alignItems: "center",
                    backgroundColor: '#FFFFFF'
                }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            width: '100%',
                            height: "100%",
                            backgroundColor: 'white',
                            alignItems: "center"
                        }
                        }>
                        <Text style={{
                            flexGrow: 1,
                            marginLeft: 14,
                            fontSize: 16,
                            color: item.isSelectedDay ? "#4A70A5" : "#000000",
                            textAlign: 'left'
                        }}>{item.name}</Text>
                        <Image style={{height: 25, width: 25, marginRight: 14, resizeMode: 'contain'}}
                               source={item.isSelectedDay ? require('.././../../resources/images/newLive/icon_choose_sel.png') : require('.././../../resources/images/newLive/icon_choose_white_unsel.png')}
                        />
                    </View>
                </View>
            </TouchableWithoutFeedback>
        );
    }

    _sureWeekDay() {
        let weekArrData = [];
        let weekTitArr = [];
        for (let i = 0; i < this.state.weekArr.length; i++) {
            let weekDay = this.state.weekArr[i];
            console.log('当前选中的weekDay--', weekDay);
            if (weekDay.isSelectedDay) {
                weekArrData.push(1);
                weekTitArr.push(i);
            } else {
                weekArrData.push(0);
            }
        }
        let compareWeek = [0, 0, 0, 0, 0, 0, 0];
        console.log('选中数据', weekArrData, weekTitArr);
        console.log('当前选中的selectWeek', this.state.selectWeek);
        let result = (weekArrData.length === compareWeek.length && weekArrData.every(a => compareWeek.some(b => a === b)) && compareWeek.every(_b => weekArrData.some(_a => _a === _b)));
        if (result) {
            console.log('未选中数据');
            let weekData = [
                {name: stringsTo("week_new_1"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_2"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_3"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_4"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_5"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_6"), isSelectedDay: false, isDisabledDay: false},
                {name: stringsTo("week_new_7"), isSelectedDay: false, isDisabledDay: false},
            ];
            this.setState({
                weekArr: weekData,
                selectWeek: compareWeek,
                showCustomDialog: false,
                selectIndex: 0,
                isShowCustomTit: false
            });
        } else {
            console.log('选中了数据');
            let weekTxt = '';
            if (weekTitArr.includes(0)) {
                weekTxt += " " + stringsTo("week_new_1");
            }
            if (weekTitArr.includes(1)) {
                weekTxt += " " + stringsTo("week_new_2");
            }
            if (weekTitArr.includes(2)) {
                weekTxt += " " + stringsTo("week_new_3");
            }
            if (weekTitArr.includes(3)) {
                weekTxt += " " + stringsTo("week_new_4");
            }
            if (weekTitArr.includes(4)) {
                weekTxt += " " + stringsTo("week_new_5");
            }
            if (weekTitArr.includes(5)) {
                weekTxt += " " + stringsTo("week_new_6");
            }
            if (weekTitArr.includes(6)) {
                weekTxt += " " + stringsTo("week_new_7");
            }
            console.log('最终选中星期数据---', weekTxt);
            console.log('最终选中的自定义数据---', this.state.weekArr);
            this.setState({
                weekArr: this.state.weekArr,
                selectWeek: weekArrData,
                weekTit: weekTxt,
                showCustomDialog: false,
                selectIndex: 3,
                isShowCustomTit: true
            });
        }
    }
}

const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: 80,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center"
    },
});

