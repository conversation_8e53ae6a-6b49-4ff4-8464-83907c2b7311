import React from 'react';

import {
    StyleSheet,
    View,
    FlatList,
    Image,
    Text,
    Platform,
    ScrollView,
    Modal,
    TouchableOpacity,
    Dimensions,
    BackHandler,
    DeviceEventEmitter
} from 'react-native';

import {imiThemeManager,Separator,showLoading, showToast} from '../../../../../imilab-design-ui';
import I18n from '../../../../../globalization/Localize'
import BaseDeviceComponent from "../../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {LetDevice, letDevice} from "../../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {LetIMIIotRequest} from "../../../../../imilab-rn-sdk";
import {stringsTo} from "../../../../../globalization/Localize";
import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import ImageButton from "../../../../../imi-rn-commonView/ImageButton/ImageButton"
import {isAndroid} from "../../../../../imilab-rn-sdk/utils/Utils";
import Sound from "react-native-sound";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMIPackage from "../../../../../imilab-rn-sdk/native/local-kit/IMIPackage";
import IMIDownload from "../../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import {IMINativeLifeCycleEvent} from "../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import getVoiceArr from "../../utils/VoiceAlarmConstants";
const EVENT_NAME = "IMIDownloadAudioPathScheduler --- ";

const screen_width = Dimensions.get('window').width;

let tempPlayPath = '';
let player = new Sound(tempPlayPath, '', (error) => {
    if (error) {
        console.log('资源加载失败', error);
        return;
    }
});

let lastSaveTime = 0;



/**
 * 提示音选择
 */
export  default  class  IntelligentVoiceSelect extends BaseDeviceComponent {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = {
            type:this.props.route.params.type,//从不同页面进入提示音选择页面
            voiceUrl:this.props.route.params.voiceUrl,
            voiceName:stringsTo('voice_for_warning'),
            // voiceUrl:'1-alarm',
            voiceSelectIndex:0,
            allVoiceArr: [],//所有音频数据 包括标准+自定义音频
            customVoiceList:[],//自定义铃声数组
        }
        this.voiceArr = getVoiceArr();
    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }


    componentDidMount() {
        //console.log('当前服务器地址--',IMIHost.serverCode);
        // this.getVoiceFile();
        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            this.handlePauseAudio();
            showLoading(false);

        });

        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            this.handlePauseAudio();
        });

        if (this.state.type == "areaVoice"){
            this.location = this.props.route.params.location;
            console.log('当前角度的位置',this.location);
        }

        this.getAllValue();

    }


    // 播放录音
    async handlePlayCustomAudio(audioPath) {
        player = new Sound(audioPath, '', (err) => {
            if(err){
                showToast(I18n.t('waitFailedTip'));
                return console.log(err)
            }else{
                player.setVolume(0.5);
                player.setCurrentTime(0);
                player.setNumberOfLoops(0); //只放一次
                player.play(success => {
                    if (success) {

                    } else {

                    }
                })
            }

        });
    }

    async handlePlayAudio(audioPath) {
        player = new Sound(audioPath, (err) => {
            if(err){
                showToast(I18n.t('waitFailedTip'));
                // console.log('加载音频失败')
                return console.log(err)
            }else{
                player.setVolume(0.5);
                player.setCurrentTime(0);
                player.setNumberOfLoops(0); //只放一次
                player.play(success => {
                    if (success) {

                    } else {

                    }
                })
            }

        })
    }

    async handlePauseAudio() {
        player.pause();
    }

    // 获取所有数据
    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data)=>{
            showLoading(false);
            let dataObject = JSON.parse(data);
            let stateProps = {};
            let tempAudioPath = "";
            console.log('获取所有数据--',dataObject);
            // if (this.state.type == "areaVoice"){
            //     if (dataObject.KeyAreaAttr){
            //         console.log('有keyAreaArr',dataObject.KeyAreaAttr);
            //         if (dataObject.KeyAreaAttr.value.length>0) {
            //             //有值
            //             console.log('重点区域有值');
            //             let areaArr = dataObject.KeyAreaAttr.value;
            //             if (areaArr.length>0){
            //                 for (let i = 0; i < areaArr.length; i++) {
            //                     let singleArea = areaArr[i];
            //                     console.log('当前单个重点区域值--',singleArea,);
            //                     if (singleArea.idx == this.location){
            //                         let soundInfo = JSON.parse(singleArea.sound_alarm) ;
            //                         console.log('声音---',soundInfo,);
            //                         stateProps.voiceUrl = soundInfo.name;
            //                         tempAudioPath = soundInfo.name;
            //                     }
            //                 }
            //             }
            //         }else {
            //             console.log('重点区域无值');
            //         }
            //     }else {
            //         console.log('没有keyAreaArr');
            //         stateProps.voiceName = stringsTo('voice_for_warning'),
            //         stateProps.voiceUrl = '1-alarm';
            //         stateProps.voiceSelect = 0;
            //     }
            // }
            // 自定义音频
            if (dataObject.SoundLightCustomAudioList &&
                dataObject.SoundLightCustomAudioList.value.length > 0) {
                // 一键警告自定义音频数组
                console.log('自定义音频数组');
                let tempTotalArr = [];
                tempTotalArr = tempTotalArr.concat(this.voiceArr);
                //自定义语音
                let customArr = dataObject.SoundLightCustomAudioList.value;
                let tempArr = [];
                stateProps.customVoiceList = customArr;
                console.log('当前自定义音频数组', customArr, customArr.length);
                for (let i = 0; i < customArr.length; i++) {
                    let tempStr = JSON.parse(customArr[i]);
                    console.log('tempStr---', tempStr, tempStr.url, tempStr.name);
                    let tempParams = {
                        name: tempStr.name,
                        url: tempStr.url,
                        index: tempStr.index,
                        time: tempStr.time,
                    }
                    tempArr.push(tempParams);
                }
                console.log('转换后的自定义音频数组', tempArr);
                if (tempArr.length > 1) {
                    tempArr.sort((a, b) => {
                        return a.index - b.index;
                    });// 排序一下
                }
                tempTotalArr = tempTotalArr.concat(tempArr);
                stateProps.allVoiceArr = tempTotalArr;
                console.log('所有自定义音频数据', stateProps.allVoiceArr);
                for (let voiceItem of tempTotalArr) {
                    let newAudioUrl = voiceItem.url;
                    // if (tempAudioPath == newAudioUrl) {
                    //     stateProps.voiceName = voiceItem.name;
                    //     stateProps.voiceSelectIndex  = voiceItem.index;
                    //     console.log('音频名称', voiceItem.name, stateProps.voiceName);
                    //     break;
                    // }
                    if (this.state.voiceUrl == newAudioUrl) {
                        stateProps.voiceName = voiceItem.name;
                        stateProps.voiceSelectIndex  = voiceItem.index;
                        console.log('音频名称', voiceItem.name, stateProps.voiceName);
                        break;
                    }
                }

            } else {
                // 未设置自定义音频
                console.log('未设置自定义音频数组');
                // //中国
                for (let voiceItem of this.voiceArr) {
                    let newAudioUrl = voiceItem.url;//提示音路径
                    // if (tempAudioPath == newAudioUrl) {
                    //     stateProps.voiceName = voiceItem.name;
                    //     stateProps.voiceSelectIndex  = voiceItem.index;
                    //     console.log('音频名称', tempAudioPath, voiceItem.name);
                    //     break;
                    // }
                    if (this.state.voiceUrl == newAudioUrl) {
                        stateProps.voiceName = voiceItem.name;
                        stateProps.voiceSelectIndex  = voiceItem.index;
                        console.log('音频名称', tempAudioPath, voiceItem.name);
                        break;
                    }
                }
                stateProps.allVoiceArr = this.voiceArr;
            }
            // 统一设置从设备端获取的值
            this.setState(stateProps);
        }).catch(error=>{
            console.log(JSON.stringify(error))
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
        });
    }

    // 进入添加页面
    _onPressAdd() {
        let addIndex = this.state.allVoiceArr.length;
        //自定义语音允许用户最多录制3条
        let totalLength = this.voiceArr.length+3;
        if (addIndex >= totalLength){
            showToast(I18n.t('voice_for_max_3'));
            return;
        }
        console.log('添加的第几个音频--',addIndex,this.state.type,this.state.customVoiceList);
        this.props.navigation.navigate('IntelligentVoiceEdit',{
            type:this.state.type,
            isAdd:true,
            index:addIndex,
            customVoiceList:this.state.customVoiceList,
            selectIndex:this.state.voiceSelectIndex,
            callback:((customizeList,timeWhenSave) => {
                console.log('当前customList',customizeList,timeWhenSave);
                let tempTotalArr = [];
                let tempArr = [];
                tempTotalArr = tempTotalArr.concat(this.voiceArr);
                for (let i = 0; i < customizeList.length; i++) {
                    let tempStr = JSON.parse(customizeList[i]);
                    console.log('tempStr---', tempStr, tempStr.url, tempStr.name);
                    let tempParams = {
                        name: tempStr.name,
                        url: tempStr.url,
                        index: tempStr.index,
                        time: tempStr.time,
                    }
                    tempArr.push(tempParams);
                }
                console.log('转换后的自定义音频数组', tempArr);
                if (tempArr.length > 1) {
                    tempArr.sort((a, b) => {
                        return a.index - b.index;
                    });// 排序一下
                }
                tempTotalArr = tempTotalArr.concat(tempArr);
                console.log('添加返回所有数据--',tempTotalArr);
                this.setState({allVoiceArr:tempTotalArr,customVoiceList:customizeList});
                lastSaveTime = timeWhenSave;
            })
        })
    }

    _getNewItemIndex(){
        let newIndex = 0;
        this.state.listDataSource.map(vo=>{
            if(newIndex == vo.index){
                newIndex ++;
            }
        });
        return newIndex;
    }


    _renderItem = ({item, index}) => {
        return (
            <TouchableOpacity
                onPress={this._itemPress.bind(this,item)}
            >
                <View style={{flexDirection:'column',height: 60,width:screen_width,display: "flex", alignItems: "center",backgroundColor: "white"}}>
                    <View
                        style={{flexDirection:'row',width:screen_width,height:60,backgroundColor:'white', alignItems: "center"}}>
                        <Image style={{height:25,width:25,marginLeft:14,resizeMode: 'contain',tintColor:this.state.voiceSelectIndex == item.index?null:'transparent'}}
                               source={require("../../../resources/images/newLive/icon_choose_sel.png")}/>
                        <Text numberOfLines={2} style={{flex:1,fontSize: 15,fontWeight:"500",color:"#333333",textAlign:'left',marginLeft:10}}>{item.name}</Text>

                        {(index > this.voiceArr.length-1) && !LetDevice.isShareUser ? <ImageButton
                            style={{
                                width: 30,
                                height: 30,
                                marginRight:5
                            }}
                            source={require('../../../resources/images/newLive/icon_voice_edit.png')}
                            accessibilityLabel={"edit_img"}
                            onPress={() => {
                                this._onPressEdit(item,index);
                            }}
                        /> : null}

                    </View>
                </View>
            </TouchableOpacity>
        );
    }

    _onPressEdit(item,currentIndex) {
        console.log('编辑选中item',item,currentIndex);
        this.props.navigation.navigate('IntelligentVoiceEdit',{
            type:this.state.type,
            isAdd:false,
            index:item.index,
            customVoiceList:this.state.customVoiceList,
            selectIndex:this.state.voiceSelectIndex,
            callback:((customizeList,timeWhenSave) => {
                console.log('返回自定义列表编辑',customizeList,timeWhenSave);
                let tempTotalArr = [];
                let tempArr = [];
                tempTotalArr = tempTotalArr.concat(this.voiceArr);
                console.log('系统数组',tempTotalArr);
                for (let i = 0; i < customizeList.length; i++) {
                    let tempStr = JSON.parse(customizeList[i]);
                    console.log('tempStr---', tempStr, tempStr.url, tempStr.name);
                    let tempParams = {
                        name: tempStr.name,
                        url: tempStr.url,
                        index: tempStr.index,
                        time: tempStr.time,
                    }
                    tempArr.push(tempParams);
                }
                console.log('转换后的自定义音频数组', tempArr);
                if (tempArr.length > 1) {
                    tempArr.sort((a, b) => {
                        return a.index - b.index;
                    });// 排序一下
                }
                tempTotalArr = tempTotalArr.concat(tempArr);
                console.log('编辑返回所有数据---',tempTotalArr);
                this.setState({allVoiceArr:tempTotalArr,customVoiceList:customizeList});
                lastSaveTime = timeWhenSave;
            })
        })
    }


    //点击铃声条目，播放铃声，并选中
    _itemPress = (item) => {
        console.log("item=", JSON.stringify(item));
        console.log('index---', item.index);
        // this.setState({voiceSelectIndex:item.index,voiceUrl:item.url,voiceName:item.name});

        this.handlePauseAudio();
        this.updateSelectValue(item);

        // if (item.index < 8) {
        //     this.updateSelectValue(item);
        // } else {
        //     let audioPath = IMIFile.storageBasePath + `/${this.state.typeStr}_download_audio_${this.getTimestampOfUrl(item.url)}_${item.index}.aac`;
        //     IMIFile.fileExists(audioPath).then((result) => {
        //         if (result) {
        //             this.handlePlayCustomAudio(audioPath);
        //             this.updateSelectValue(item);
        //         } else {
        //             //先下载，后播放，然后设置
        //             this.downAudioUrl(item);
        //         }
        //
        //     }).catch(() => {
        //
        //     });
        // }

    };

    getTimestampOfUrl(url){
        if(!url || url == ""){
            return new Date().getTime.toString;
        }
        // 'oss|a1o26TMbbCU/customize_audio/Zu23O3LAyChP0UFAzSzI000000/20220813/1660386674224_bsuabo.aac';
        return url.split("customize_audio/")[1].split("/")[2].split("_")[0];
    }

    downAudioUrl(item) {
        const params = {
            Path: 'api/app_file/device/download_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: item.url,
                timer: 600000,
            }
        };
        console.log('获取下载文件参数', params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            let tempStr = JSON.parse(data);
            console.log('获取下载文件数据--', tempStr); //TODO测试每个服务器下，path前拼接的前缀
            this.getDownAudioPath(tempStr,item);
        }).catch((error) => {
            console.log('下载文件失败--', error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    getDownAudioPath(dataStr,item) {
        console.log('下载data', dataStr,IMIFile.storageBasePath);
        let fileName = `${this.state.typeStr}_download_audio_${this.getTimestampOfUrl(item.url)}_${item.index}.aac`;
        IMIDownload.downloadToPath(EVENT_NAME, dataStr.downloadUrl, IMIFile.storageBasePath, fileName);
        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            if (event.status === IMIDownload.STATUS_START) {
                console.log('开始下载');
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                console.log('正在下载');
            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                // console.log(EVENT_NAME + " download error mataInfo : " + mataInfo)
                showLoading(false);
                showToast(I18n.t('waitFailedTip'));
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                const path = `${event.downloadPath}/${fileName}`;
                console.log('下载成功');
                console.log('下载成功后文件路径-常量---', path);
                showLoading(false);
                this.handlePlayCustomAudio(path);
                this.updateSelectIndex(item.index);

                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });
    }

    /*_itemPress = (item) => {
        console.log("item=",JSON.stringify(item));
        console.log('index---',item.index,item.itemIndex);

        this.handlePauseAudio();
        item.index < 3 ? this.handlePlayAudio(DEFAULT_BELL_PATH_ARRAY[item.index]) : null;
        if(item.index>=3 && IMIPackage.minApiLevel>=10007){

        }
        this.setState({voiceSelectIndex:item.index});
        this.updateSelectIndex(item.index);
    };*/


    updateSelectValue(tempItem){
        console.log('当前选中音频Item',tempItem)
        showLoading(stringsTo('commWaitText'), true);
       let params = {ListenAudio: tempItem.url};
       console.log('音频试听params',params);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            const listenAudioTime = setTimeout(() => {
                //时间到了，开始执行
                this.setState({voiceSelectIndex:tempItem.index,voiceUrl:tempItem.url,voiceName:tempItem.name});
                showToast(I18n.t('settings_set_success'));
                showLoading(false);
                //清除
                clearTimeout(listenAudioTime);
            }, 1000)

        }).catch((error) => {
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    render() {
        global.navigation = this.props.navigation;
        return (<View style={styles.container}>
            <NavigationBar
                title={stringsTo("tip_voice_selected")}
                left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
                right={
                    LetDevice.isShareUser ? [] : [{
                        key: NavigationBar.ICON.CUSTOM,
                        n_source:require('../../../resources/images/newLive/icon_voice_add.png'),
                        onPress: () => {
                            this._onPressAdd();
                        },
                        accessibilityLabel: 'choose_voice_back'
                    }]}
            />
            <Separator/>
            <View style={{height:14,backgroundColor: "#F1F1F1"}} />
            <FlatList
                data={this.state.allVoiceArr}
                renderItem={this._renderItem}
                keyExtractor={(item, index) => `key_${index}`}
                extraData={this.state}
                onEndReachedThreshold={0.1}
                // ItemSeparatorComponent={() => <View
                //     style={{height: 0.5, width: '100%', backgroundColor: '#CCCCCC'}}/>}
            />

        </View>);
    }





    componentWillUnmount() {
        this.handlePauseAudio();
        player.release();
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress',this.onBackHandler);
        }
        this._subscribe_blur && this._subscribe_blur();
        this._enterBackground && this._enterBackground.remove();
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigation.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };
    // 返回上一页
    _onPressBack = () => {
        if (this.props.route.params.callback) {
            // let selectItemArray = this.state.allVoiceArr.filter((vo) => {
            //     return vo.index == this.state.voiceSelectIndex;
            // });
            // this.props.route.params.callback(selectItemArray.length == 0 ?
            //     stringsTo("customize_reply") : selectItemArray[0].name, lastSaveTime);
            this.props.route.params.callback(this.state.voiceUrl,this.state.voiceName);
        }
        this.props.navigation.pop();
    };
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#F1F1F1",
    }
});
