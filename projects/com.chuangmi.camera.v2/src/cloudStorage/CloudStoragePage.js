import React, {Component} from "react";
import {AlarmType, IMIGotoPage, IMIPackage, LetDevice} from "../../../../imilab-rn-sdk";
import CloudStoragePagePlayerComponent from "./CloudStoragePagePlayerComponent";
import PropTypes from "prop-types";
import moment from "moment";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import {isIos} from "../../../../imilab-rn-sdk/utils/Utils";
import {StatusBar} from "react-native";
import {showToast} from "../../../../imilab-design-ui";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import NetInfo from "@react-native-community/netinfo";

import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import {stringsTo} from "../../../../globalization/Localize";
import Orientation from "react-native-orientation";
import HomePageLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
/**
 * CloudStoragePage
 * @author: yanmin
 * @date: 2020/12/28
 */
export default class CloudStoragePage extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            isFullScreen: false,
            mute: true,
            recording: false,
            recordDuration: 0,
            showFullScreenTools: false,
            showAlarmView:false,
            netConnected:true
        };
        this.isForegroundPage = true;
        this.playStatus=false;
        this.camera=false;//防止从RN进入后会出现暂停
        // this.leaveCloudFull = false;
    }
    static propTypes = {
        pageStartProp:PropTypes.number,
        eventAlarmTypeProp:PropTypes.number,
        item:PropTypes.string,
    };

    static defaultProps = {
        pageStartProp:0,
        eventAlarmTypeProp:AlarmType.ALL,
        item:"0",
    };

    componentDidMount() {

        this._addListener();
        this.getHouseKeep();
    }

    _addListener(){
        this.unsubscribe = NetInfo.addEventListener(state => {
            this.setState({
                netConnected:state.isConnected,
            });

            console.log("Is connected?", state.isConnected);
        });

        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            console.log("CloudStoragePage _subscribe_focus"+this.camera)
            this.isForegroundPage = true;
            if (this.camera){
                this.IMIVideoView && this.IMIVideoView.resume();
                this.camera=false;
            }
            this.getHouseKeep();
            console.log('进入当前云存页面全屏状态',this.cloudStorgeComponent.getFullscreen());
            // if (this.leaveCloudFull){
                this.cloudStorgeComponent&&this.cloudStorgeComponent.quitFullScreen();
            NavigationBar.setBarStyle('light-content');

                // setTimeout(()=>{
                //     console.log('延时1S退出全屏');
                //     this.cloudStorgeComponent&&this.cloudStorgeComponent.quitFullScreen();
                // },1500);

            // }
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            console.log("CloudStoragePage _subscribe_blur"+this.camera)
            this.isForegroundPage = false;
            if (this.camera){
                this.IMIVideoView && this.IMIVideoView.pause();
            }else {
                this.IMIVideoView && this.IMIVideoView.stop();
            }
            // this.leaveCloudFull = this.cloudStorgeComponent.getFullscreen();
            console.log('离开当前云存页面全屏状态',this.cloudStorgeComponent.getFullscreen());
        });
        this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(()=>{
            // console.log("_onPauseListener")
            if (this.playStatus) {
                this.IMIVideoView && this.IMIVideoView.pause();
            }
        });

        this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(()=>{
            // console.log("_onResumeListener"+this.isFcous)
            NavigationBar.setBarStyle('light-content'); // 修改从云存购买界面返回状态栏显示黑色字体问题
            if (this.playStatus){
                this.IMIVideoView && this.IMIVideoView.resume();
            }

        });
    }
    /**
     * 获取看家助手状态
     * @returns {JSX.Element}
     */
    getHouseKeep(){
        LetDevice.getPropertyCloud("AlarmSwitch").then(data => {
            // console.log("data:",data)
            //看家开关已打开的情况下，就不在展示设置看家弹框
            if (data!="1"){
                //看家开关未打开
                this.setState({
                    showAlarmView:true
                })

            }else {
                this.setState({
                    showAlarmView:false
                })
            }
        }).catch(error => {

        });
    }
    componentWillUnmount() {
        this._subscribe_focus && this._subscribe_focus();
        this.devicePropertyListener&&this.devicePropertyListener.remove();
        this.deviceInfoListener&&this.deviceInfoListener.remove();
        this._subscribe_blur && this._subscribe_blur();
        this.IMIVideoView && this.IMIVideoView.stop();
        this.unsubscribe && this.unsubscribe();
        this._onPauseListener&&this._onPauseListener.remove();
        this._onResumeListener&&this._onResumeListener.remove();
    }
    _exitFullScreen = () => {
        console.log('退出全屏');
    };

    render() {
       // console.log("this.state.pageStart"+this.props.pageStartProp)
        let eventAlarmType=AlarmType.ALL
        let queryTime=new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 0, 0, 0);
        let eventId="0";
        let pageStart=0;
        let time="0";
        if (this.props.route.params){
            let {pictureTime,pictureTimeUTC,pictureId,eventAlarmTypeProp,pageStartProp} = this.props.route.params.item;
            eventAlarmType=eventAlarmTypeProp;
           // console.log("jeff queryTime start:",new Date(moment(pictureTime).format('yyyy-MM-DD HH:mm:ss'))+"eventAlarmTypeProp:"+pageStart)
            queryTime=new Date(new Date(pictureTimeUTC).setHours(0, 0, 0, 0));
            eventId=pictureId;
            pageStart=pageStartProp;
            time=pictureTimeUTC;
        }
        // console.log("jeff queryTime:",queryTime+"eventAlarmTypeProp:"+pageStart,eventId);

        return (
            <CloudStoragePagePlayerComponent
                {...this.props}
                ref={component => (this.cloudStorgeComponent = component)}
                videoRef={ref => this.IMIVideoView = ref}
                // navBar={(bps, isFullScreen) => this._renderNavigationBar(bps, isFullScreen)}
                // navBarRight={[
                //
                // ]}
                onHlsPlayerStatusChange={(status)=>this._onHlsPlayerStatusChangeListener(status)}
                pageStartProp={pageStart}
                eventAlarmTypeProp={eventAlarmType}
                eventIdProp={eventId}
                queryTime={queryTime}
                pictureTime={time}
                showAlarmView={this.state.showAlarmView}
                fileNameStatusChangeListener={(status)=>this._fileNameStatusChangeListener(status)}
                netConnected={this.state.netConnected}
                >
            </CloudStoragePagePlayerComponent>
        );
    }

      _fileNameStatusChangeListener(state){
            if (state==1){
                this.playStatus=true;
            }else {
                if (state==3){
                    this.camera=true;
                }
                this.playStatus=false;
            }
      }

    _onHlsPlayerStatusChangeListener(status){
        console.log('_onHlsPlayerStatusChangeListener---',status);
        if (status == CloudStoragePagePlayerComponent.VOD_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        }else if(this.state.currentStatus==CloudStoragePagePlayerComponent.VOD_PLAYER_STATUS.ERROR
            &&status == CloudStoragePagePlayerComponent.VOD_PLAYER_STATUS.PAUSE){
            //
            return;
        }
    }


}
