import React from 'react';

import { View, Text, Image, ImageBackground, TouchableWithoutFeedback, Animated, Modal} from "react-native";

import NavigationBar from "../../../imi-rn-commonView/NavigationBar/NavigationBar";
import IMIGotoPage from "../../../imilab-rn-sdk/native/local-kit/IMIGotoPage";
import {LetDevice, LetIProperties} from "../../../imilab-rn-sdk";
import ImageButton from "../../../imi-rn-commonView/ImageButton/ImageButton";
import SeekBar from './CommonView/SeekBar';
import {stringsTo} from "../../../globalization/Localize";
import {IMINativeLifeCycleEvent} from "../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";

let animation = null; //窗帘动画执行逻辑
let curtainRuntime = 2600;//窗帘从0到100的运行时间

export default class WindowCurtains extends React.Component {

    constructor(props) {
        super(props);

        this.state = {
            curtainConrtol: 1, // 0 - 关闭窗帘 1 - 停止窗帘 2 - 打开窗帘
            motorDirection: 0, // 0 - 正转  1 - 反转
            curtainPosition:0, // 窗帘打开位置
            layoutType:0, // 窗帘样式 0 - 双开 1 - 左开 2 - 右开
            showModal:false,

            widthAnim1: new Animated.Value(1),
            widthAnim2: new Animated.Value(1),
            widthAnim3: new Animated.Value(1),
            widthAnim4: new Animated.Value(1),
            widthAnim5: new Animated.Value(1),

            widthAnim6: new Animated.Value(1),
            widthAnim7: new Animated.Value(1),
            widthAnim8: new Animated.Value(1),
            widthAnim9: new Animated.Value(1),
            widthAnim10: new Animated.Value(1),
        }

        this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(()=>{
            //app页面回到插件
            this.setState({});
        });
    }

    componentDidMount() {
        console.log("componentDidMount");
        this.mPropertyChangeListener = LetIProperties.addPropertyChangeListener((event) => {
            console.log('devicePropertyListener event-----------------', event);

            let data = typeof (event) == 'object' ? event : JSON.parse(event);

            console.log('devicePropertyListener data-----------------', data);

            let curtainConrtol = data.curtainConrtol !== undefined ? parseInt(data.curtainConrtol) : this.state.curtainConrtol;
            let motorDirection = data.motorDirection !== undefined ? parseInt(data.motorDirection) : this.state.motorDirection;
            let curtainPosition = data.curtainPosition !== undefined ? parseInt(data.curtainPosition) : this.state.curtainPosition;

            console.log('devicePropertyListener curtainConrtol=' + curtainConrtol + ',motorDirection=' + motorDirection + ',curtainPosition=' + curtainPosition);

            this.setState({
                curtainConrtol: curtainConrtol,
                motorDirection: motorDirection,
                //curtainPosition: curtainPosition,
            });

            if (data.iotId == LetDevice.deviceID) {

            }
        });
        this._getPropertyCloud();
    }

    componentWillUnmount() {
        console.log("componentWillUnmount");

        this.mPropertyChangeListener && this.mPropertyChangeListener.remove();
        this._onResumeListener && this._onResumeListener.remove();
    }

    render() {

        return (
            <ImageBackground style={{flex: 1}} source={require('../res/images/bg.png')}>

                {this._renderTitleBar()}

                <View style={{
                    flex: 1,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginVertical: 50,
                    marginHorizontal: 30
                }}>
                    <View style={{alignItems: 'center',justifyContent: 'center',width:'100%',height:'50%'}}>
                            <Image style={{width: 261, height: 5}} source={require("../res/images/pic_1_2x.png")}/>

                            {this._renderCurtainAnimationLayout()}

                    </View>

                    <View style={{
                        width: "100%",
                        height: 50,
                        backgroundColor: "transparent",
                        borderRadius: 10,
                        marginBottom: 10,
                        marginTop: 10,
                        paddingLeft: 10,
                        paddingRight: 10,
                        justifyContent: 'center'}}>

                        <SeekBar style={{flex: 1, padding: 10, backgroundColor: 'transparent'}}
                                 min={0}
                                 max={100}
                                 progress={this.state.curtainPosition}
                                 progressHeight={3}
                                 progressBackgroundColor='#1E5BA9'
                                 progressColor='#EEEEEEFF'
                                 thumbSize={16}
                                 thumbColor='#1E5BA9'
                                 thumbColorPressed='#1E5BA9'
                                 onStartTouch={() => {
                                     console.log('onStartTouch')
                                 }}
                                 onProgressChanged={(progress) => {
                                     console.log('onProgressChanged progress='+progress);
                                 }}
                                 onStopTouch={(progress) => {
                                     console.log('onStopTouch progress='+progress);
                                     this._setCurtainPosition(parseInt(progress));
                                 }}
                        />

                    </View>

                    <View style={{flexDirection:'row',alignItems: 'center',justifyContent: 'center',width:'100%',height:100}}>
                        <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                            <ImageButton
                                style={{width: 60, height: 60}}
                                source={this.state.curtainConrtol === 2 ? require('../res/images/window_curtains_open_on.png') : require('../res/images/window_curtains_open.png')}
                                onPress={()=>{
                                    this._setCurtainConrtol(2);
                                }}
                            />
                            <Text style={{marginTop: 15, color: "#FFFFFF", fontSize: 12}}>{stringsTo('window_curtains_open')}</Text>
                        </View>

                        <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                            <ImageButton
                                style={{width: 60, height: 60}}
                                source={this.state.curtainConrtol === 1 ? require('../res/images/window_curtains_stop_on.png') : require('../res/images/window_curtains_stop.png')}
                                onPress={()=>{
                                    this._setCurtainConrtol(1);
                                }}
                            />
                            <Text style={{marginTop: 15, color: "#FFFFFF", fontSize: 12}}>{stringsTo('window_curtains_stop')}</Text>
                        </View>


                        <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                            <ImageButton
                                style={{width: 60, height: 60}}
                                source={this.state.curtainConrtol === 0 ? require('../res/images/window_curtains_close_on.png') : require('../res/images/window_curtains_close.png')}
                                onPress={()=>{
                                    this._setCurtainConrtol(0);
                                }}
                            />
                            <Text style={{marginTop: 15, color: "#FFFFFF", fontSize: 12}}>{stringsTo('window_curtains_close')}</Text>
                        </View>
                    </View>

                    <View style={{flexDirection:'row',marginTop:10,alignItems: 'center',justifyContent: 'center',width:'100%',height:80}}>
                        <TouchableWithoutFeedback
                            onPress={() => {
                                this._setMotorDirection(0);
                            }}
                        >
                            <View style={{
                                zIndex: this.state.motorDirection === 0 ? 2 : 1,
                                width: '50%',
                                backgroundColor: this.state.motorDirection === 0 ? 'rgba(210, 210, 210, 1)' : 'rgba(255, 255, 255, 0.2)',
                                borderRadius: 21,
                            }}>
                            <Text style={{
                                lineHeight: 40,
                                height: 40,
                                fontSize: 15,
                                color: '#FFFFFF',
                                textAlign: 'center',
                                textAlignVertical: 'center',
                            }}>{stringsTo('motor_forward')}</Text>
                            </View>
                        </TouchableWithoutFeedback>

                        <TouchableWithoutFeedback
                            onPress={() => {
                                this._setMotorDirection(1);
                            }}
                        >
                            <View style={{
                                zIndex: this.state.motorDirection === 1 ? 2 : 1,
                                width: '50%',
                                backgroundColor: this.state.motorDirection === 1 ? 'rgba(210, 210, 210, 1)' : 'rgba(255, 255, 255, 0.2)',
                                marginLeft: -35,
                                borderRadius: 21,
                            }}>
                            <Text style={{
                                height: 40,
                                lineHeight: 40,
                                fontSize: 15,
                                color: '#FFFFFF',
                                textAlign: 'center',
                                textAlignVertical: 'center',
                            }}>{stringsTo('motor_reverse')}</Text>
                           </View>
                        </TouchableWithoutFeedback>
                    </View>
                </View>

                {this._renderMoreLayout()}
            </ImageBackground>
        )
    }



    _renderTitleBar() {
        return (
            <NavigationBar type={NavigationBar.TYPE.DARK}
                           backgroundColor={"transparent"}
                           title={LetDevice.devNickName}
                           left={[{
                               key: NavigationBar.ICON.BACK, onPress: () => {
                                   IMIGotoPage.exit();
                               }
                           }]}
                           right={[{
                               key: NavigationBar.ICON.CUSTOM,
                               n_source:require("../res/images/window_curtains_layout.png"),
                               onPress: () => {
                                   this.setState({showModal: true});
                               }
                           },{
                               key: NavigationBar.ICON.SETTING, onPress: () => {
                                   let {commSettingConfig = null} = this.props.route.params ? this.props.route.params : {};
                                   let settingConfig = commSettingConfig ? commSettingConfig : {};
                                   IMIGotoPage.starNativeCommSettingPage(LetDevice.deviceID, settingConfig);
                               }
                           }]}
            />
        )
    }

    _getPropertyCloud() {

        console.log("_getPropertyCloud");

        LetDevice.updateAllPropertyCloud().then((data) => {

            console.log('updateAllPropertyCloud:', JSON.parse(data));

            let dataPackage = JSON.parse(data);

            let curtainConrtol = dataPackage.curtainConrtol ? dataPackage.curtainConrtol.value : this.state.curtainConrtol;
            let motorDirection = dataPackage.motorDirection ? dataPackage.motorDirection.value : this.state.motorDirection;
            let curtainPosition = dataPackage.curtainPosition ? dataPackage.curtainPosition.value : this.state.curtainPosition;
            let layoutType = dataPackage.layoutType ? dataPackage.layoutType.value : this.state.layoutType;

            console.log('devicePropertyListener curtainConrtol=' + curtainConrtol + ',motorDirection=' + motorDirection
                + ',curtainPosition=' + curtainPosition + ',layoutType=' + layoutType);

            this.setState({
                curtainConrtol: curtainConrtol,
                motorDirection: motorDirection,
                curtainPosition: curtainPosition,
                layoutType:layoutType,
            });

            this.showCurtainRatio(curtainPosition);
        }).catch(error => {
            console.log("error--------", error);
        });

    }

    _setCurtainConrtol(value) {
        let params = {
            curtainConrtol: value,
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            this.setState({curtainConrtol:value})

            if(value===1){
                animation.stop(); //停止指令发送成功，先停止动画

                this.showCurtainRatio(this.getCurrentRationByCurtain());
            }else if(value===0){
                this.startAnimationWithRatioV2(0);
            }else if(value===2){
                this.startAnimationWithRatioV2(100);
            }

            console.log("设置成功-------", data);
        }).catch((err) => {
            console.log("设置失败-------", err);
        });
    }


    _setMotorDirection(value) {
        let params = {
            motorDirection: value,
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            this.setState({motorDirection:value})
            console.log("设置成功-------", data);
        }).catch((err) => {
            console.log("设置失败-------", err);
        });
    }

    _setCurtainPosition(value) {

        let params = {
            curtainPosition: value,
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            //this.setState({curtainPosition:value})
            this.showCurtainRatio(value);
            console.log("设置成功-------", data);
        }).catch((err) => {
            console.log("设置失败-------", err);
        });
    }

    _setLayoutType(value) {
        let params = {
            layoutType: value,
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            this.setState({layoutType:value})
            console.log("设置成功-------", data);
        }).catch((err) => {
            console.log("设置失败-------", err);
        });
    }


    /*根据当前窗帘的展开状态计算出当前的窗帘开合百分比*/
    getCurrentRationByCurtain() {
        let currentRatio = 0;
        let leftWidthArray = [this.state.widthAnim1._value, this.state.widthAnim2._value, this.state.widthAnim3._value, this.state.widthAnim4._value, this.state.widthAnim5._value]; //左边窗帘从左至右每个窗帘条
        let rightWidthArray = [this.state.widthAnim6._value, this.state.widthAnim7._value, this.state.widthAnim8._value, this.state.widthAnim9._value, this.state.widthAnim10._value];//右边窗帘从右至左每个窗帘条
        let motionPartNum = 5;
        let motionPartMaxWidth = 12;
        let motionPartRatio = 20;
        if (this.state.layoutType != 0) {
            motionPartNum = 10;
            motionPartMaxWidth = 13;
            motionPartRatio = 10;
            leftWidthArray = leftWidthArray.concat(rightWidthArray);

            if (this.state.layoutType === 2) {
                leftWidthArray.reverse();
            }
        }

        for (let i = 0; i < motionPartNum; i++) {
            if (leftWidthArray[i] == 1) {
                currentRatio = currentRatio + 0;
            } else if (leftWidthArray[i] == motionPartMaxWidth + 1) {
                currentRatio = currentRatio + motionPartRatio;
            } else {
                currentRatio = currentRatio + parseInt(leftWidthArray[i] / (motionPartMaxWidth + 1) * motionPartRatio);
            }

        }

        return currentRatio;
    }


    /*进入页面，根据获得的ratio来显示窗帘的当前展开状态*/
    showCurtainRatio(curtainPosition) {
        this.setState({curtainPosition:curtainPosition});

        //curtainPosition= 100 -curtainPosition;

        console.log("dengying showCurtainRatio1-------------------------curtainPosition===========",curtainPosition);
        let leftWidthArray = ["widthAnim1", "widthAnim2", "widthAnim3", "widthAnim4", "widthAnim5"]; //左边窗帘从左至右每个窗帘条
        let rightWidthArray = ["widthAnim6", "widthAnim7", "widthAnim8", "widthAnim9", "widthAnim10"];//右边窗帘从右至左每个窗帘条
        let motionPartNum = 5;
        let motionPartMaxWidth = 12;
        let motionPartRatio = 20;
        if(this.state.layoutType!=0){ //不是左开和右开的样式
            motionPartNum = 10;
            motionPartMaxWidth = 13;
            motionPartRatio =10;
            leftWidthArray = leftWidthArray.concat(rightWidthArray);
            if(this.state.layoutType===2){
                leftWidthArray.reverse();
            }
        }

        if (curtainPosition === 0) {//关闭状态，动画铺满

            console.log("dengying showCurtainRatio2-------------------------curtainPosition===========",curtainPosition);

            for (let i = 0; i < motionPartNum; i++) {
                let stateValues = { [leftWidthArray[i]]: new Animated.Value(motionPartMaxWidth+1)};
                if(this.state.layoutType===0){
                    stateValues[rightWidthArray[i]] = new Animated.Value(motionPartMaxWidth+1);
                }

                console.log("dengying showCurtainRatio-------------------------stateValues===========",stateValues);

                this.setState(stateValues);
            }
        } else if (curtainPosition === 100) {//打开状态
            for (let i = 0; i < motionPartNum; i++) {
                console.log("循环赋值----",motionPartMaxWidth);
                let stateValues = { [leftWidthArray[i]]: new Animated.Value(1)};
                if(this.state.layoutType===0){
                    stateValues[rightWidthArray[i]] = new Animated.Value(1);
                }

                console.log("dengying showCurtainRatio3-------------------------stateValues===========",stateValues);

                this.setState(stateValues);
            }
        } else {
            let expandNum = parseInt(curtainPosition / motionPartRatio); //全部展开的窗帘条数
            let stateValues = {};
            for (let i = 0; i < expandNum; i++) {
                stateValues[leftWidthArray[i]]= new Animated.Value(motionPartMaxWidth+1);
                if(this.state.layoutType===0){
                    stateValues[rightWidthArray[i]] = new Animated.Value(motionPartMaxWidth+1);
                }

            }
            this.setState(stateValues);

            let width = 1 + curtainPosition % motionPartRatio / motionPartRatio * motionPartMaxWidth; //最后一条展开窗帘的宽度
            console.log("最后一个布帘的宽度--------------------------",width);
            let Values = {[leftWidthArray[expandNum]]: new Animated.Value(width)};
            if (this.state.layoutType === 0) {
                Values[rightWidthArray[expandNum]] = new Animated.Value(width);
            }
            this.setState(Values);

            //还得确保剩下的都没展开
            console.log("元旦加班----",expandNum+1,motionPartNum);

            for(let i = expandNum+1;i<motionPartNum;i++){
                console.log("元旦加班，循环",i);
                let stateValues = { [leftWidthArray[i]]: new Animated.Value(1)};
                if(this.state.layoutType===0){
                    stateValues[rightWidthArray[i]] = new Animated.Value(1);
                }
                this.setState(stateValues);
            }

        }
    }

    /*新版动画方案-----拖动进度条后，根据当前的开合程度来显示窗帘动画*/
    startAnimationWithRatioV2(curtainPosition) {

        console.log("dengying ------------------startAnimationWithRatioV2-------------------------",this.state.curtainPosition+"----->"+curtainPosition);

        let currentRatio = this.state.curtainPosition;

        let motionPartMaxWidth = 12; //每一个可动窗帘部分展开时的最大宽度
        let motionPartRatio = 20; //每一个可动窗帘部分所占的百分比
        let singleTime = curtainRuntime/5; //运动完一格窗帘所需要的的时间
        if (curtainPosition > 100 || curtainPosition < 0||currentRatio==curtainPosition) {
            return;
        }

        this.setState({
            isAnimating:true
        });

        if(this.state.layoutType!=0){
            motionPartMaxWidth = 13;
            motionPartRatio =10;
            singleTime = curtainRuntime/10;
        }

        if (curtainPosition < this.state.curtainPosition) {
            console.log("dengying ------------------关闭窗帘-------------------------");

            let leftCurtainAnimationParams = [];
            let leftCurtainWidthArray = [this.state.widthAnim1, this.state.widthAnim2, this.state.widthAnim3,
                this.state.widthAnim4, this.state.widthAnim5];

            let rightCurtainAnimationParams = [];
            let rightCurtainWidthArray = [this.state.widthAnim6, this.state.widthAnim7, this.state.widthAnim8,
                this.state.widthAnim9, this.state.widthAnim10];

            if(this.state.layoutType!=0){
                leftCurtainWidthArray = leftCurtainWidthArray.concat(rightCurtainWidthArray);
                if(this.state.layoutType===2){
                    leftCurtainWidthArray.reverse();
                }
            }

            //let startNum = parseInt((100-currentRatio)/ motionPartRatio); //动画开始的窗条
            //let expandNum = 5;//parseInt((100-currentRatio) / motionPartRatio); //全部展开的窗帘条数

            let startNum = parseInt(this.state.curtainPosition / motionPartRatio) - 1; //动画开始的窗条  5-1 = 4   widthAnim5
            let expandNum = parseInt(curtainPosition / motionPartRatio) - 1;

            console.log("dengying ---------------------startNum="+startNum+",expandNum="+expandNum);

            console.log("dengying ---------------------leftCurtainWidthArray=",leftCurtainWidthArray);

            //将完全展开的窗条加入动画序列
            let firstExpandTime = singleTime;
            for (let index = startNum; index > expandNum; index--) {

                if(index==startNum){ //第一个完全展开的窗帘，时间可能不是singleTime 需要单独处理
                    firstExpandTime = (motionPartRatio-currentRatio%motionPartRatio)/motionPartRatio*singleTime;
                }
                if(leftCurtainWidthArray[index]._value!=motionPartMaxWidth+1) {
                    leftCurtainAnimationParams.push(
                        Animated.timing(
                            leftCurtainWidthArray[index],////初始值
                            {toValue: motionPartMaxWidth + 1,duration:index===startNum?firstExpandTime:singleTime, useNativeDriver: false} //结束值  duration:950
                        )
                    );
                }
                if(this.state.layoutType===0&&rightCurtainWidthArray[index]._value!=motionPartMaxWidth+1){
                    rightCurtainAnimationParams.push(
                        Animated.timing(
                            rightCurtainWidthArray[index],////初始值
                            {toValue: motionPartMaxWidth+1,duration:index===startNum?firstExpandTime:singleTime, useNativeDriver: false} //结束值
                        )
                    );
                }
            }
            //将最后一个未完全展开的窗条加入动画序列
            if (curtainPosition % motionPartRatio != 0) {
                let width = 1 + curtainPosition % motionPartRatio / motionPartRatio * motionPartMaxWidth; //最后一条展开窗帘的宽度
                let time = curtainPosition % motionPartRatio/motionPartRatio * singleTime;
                leftCurtainAnimationParams.push(
                    Animated.timing(
                        leftCurtainWidthArray[expandNum],////初始值
                        {toValue: width,duration:time, useNativeDriver: false} //结束值
                    )
                );
                if(this.state.layoutType===0){
                    rightCurtainAnimationParams.push(
                        Animated.timing(
                            rightCurtainWidthArray[expandNum],////初始值
                            {toValue: width,duration:time, useNativeDriver: false} //结束值
                        )
                    );
                }
            }
            this.setState({
                // motionRatio: ratio,
                //curtainPosition:ratio
            });
            /*Animated.sequence(leftCurtainAnimationParams).start();
            Animated.sequence(rightCurtainAnimationParams).start();*/
            animation = Animated.sequence(leftCurtainAnimationParams);
            if(this.state.layoutType===0){ //如果是双开，那就左右两边窗帘一块动
                animation = Animated.parallel([Animated.sequence(leftCurtainAnimationParams), Animated.sequence(rightCurtainAnimationParams)]);
            }
            animation.start(() => {
                this.setState({
                    isShrinking: false,
                    isExpanding: false,
                    curtainPosition: curtainPosition,
                    isAnimating: false
                });
            });

        } else {//打开状态
            console.log("dengying ------------------打开窗帘-------------------------");
            let leftCurtainAnimationParams = [];
            let leftCurtainWidthArray = [this.state.widthAnim1, this.state.widthAnim2, this.state.widthAnim3,
                this.state.widthAnim4, this.state.widthAnim5];
            let rightCurtainAnimationParams = [];
            let rightCurtainWidthArray = [this.state.widthAnim6, this.state.widthAnim7, this.state.widthAnim8,
                this.state.widthAnim9, this.state.widthAnim10];
            if(this.state.layoutType!=0){
                leftCurtainWidthArray = leftCurtainWidthArray.concat(rightCurtainWidthArray);
                if(this.state.layoutType===2){
                    leftCurtainWidthArray.reverse();
                }
            }
            //let startNum = parseInt(this.state.curtainPosition / motionPartRatio); //动画开始的窗条  5-1 = 4   widthAnim5
            //let shrinkToNum = parseInt(curtainPosition / motionPartRatio); //全部展开的窗帘条数 2 -1 =  1

            let startNum = parseInt(currentRatio/ motionPartRatio); //动画开始的窗条
            let shrinkToNum = parseInt(curtainPosition / motionPartRatio); //全部展开的窗帘条数

            if (this.state.curtainPosition % motionPartRatio != 0) { //第一个开始收缩的不是完整的一条
                startNum = startNum + 1;
            }

            if (curtainPosition % motionPartRatio != 0) {  //最后一个开始收缩的不是完整的一条
                shrinkToNum = shrinkToNum + 1;
            }

            console.log("dengying ---------------------startNum="+startNum+",shrinkToNum="+shrinkToNum);

            console.log("dengying ---------------------leftCurtainWidthArray=",leftCurtainWidthArray);

            //处理完整收缩的窗条
            let firstShrinkTime = singleTime;
            for (let index = startNum; index < shrinkToNum; index++) {
                if(index==startNum&&currentRatio % motionPartRatio != 0){ //第一个开始收缩的窗帘时间可能不是singleTime，需单独处理
                    firstShrinkTime = currentRatio%motionPartRatio/motionPartRatio*singleTime;
                }
                if(leftCurtainWidthArray[index]._value!=1){
                    leftCurtainAnimationParams.push(
                        Animated.timing(
                            leftCurtainWidthArray[index],//初始值
                            {toValue: 1,duration:index==startNum?firstShrinkTime:singleTime, useNativeDriver: false} //结束值
                        )
                    );
                }

                if(this.state.layoutType===0&&rightCurtainWidthArray[index]._value!=1){
                    rightCurtainAnimationParams.push(
                        Animated.timing(
                            rightCurtainWidthArray[index],////初始值
                            {toValue: 1,duration:index==startNum?firstShrinkTime:singleTime, useNativeDriver: false} //结束值
                        )
                    );
                }
            }
            //处理最后一个不完全收缩窗条
            if (curtainPosition % motionPartRatio != 0) {
                let width = (1 + curtainPosition % motionPartRatio / motionPartRatio * motionPartMaxWidth); //最后一条展开窗帘的宽度
                let time = (motionPartRatio-curtainPosition % motionPartRatio)/motionPartRatio * singleTime;
                if( leftCurtainWidthArray[shrinkToNum]._value!=width){
                    leftCurtainAnimationParams.push(
                        Animated.timing(
                            leftCurtainWidthArray[shrinkToNum],////初始值
                            {toValue: width,duration:time, useNativeDriver: false} //结束值
                        )
                    );
                }
                if(this.state.layoutType===0){
                    rightCurtainAnimationParams.push(
                        Animated.timing(
                            rightCurtainWidthArray[shrinkToNum],//初始值
                            {toValue: width,duration:time, useNativeDriver: false} //结束值
                        )
                    );
                }
            }
            this.setState({
                //motionRatio: ratio,
                // curtainPosition:ratio
            });
            //左右窗帘同时开启动画
            /*Animated.sequence(leftCurtainAnimationParams).start();
            Animated.sequence(rightCurtainAnimationParams).start();*/

            console.log("dengying ---------------------",leftCurtainAnimationParams,rightCurtainAnimationParams);

            animation = Animated.sequence(leftCurtainAnimationParams);
            if(this.state.layoutType===0){
                animation = Animated.parallel([Animated.sequence(leftCurtainAnimationParams), Animated.sequence(rightCurtainAnimationParams)]);
            }
            animation.start(() => {
                console.log("动画停止");
                this.setState({
                    isShrinking: false,
                    isExpanding: false,
                    curtainPosition: curtainPosition,
                    motionRatio: curtainPosition,
                    isAnimating: false
                });
            });
        }


    }

    _renderCurtainAnimationLayout(){
        if(this.state.layoutType==0){
            return(
                <View style={{width: 250, justifyContent: 'center', flexDirection: 'row'}}>
                    <View style={{width: 125, justifyContent: 'flex-start', flexDirection: 'row'}}>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim1, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim2, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim3, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim4, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim5, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    </View>


                    <View style={{width: 125, justifyContent: 'flex-end', flexDirection: 'row'}}>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim10, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim9, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim8, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim7, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim6, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_3_2x.png")}/>
                        <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    </View>

                </View>
            )
        }else if(this.state.layoutType==1){
            return(
                <View style={{width: 250, justifyContent: 'flex-start', flexDirection: 'row'}}>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim1, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")} />
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim2, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim3, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim4, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim5, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>

                    <Animated.Image style={{width: this.state.widthAnim6, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim7, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim8, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim9, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                    <Animated.Image style={{width: this.state.widthAnim10, height: 205, marginTop: 4}}
                                    source={require("../res/images/pic_left_right_2x.png")}/>
                    <Image style={{width: 10, height: 214}} source={require("../res/images/pic_2_2x.png")}/>
                </View>
            )
        }else if(this.state.layoutType==2){
            return(
                <View style={{width: 250, justifyContent: 'center', flexDirection: 'row'}}>
                    <View style={{width: 250, justifyContent: 'flex-end', flexDirection: 'row'}}>
                        <Image style={{width:10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim1, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim2, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim3, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim4, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim5, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>

                        <Animated.Image style={{width: this.state.widthAnim6, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim7, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim8, height: 205, marginTop: 4}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim9, height: 205, marginTop: 4,resizeMode:"stretch"}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                        <Animated.Image style={{width: this.state.widthAnim10, height: 205, marginTop: 4,resizeMode:"stretch"}}
                                        source={require("../res/images/pic_left_right_2x.png")}/>
                        <Image style={{width: 10, height: 214,resizeMode:"stretch"}} source={require("../res/images/pic_2_2x.png")}/>
                    </View>
                </View>
            )
        }
    }

    _renderMoreLayout(){
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={this.state.showModal}
                onRequestClose={() => {
                    this.setState({showModal: false});
                }}
                onShow={() => {}}
                onDismiss={() => {}}>

                <View style={{
                    flexDirection: 'column',
                    flex: 1,
                    justifyContent: 'flex-end',
                    backgroundColor: '#00000099'
                }}>
                    <TouchableWithoutFeedback onPress={() => this.setState({showModal: false})}>
                        <View style={{position: 'absolute', left: 0, right: 0, top: 0, bottom: 0}}/>
                    </TouchableWithoutFeedback>

                    <View style={{
                        flexDirection: 'column',
                        borderTopLeftRadius:20,
                        borderTopRightRadius:20,
                        backgroundColor: '#FFFFFF'
                    }}>
                        <Text style={{marginTop:20,color:'#333333',fontSize:15,textAlign:'center'}}>{stringsTo('window_curtains_style')}</Text>

                        <View style={{marginVertical:30,flexDirection:'row',alignItems: 'center',justifyContent: 'center',width:'100%',height:100}}>
                            <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                                <ImageButton
                                    style={[{width: 60, height: 60},this.state.layoutType==0?{backgroundColor:'#1E5BA9',borderRadius:10}:{}]}
                                    source={this.state.layoutType==0?require('../res/images/layout_double_checked.png'):require('../res/images/layout_double_unchecked.png')}
                                    onPress={()=>{
                                        this._setLayoutType(0);
                                        this.setState({showModal: false});
                                    }}
                                />
                                <Text style={{marginTop: 15, color: "#333333", fontSize: 12}}>{stringsTo('double_open')}</Text>
                            </View>

                            <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                                <ImageButton
                                    style={[{width: 60, height: 60},this.state.layoutType==1?{backgroundColor:'#1E5BA9',borderRadius:10}:{}]}
                                    source={this.state.layoutType==1?require('../res/images/layout_left_checked.png'):require('../res/images/layout_left_unchecked.png')}
                                    onPress={()=>{
                                        this._setLayoutType(1);
                                        this.setState({showModal: false});
                                    }}
                                />
                                <Text style={{marginTop: 15, color: "#333333", fontSize: 12}}>{stringsTo('left_open')}</Text>
                            </View>


                            <View style={{alignItems: 'center',justifyContent: 'center',flex:1,height:'100%'}}>
                                <ImageButton
                                    style={[{width: 60, height: 60},this.state.layoutType==2?{backgroundColor:'#1E5BA9',borderRadius:10}:{}]}
                                    source={this.state.layoutType==2?require('../res/images/layout_right_checked.png'):require('../res/images/layout_left_unchecked.png')}
                                    onPress={()=>{
                                        this._setLayoutType(2);
                                        this.setState({showModal: false});
                                    }}
                                />
                                <Text style={{marginTop: 15, color: "#333333", fontSize: 12}}>{stringsTo('right_open')}</Text>
                            </View>
                        </View>


                    </View>

                </View>
            </Modal>
        )
    }
}
