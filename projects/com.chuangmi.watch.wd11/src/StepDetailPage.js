import React, {Component} from 'react';
import {
  View, Text, Image, StyleSheet, TouchableOpacity, BackHandler
} from 'react-native';
import NavigationBar from "../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {isAndroid} from "../../../imilab-rn-sdk/utils/Utils";
import ECharts from "../../../local_third_modules/native-echarts/src";
import {WatchDataMgr} from "./utils/IMIWatchDataMgr";
import {IMIWatch} from "./HealthyMainPage";
import {LetDevice} from "../../../imilab-rn-sdk";

/**
 * 步数详情页
 */
export default class StepDetailPage extends Component {

  constructor(props, context) {
    super(props, context);
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth();
    let day = date.getDate();
    this.todayTimeStamp = new Date(year, month, day, 0, 0, 0).getTime();//今日0点时间戳
    this.state = {
      dataRange: 0,//0日，1周，2月
      xData: [],//X坐标轴数据
      yData: [],//Y坐标轴数据
      timeStamp: this.todayTimeStamp,//当前显示的时间戳
      steps: -1,
      distance: -1,
      kCal: -1,
      dailyAverage: -1,//日均完成
    }
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', _ => this._onPressBack());
    }
  }

  componentDidMount() {
    IMIWatch.getUserInfo(LetDevice.deviceID).then(userInfo => {
      let userJson = JSON.parse(userInfo);
      this.targetStep = userJson.targetStep;
      this.unit = userJson.unit;//1:公制 0:英制

      this._refreshData(0);
    });

  }

  componentWillUnmount() {
  }

  render() {
    let isDay = this.state.dataRange === 0;//是否是日布局
    return (
      <View style={{flex: 1}}>
        <View style={{
          flex: isDay ? 2 : 5,
          backgroundColor: "#8BBBC3",
          alignItems: 'center'
        }}>
          <NavigationBar type={NavigationBar.TYPE.DARK}
                         backgroundColor={"transparent"}
                         title={"步数"}
                         left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
                         right={[]}/>

          {this._renderDataLayout()}
        </View>

        <View style={{
          flex: isDay ? 1 : 3,
          backgroundColor: "#439DAA",
          alignItems: 'center',
          padding: 16,
        }}>
          {this._renderDateLayout(isDay)}
        </View>
      </View>
    )
  }

  /**
   * 图表数据布局
   */
  _renderDataLayout() {
    return (
      <View style={{flex: 1, width: "100%", alignItems: "center"}}>
        <View style={{width: "60%", height: 60, paddingTop: 16, flexDirection: "row"}}>
          <TouchableOpacity
            style={{flex: 1}}
            onPress={() => this._changeDataRange(0)}
          >
            <View style={{flex: 1, alignItems: 'center', justifyContent: 'flex-start'}}>
              <Text style={{fontSize: 16, color: this.state.dataRange === 0 ? "#ffffff" : "#439DAA"}}>{"日"}</Text>
              {this.state.dataRange === 0 ?
                <View style={{marginTop: 4, width: 24, height: 2, backgroundColor: "#ffffff"}}/> : null}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={{flex: 1}}
            onPress={() => this._changeDataRange(1)}
          >
            <View style={{flex: 1, alignItems: 'center', justifyContent: 'flex-start'}}>
              <Text style={{fontSize: 16, color: this.state.dataRange === 1 ? "#ffffff" : "#439DAA"}}>{"周"}</Text>
              {this.state.dataRange === 1 ?
                <View style={{marginTop: 4, width: 24, height: 2, backgroundColor: "#ffffff"}}/> : null}
            </View>
          </TouchableOpacity>

          {/*<TouchableOpacity*/}
          {/*  style={{flex: 1}}*/}
          {/*  onPress={() => this._changeDataRange(2)}*/}
          {/*>*/}
          {/*  <View style={{flex: 1, alignItems: 'center', justifyContent: 'flex-start'}}>*/}
          {/*    <Text style={{fontSize: 16, color: this.state.dataRange === 2 ? "#ffffff" : "#439DAA"}}>{"月"}</Text>*/}
          {/*    {this.state.dataRange === 2 ?*/}
          {/*      <View style={{marginTop: 4, width: 24, height: 2, backgroundColor: "#ffffff"}}/> : null}*/}
          {/*  </View>*/}
          {/*</TouchableOpacity>*/}
        </View>

        {this._renderEChartLayout()}
      </View>
    );
  }

  /**
   * EChart布局
   * @private
   */
  _renderEChartLayout() {
    const option = {
      tooltip: {
        trigger: 'axis',
        position: function (pt) {
          return [pt[0], '10%'];
        }
      },
      xAxis: {
        data: this.state.xData,
        type: 'category',
        boundaryGap: true,
        axisTick: { //坐标轴刻度相关设置
          show: false, //是否显示坐标轴刻度。
          alignWithLabel: true, //类目轴中在 boundaryGap 为 true 的时候有效，可以保证刻度线和标签对齐
          // interval: this.state.dataRange === 0 ? 4 : 0, //坐标轴刻度的显示间隔，在类目轴中有效。默认会采用标签不重叠的策略间隔显示标签。可以设置成 0 强制显示所有标签。如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
          inside: true, //坐标轴刻度是否朝内，默认朝外。
          length: 2, //坐标轴刻度的长度。
        },
        axisLabel: { //坐标轴刻度标签的相关设置
          show: true, //是否显示
          // interval: 0, //坐标轴刻度标签的显示间隔，在类目轴中有效。默认会采用标签不重叠的策略间隔显示标签。可以设置成 0 强制显示所有标签。如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
          inside: false, //刻度标签是否朝内，默认朝外
          rotate: 0, //刻度标签旋转的角度，在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。旋转的角度从 -90 度到 90 度
          margin: 4, //刻度标签与轴线之间的距离
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        boundaryGap: false,
        axisTick: { //坐标轴刻度相关设置
          show: false, //是否显示坐标轴刻度。
          alignWithLabel: true, //类目轴中在 boundaryGap 为 true 的时候有效，可以保证刻度线和标签对齐
          interval: 0, //坐标轴刻度的显示间隔，在类目轴中有效。默认会采用标签不重叠的策略间隔显示标签。可以设置成 0 强制显示所有标签。如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
          inside: true, //坐标轴刻度是否朝内，默认朝外。
          length: 4, //坐标轴刻度的长度。
        },
        axisLabel: { //坐标轴刻度标签的相关设置
          show: true, //是否显示
          interval: 0, //坐标轴刻度标签的显示间隔，在类目轴中有效。默认会采用标签不重叠的策略间隔显示标签。可以设置成 0 强制显示所有标签。如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示隔两个标签显示一个标签，以此类推
          inside: false, //刻度标签是否朝内，默认朝外
          rotate: 0, //刻度标签旋转的角度，在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。旋转的角度从 -90 度到 90 度
          margin: 4, //刻度标签与轴线之间的距离
        },
        splitLine: {
          show: true
        }
      },
      series: [
        {
          name: '步数',
          type: 'bar',
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: '#000000',
            width: 1
          },
          areaStyle: {
            color: '#000000'
          },
          data: this.state.yData,
        }
      ]
    };
    return (
      <View style={{paddingBottom: 16}}>
        <ECharts option={option} height={380} width={"100%"}/>
      </View>
    )
  }

  _changeDataRange(dataRange) {
    this.setState({dataRange: dataRange})
    this._refreshData(dataRange);
  }

  _refreshData(dataRange) {
    let xData = [];
    let yData = [];

    let stepValue = -1;
    let distanceValue = -1;
    let kcalValue = -1;
    let dailyAverage = -1;
    if (dataRange === 0) {//日视图
      for (let i = 0; i < 24; i++) {
        if (i < 10) {
          xData[i] = "0" + i + ":00";
        } else {
          xData[i] = i + ":00";
        }
      }

      let motionData = WatchDataMgr.getMotionDataByDate(this._formatTimeStr(this.state.timeStamp));
      console.log("szm===========步数数据获取时间戳:", this.state.timeStamp, "  formart:", this._formatTimeStr(this.state.timeStamp));
      console.log("szm===========步数数据获取数据:", motionData);
      if (motionData !== "") {
        let motionDataJson = JSON.parse(motionData);
        stepValue = motionDataJson.motionStep;
        distanceValue = Math.round(motionDataJson.motionDistance * 100) / 100;
        kcalValue = Math.round(motionDataJson.motionCalorie * 100) / 100;
        yData = motionDataJson.motionData;
      }
    } else if (dataRange === 1) {
      let oneDayMillsSecond = 24 * 60 * 60 * 1000;
      let totalSteps = 0;
      let totalkCal = 0;
      let totalDistance = 0;
      let hasDataDay = 0;//有数据的天数
      for (let i = 0; i < 7; i++) {
        let timeStr = this._formatTimeStr(this.state.timeStamp - i * oneDayMillsSecond);
        xData[6 - i] = timeStr.substring(5);

        let motionData = WatchDataMgr.getMotionDataByDate(timeStr);
        if (motionData !== "") {
          let motionDataJson = JSON.parse(motionData);
          yData[6 - i] = motionDataJson.motionStep;
          totalDistance += motionDataJson.motionDistance;
          totalkCal += motionDataJson.motionCalorie;
          hasDataDay++;
        } else {
          yData[6 - i] = 0;
        }
        totalSteps += yData[6 - i];
      }

      kcalValue = hasDataDay !== 0 ? Math.round(totalkCal * 100) / 100 : "--";
      distanceValue = hasDataDay !== 0 ? Math.round(totalDistance * 100) / 100 : "--";
      stepValue = hasDataDay !== 0 ? Math.round(totalSteps / hasDataDay * 100) / 100 : "--";
      dailyAverage = hasDataDay !== 0 ? Math.round(totalSteps * 100 / this.targetStep / hasDataDay * 100) / 100 : "--";
    }

    console.log("szm  图标数据  x:", xData, "   y:", yData);
    this.setState({
      xData: xData,
      yData: yData,
      steps: stepValue,
      distance: distanceValue,
      kCal: kcalValue,
      dailyAverage: dailyAverage
    });
  }

  /**
   * 将时间戳（毫秒）转化为 "2021/02/02"
   * @param timeStamp 0，当前时间
   * @param hideYear 字符串是否隐藏年份，默认不隐藏
   * @private
   */
  _formatTimeStr(timeStamp, hideYear) {
    let date = timeStamp === 0 ? new Date() : new Date(timeStamp);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    return year.toString() + "-" + (month < 10 ? ("0" + month) : month).toString() + "-" + (day < 10 ? ("0" + day) : day).toString();
  }

  /**
   * 渲染日期布局
   */
  _renderDateLayout(isDay) {
    return (
      <View style={{height: "100%", width: "100%", alignItems: 'center'}}>
        <View style={{
          flex: 2,
          width: "80%",
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: "row",
        }}>
          <TouchableOpacity
            style={{flex: 1, alignItems: 'center'}}
            onPress={() => this._changeDate(false)}
          >
            <Image style={{width: 20, height: 20}} source={require('../res/images/icon_left.png')}/>
          </TouchableOpacity>

          <Text style={{
            flex: 2,
            fontSize: 20,
            color: "#ffffff",
            textAlign: 'center'
          }}>{this._formatTimeStr(this.state.timeStamp)}</Text>

          <TouchableOpacity
            style={{flex: 1, alignItems: 'center'}}
            onPress={() => this._changeDate(true)}
          >
            <Image style={{width: 20, height: 20}} source={require('../res/images/icon_right.png')}/>
          </TouchableOpacity>
        </View>

        <View style={{flex: isDay ? 3 : 4, width: "100%"}}>
          {this._renderDataGridLayout(isDay)}
        </View>
      </View>
    );
  }

  /**
   * 渲染日期布局中的数据格子
   */
  _renderDataGridLayout(isDay) {
    if (isDay) {
      return (
        <View style={{width: "100%", height: "100%", flexDirection: "row"}}>
          <View style={{
            flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 8}}>{"步数"}</Text>
            <Text style={{
              fontSize: 24,
              color: "#496EE0",
              marginTop: 8,
              textAlign: "center",
            }}>{this.state.steps === -1 ? "--" : this.state.steps}</Text>
          </View>
          <View style={{
            flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 8}}>{"距离"}</Text>
            <Text style={{
              fontSize: 24,
              color: "#496EE0",
              marginTop: 8,
              textAlign: "center",
            }}>{this.state.distance === -1 ? "--" : (this.state.distance + (this.unit === 1 ? "km" : "ft"))}</Text>
          </View>
          <View style={{
            flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 8}}>{"热量"}</Text>
            <Text style={{
              fontSize: 24,
              color: "#496EE0",
              marginTop: 8,
              textAlign: "center",
            }}>{this.state.kCal === -1 ? "--" : (this.state.kCal + "kcal")}</Text>
          </View>
        </View>
      )
    } else {
      return (
        <View style={{width: "100%", height: "100%", flexDirection: "column"}}>
          <View style={{flex: 1, flexDirection: "row"}}>
            <View style={{
              flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 4}}>{"日均步数"}</Text>
              <Text style={{fontSize: 24, color: "#496EE0", marginTop: 4}}>{this.state.steps}</Text>
            </View>
            <View style={{
              flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 4}}>{"日均完成"}</Text>
              <Text style={{fontSize: 24, color: "#496EE0", marginTop: 4}}>{this.state.dailyAverage + "%"}</Text>
            </View>
          </View>

          <View style={{flex: 1, flexDirection: "row"}}>
            <View style={{
              flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 4}}>{"总路程"}</Text>
              <Text style={{fontSize: 24, color: "#496EE0", marginTop: 4}}>{this.state.distance +  (this.unit === 1 ? "km" : "ft")}</Text>
            </View>
            <View style={{
              flex: 1, borderRadius: 5, backgroundColor: "#FFFFFF", margin: 2, alignItems: 'center',
              justifyContent: 'center',
            }}>
              <Text style={{fontSize: 16, color: "#13A4BB", marginBottom: 4}}>{"总消耗"}</Text>
              <Text style={{fontSize: 24, color: "#496EE0", marginTop: 4}}>{this.state.kCal + "kcal"}</Text>
            </View>
          </View>
        </View>
      )
    }
  }

  /**
   * 改变日期
   * @param add true向后一天，否则向前一天
   */
  _changeDate(add) {
    let newTimeStamp = add ? this.state.timeStamp + 24 * 60 * 60 * 1000 : this.state.timeStamp - 24 * 60 * 60 * 1000;
    this.setState({timeStamp: newTimeStamp},
      () => this._refreshData(this.state.dataRange))
    ;
  }

  _onPressBack() {
    this.props.navigation.goBack()
  }

}


