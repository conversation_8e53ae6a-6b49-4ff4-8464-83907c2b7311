/**
 * HomePageLivePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 * @property {string}  pageBackgroundColor 直播播放器下方空白页面的颜色
 *
 * 示例:
 * <HomePageLivePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </HomePageLivePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/11/25
 */

import React, {Component} from 'react';
import {
    View, Text, BackHandler, ActivityIndicator,StyleSheet
} from 'react-native';
import {IMIGotoPage, LetDevice} from "../../../../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import I18n, {stringsTo} from "../../../../../globalization/Localize";
import LivePlayerToolBarView from "../PlayerToolBarView/LivePlayerToolBarView";
import NavigationBar from "../NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import TouchableOpacityText from "../TouchableOpacityText/TouchableOpacityText";
import ModalView from "../ModalView/ModalView";
import {
    getScreenHeight,
    getScreenWidth,
    isAndroid,
    isIos,
    isIphoneXSeries
} from "../../../../../imilab-rn-sdk/utils/Utils";
import PropTypes from 'prop-types';
import IMIToast from "../../../../../imilab-design-ui/src/widgets/IMIToast";
import LivePlayerFullScreenToolBarView from "../PlayerToolBarView/LivePlayerFullScreenToolBarView";
import {XText} from "react-native-easy-app";
import {colors, RoundedButtonView, showLoading} from "../../../../../imilab-design-ui";
import ImageButton from "../ImageButton/ImageButton";
import IMIPermission from "../../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {showToast} from "../../../../../imilab-design-ui/src/widgets/Loading";
import Toast from "react-native-root-toast";
import * as Typography from "../../../../../imilab-design-ui/src/style/Typography";
import {timeFilter} from "../../../../../imilab-rn-sdk/utils/DateUtils";
import {CONST} from "../../../../../imilab-design-ui/src/style/ThemeManager";
import IMILog from "../../../../../imilab-rn-sdk/native/local-kit/IMILog";
import IMILogUtil from "../../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import moment from "moment";
import NetInfo from "@react-native-community/netinfo";
const LIVE_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading' ,
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
Object.freeze(LIVE_PLAYER_STATUS);
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});

const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let time;
let bpsArray = []; //存放最新5次网速值的数组
let timeErr10005=0;// 1005连接次数错误

let liveDataSourceParams = {
    playerClass: IMICameraVideoView.PlayerClass.LIVE,
    did: LetDevice.deviceID,
    audioParams:3 //MONO_16K_AAC_LC
};
LetDevice.model != "a1EVaCvJ43g" && delete liveDataSourceParams.audioParams; //只有033传入audioParams
export default class HomePageLivePlayerComponent extends Component {
    static LIVE_PLAYER_STATUS = LIVE_PLAYER_STATUS;
    
    constructor(props, context) {
        super(props, context);
        this.state = {
            // qualityData: [ stringsTo("quality_sd"), stringsTo("quality_fhd"),stringsTo("quality_2k"),],
            qualityIndex: this.props.defaultQualityIndex,
            qualityVisible: false,
            bps: -1,
            isFullScreen: false,
            mute: LetDevice.category != "doorbell", //门铃产品默认打开监听
            recording: false,
            calling: false,
            recordDuration: 0,
            showFullScreenTools: false,

            isLoading: false,
            isPlaying: false,
            showErrorView: false,
            showPauseView: false,
            errorCode: null,

            snapshotVisible: false,
            screenShotPath: null,

            netConnected:true,//是否有网络连接
        }
        this.isForegroundPage = true;//是否显示在前台
    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        videoSubView: PropTypes.func,

        coverView: PropTypes.func,
        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,
        isSleepStatus: PropTypes.bool,//休眠状态
        isMove: PropTypes.bool,//全屏触摸转动云台
        isOnLine: PropTypes.bool,//在线状态
        isCalling:PropTypes.bool,//通话是否打开
        onLivePlayerStatusChange: PropTypes.func,

        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number
        }),
        onVideoClick: PropTypes.func,
        pageBackgroundColor: PropTypes.string,
        qualityData:PropTypes.array,//清晰度数据
        albumName:PropTypes.string,//相册名称
        onCheckPermissionStatusChange:PropTypes.func, //回调是否处于权限检测状态中
        onVoiceCallErrorOccurred: PropTypes.func,
        componentContainerRef:PropTypes.func,  //回调组件容器的引用，方便调用者以此来获取容器的宽高等参数
        onDeviceStatusChange:PropTypes.func, // 回调设备当前的状态
        onOrientationChange:PropTypes.func //回调横竖屏的状态变化
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        qualityData:[{title:stringsTo("quality_low"),index:0,accessibilityLabel:"home_page_clarity_show_low"},
            {title:stringsTo("quality_sd"),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo("quality_fhd"),index:2,accessibilityLabel:"home_page_clarity_show_fhd"}],
        defaultQualityIndex:0,
        isSleepStatus:false,
        isOnLine:true,
        isCalling:false,
        isMove:false,
        albumName:LetDevice.devNickName,
    };


    UNSAFE_componentWillMount(){

        Orientation.lockToPortrait();


        LetDevice.getPropertyCloud('StreamVideoQuality').then(data => {
            let  selectedIndex = 0;
            for (let i = 0; i < this.props.qualityData.length; i++){
                let qualityData = this.props.qualityData[i];
                if (qualityData.index == data){
                    selectedIndex = i;
                }
            }
            this.setState({qualityIndex: selectedIndex});
        }).catch(error => {
           // alert(JSON.stringify(Utils.parseError(error)));
        });

        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack()); //解决031、038无法单步返回的问题

        /*本页面获得焦点*/
        this._subscribeFocus = this.props.navigation.addListener('focus', () => {
            Orientation.addOrientationListener(this._orientationDidChange);
        });

        /*本页面失去焦点*/
        this._subscribeBlur = this.props.navigation.addListener('blur', () => {
            Orientation.removeOrientationListener(this._orientationDidChange);
            this.state.qualityVisible && this.setState({qualityVisible: false});
        });

        this._subscribeNetInfo = NetInfo.addEventListener(state => {
            if (state.isConnected !== this.state.netConnected) {
                this.setState({netConnected: state.isConnected});
            }
        });


    }

    componentDidMount() {
        // setTimeout(() => this.IMIVideoView.start(), 2000)
        this.props.componentContainerRef && this.props.componentContainerRef(this.componentContainer);
    }

    componentWillUnmount() {
        Orientation.removeOrientationListener(this._orientationDidChange);
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.IMIVideoView = null;
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.backHandler && this.backHandler.remove();
        this._subscribeFocus && this._subscribeFocus();
        this._subscribeBlur && this._subscribeBlur();
        this._subscribeNetInfo && this._subscribeNetInfo();
        this.delayFullScreen && clearTimeout(this.delayFullScreen);
    }

    //判断当前是否可以操作
    _canStepIn(){
      
        if (!this.props.isOnLine){
            console.log("设备离线，不可操作");
            showToast(stringsTo('device_offline'));
            return false;
        }
        if (this.props.isSleepStatus){
            console.log("设备休眠，不可操作");
            showToast(stringsTo('power_off'));
            return false;
        }
        if(!this.state.isPlaying){
            console.log("直播流未开始，不可操作");
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

  _canIsCalling(){
        console.log("通话状态，state:",this.props.isCalling);
        if (this.props.isCalling)
        {
            showToast(stringsTo('imi_speaking_block'));
            return false;
        }
        return true;
    }


    _orientationDidChange = (orientation) => { //加此监听逻辑用来解决方向锁定关闭后，横放手机再点全屏按钮，页面显示不对的问题
        if (orientation === 'LANDSCAPE') {
            if (!this.state.isFullScreen&&this.state.isPlaying) {
                //不先全屏和不加延时，直接走点击全屏按钮逻辑无法正确切换全屏
                Orientation.lockToPortrait();
                this.delayFullScreen = setTimeout(()=>{
                    this._onPressFullScreen();
                    this.delayFullScreen && clearTimeout(this.delayFullScreen);
                },50);
            }
        } else {
            this.state.isFullScreen && this._exitFullScreen();
        }
    };


    _onPressFullScreen = () => {
        if(!this._canStepIn())  return;
        Orientation.lockToPortrait();
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
        this.props.onOrientationChange && this.props.onOrientationChange(true);
        IMILogUtil.uploadClickEventForCount("FullScreen"); //统计点击全屏的情况
    };

    _exitFullScreen = () => {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this.props.onOrientationChange && this.props.onOrientationChange(false);
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
            return true;
        }
        return false;
    };


    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            if (this.props.isMove)return;//全屏长按触摸 取消消失
            this._onCloseFullScreenTools();
        }, 8000);
    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }

    _onPressMute = () => {
        if(!this._canStepIn())  return;
        this.setState({mute: !this.state.mute})
    };

    /*告知外部调用者监听状态*/
    getMute() {
        return this.state.mute;
    }

    /*外部调用者通知该组件监听状态的变化*/
    setMute(mute) {
        this.setState({mute: mute});
    }

    /*外部调用者通知该组件是否在通话*/
    setCalling(isCalling) {
        this.setState({calling: isCalling});
    }

    //获取录屏状态
    getRecordStatus(){
        return this.state.recording;
    }

    //设置是否显示暂停按钮
    setPauseView(showPauseView){
        this.setState({showPauseView: showPauseView});
    }
    //同步清晰度
    setQualityIndex(index){
        console.log('王--setQualityIndex',index);
        this.setState({qualityIndex: index});
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        }
    }
    setIsForegroundPage(isForegroundPage){
       this.isForegroundPage=isForegroundPage;
    }

  // 052全屏状态是有报警按钮，如果看家助手/一键警告 未打开，
    //  需要退出全屏进入看家助手/一键警告
    quitFullScreen() {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this.props.onOrientationChange && this.props.onOrientationChange(true);
        this._onCloseFullScreenTools();

    }



    //点击截屏按钮
    _onPressScreenShot = () => {

        if(!this._canStepIn())  return;

        IMILogUtil.uploadClickEventForCount("VideoSnapImage"); //统计点击截屏的情况
        if(new Date().getTime()-lastClickSnapPhoto<1000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        lastClickSnapPhoto = new Date().getTime();
        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);


        if(this.state.snapshotVisible){
            this.setState({ snapshotVisible: false});
        }
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    if (status2 === 0) {
                        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                            console.log("排查页面卡死问题------截图成功");
                            IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, this.props.albumName).then(_ => {
                                this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true});
                                IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
                                console.log("排查页面卡死问题------截图成功-----保存截图成功");
                                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                    this.setState({ snapshotVisible: false});
                                }, 3000);
                            });
                        });
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    }
                });
            } else if (status === -1) {
                this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                showToast(stringsTo('storage_permission_denied'))
            }
        })


    };

    //点击录屏按钮
    _onPressRecord = () => {
        console.log("录屏-----点击录屏按钮",this.state.recording);
        if(!this._canStepIn())  return;
    /*    if(!this._canIsCalling())  return;*/
        IMILogUtil.uploadClickEventForCount("VideoRecord"); //统计点击录屏的情况
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
                if (status === 0) {
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                        if (status2 === 0) {
                            time=moment(new Date().getTime()).format('yyyyMMDD')+"_"+new Date().getTime();
                            let pathUrl=VEDIO_RECORD_PATH;
                            if (isIos()){
                                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                            }
                            console.warn("排查录屏卡死问题------录屏-----开始录屏",pathUrl);
                            this.IMIVideoView.startRecord(pathUrl).then(_ => {
                                console.warn("排查录屏卡死问题------录屏-----开始录屏");
                                this.setState({recording: true, recordDuration: 0});
                            });
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        } else if (status2 === -1) {
                            showToast(stringsTo('storage_permission_denied'));
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        }
                    })
                } else if (status === -1) {
                    showToast(stringsTo('storage_permission_denied'));
                    this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                }
            })

        }
    };

    //停止录像并保存在相册
    _stopRecord(forSave=true){
        console.log("排查录屏卡死问题------录屏-----停止录制",this.state.recordDuration);
        if(this.state.recordDuration<6){
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            console.log("排查录屏卡死问题------录屏-----停止录制成功");
            if(!forSave){ //只停止，不保存
                console.log("停止录制-------保存失败");//save_system_album_failed
               // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }

            let pathUrl=VEDIO_RECORD_PATH;
            if (isIos()){
                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }
            IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
                if (this.isForegroundPage){
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                console.log("排查录屏卡死问题------录屏-----停止录制成功后保存录像");
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                    this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true});
                    this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                        this.setState({snapshotVisible: false});
                    }, 3000);
                    });
                }
                IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            });
        });
        this.setState({recording: false, recordDuration: 0});
    }

    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum() {
        console.log("排查页面卡死问题------录屏因为实时流停止------",this.state.recordDuration,this.state.isPlaying);
        if (this.state.recordDuration < 6&&this.state.recordDuration>0) { //Android因为直播流停止recordDuration会直接边为0
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.setState({recording: false, recordDuration: 0});
            return;
        }
        let pathUrl=VEDIO_RECORD_PATH;
        if (isIos()){
            pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
            if (this.isForegroundPage){
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
            this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);
            });
            }
            IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        });
        this.setState({recording: false, recordDuration: 0});
    }

    _onPressInStartSpeak = () => {
        this.IMIVideoView.startSpeak()
    }

    _onPressOutStopSpeak = () => {
        this.IMIVideoView.stopSpeak();
    }

    _onPressQuality(index, selectIndex) {
        if (this.state.qualityIndex === selectIndex) return;
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify({"StreamVideoQuality": index})).then(data => {
            this.setState({qualityIndex: selectIndex});
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    /*优化网速值有较大跳变的问题，取平均值*/
    _getAverageBps(currentBps){
        let average = 0;
        if(bpsArray.length<5){
            bpsArray.push(currentBps);
        }else{
            bpsArray.shift();
            bpsArray.push(currentBps);
        }
        let total = bpsArray.reduce((a,b)=>{
            return a+b;
        });
        return  parseInt(total/bpsArray.length);
    }

    _onEventChange = (event) => {
        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log("直播流----_onEventChange,回调网速值",event.extra.bps);
            this.setState({bps: this._getAverageBps(event.extra.bps)});
            if ( event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying){ //loading消失而实时流不是playing状态
                this.setState({isLoading:false,isPlaying:true,showPauseView: false})
                this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
            }
           // event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying&&this.setState({isLoading:false,isPlaying:true});//此句为了规避正常播放时突然出现loading框的问题
            //10005时3次连接问题 如果大于0kb就清除
              if ( event.extra.bps>0){
                  timeErr10005=0;
              }
        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("直播流----_onEventChange,开始启用");
          //  IMILog.logD("王 实时流 _onEventChange >LOADING ",event.toString())
            this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
            if(this.state.recording&&isIos()){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                    this._stopRecord();
             }
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log("直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false, isPlaying: true});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
         //  IMILog.logD("王 实时流 _onEventChange >PAUSE  ",event.toString())
            console.log("直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.setState({isLoading: false, isPlaying: false, showPauseView: true}); //Android会调用两次，所以停止了也没有暂停按钮
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);

            if(this.state.recording){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                if(CONST.isAndroid&&this.state.isPlaying){
                    this._stopRecord();
                }else{ //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
                    this._saveVideoToPhotosAlbum();
                }

            }

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            console.log("直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});
        }else if (event.code ===104) {
            if(CONST.isAndroid&&event.extra.mode_value_key==false){
                this.IMIVideoView.clearRecord();
            }
        }
    };

    _onRecordTimeChange = (event) => {
        console.log("排查页面卡死问题------录屏-----时间回调",event.extra);
        this.setState({recordDuration: event.extra})
    }

    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (
            // <>
            //     <View style={{position: "absolute", width: "100%"}}>
            //         {
            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.DARK} backgroundColor={"transparent"}
                    title={LetDevice.devNickName}
                    subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                    left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                    right={this.props.navBarRight ? this.props.navBarRight : []}/>)
            //         }
            //     </View>
            // </>
        );
    }

    /**
     * 清晰度选择器
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenQualityPopView() {
        return (
            <ModalView visible={this.state.qualityVisible}
                       onClose={_ => this.setState({qualityVisible: false})}>
                <View style={{flex: 1, flexDirection: "column", justifyContent: "center", alignItems: "center"}}>
                    {
                        this.props.qualityData.map((item, index) => {
                            let qualityData = this.props.qualityData[index];
                            return (
                                <TouchableOpacityText
                                    key={`qualityItem_${index}`}
                                    title={qualityData.title}
                                    accessibilityLabel={qualityData.accessibilityLabel}
                                    style={{
                                        width: 120,
                                        height: 40,
                                        borderRadius: 20,
                                        backgroundColor: index === this.state.qualityIndex ? "#4A70A5" : "rgba(255,255,255,0.3)",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        marginVertical: 10
                                    }}
                                    textStyle={{color: 'rgba(255,255,255,0.9)', fontSize: 15, fontWeight: "bold"}}
                                    onPress={_ => {
                                        this.setState({qualityVisible: false});
                                        this._onPressQuality(qualityData.index,index);
                                    }}
                                />
                            );
                        })
                    }
                </View>
            </ModalView>
        );
    }

    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenVideoViewArea() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        return (
            this.state.showFullScreenTools ? (
                <LivePlayerFullScreenToolBarView
                    // style={{position: "absolute"}}
                    exitPress={this._exitFullScreen}
                    qualityTitle={qualityData.title}
                    qualityPress={_ =>{ //IPC031 033要求直播流不打开也可以修改画质
                        if (LetDevice.model != "a1MZkl613tF" && LetDevice.model != "a1EVaCvJ43g"
                            && !this._canStepIn()) return;
                        this.setState({qualityVisible: true})
                    }}
                    qualityDisabled={this.state.recording}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    screenshotPress={this._onPressScreenShot}
                    recordPress={this._onPressRecord}
                    recording={this.state.recording}
                    moreItems={this.props.fullScreenToolBarMoreItems}
                    accessibilityLabel={qualityData.accessibilityLabel}
                />
            ) : null
        );
    }


    /**
     * 竖屏状态下半屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenPlayerToolBarArea() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];

        return (
            <View style={{flex: 1, flexDirection: "column"}}>
                <LivePlayerToolBarView
                    qualityTitle={qualityData.title}
                    accessibilityLabel={qualityData.accessibilityLabel}
                    qualityPress={_ => {  //IPC031 033要求直播流不打开也可以修改画质
                        if (LetDevice.model != "a1MZkl613tF" && LetDevice.model != "a1EVaCvJ43g"
                            && !this._canStepIn()) return;
                        this.setState({qualityVisible: true})
                    }}
                    qualityDisabled={this.state.recording}
                    fullscreenPress={this._onPressFullScreen}  //全屏
                    mutePress={this._onPressMute}  //音量
                    mute={this.state.mute}
                    screenshotPress={this._onPressScreenShot}  //截图
                    recordPress={this._onPressRecord}  //录像
                    recording={this.state.recording}
                    moreItems={this.props.toolBarMoreItems}
                />
                {this.props.children}
            </View>
        );
    }

    _renderRecordingView() {
        let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : "00:00";
       
        return (
            <View style={{
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: "center",
                alignItems: "center",
                marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
            }}>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={[{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'},Typography.textFix]}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (!this.state.isLoading) return;
        return (<View  pointerEvents="box-none"
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (!this.state.showErrorView) return;
        if (this.state.isLoading) return;
        return (
            <View  pointerEvents="box-none"
                style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal:15,
                                       height: 40,
                                   }}
                                   buttonTextStyle={{textAlign:'center'}}
                                   onPress={() => {
                                       if(!this.state.netConnected){
                                           showToast(stringsTo('network_not_connected'))
                                           return;
                                       }
                                       this.IMIVideoView.start();
                                       this.setState({
                                           showErrorView:false,
                                           isLoading:true
                                       });
                                   }}
                                   accessibilityLabel={"error_code_common_retry"}
                />
            </View>
        );
    }

    _pauseView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (!this.state.showPauseView) return null;
        if (this.state.showErrorView) return  null; //IOS上会先ERROR后PAUSE，如果已经显示errorView，则不显示pauseView
        if (this.state.isLoading) return;
        return (<View  pointerEvents="box-none"
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ImageButton
                style={{width: 52, height: 52}}
                source={require("./res/icon_play.png")}
                highlightedSource={require("./res/icon_play_p.png")}
                onPress={() => {
                    if(!this.state.netConnected){
                        showToast(stringsTo('network_not_connected'))
                        return;
                    }
                    this.setState({
                        showPauseView:false,
                        isLoading:true
                    });
                    this.IMIVideoView.start();
                }}
                accessibilityLabel={"home_page_icon_play"}
            />
        </View>);
    }







    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        console.log("截屏路径------------",this.state.screenShotPath);
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                bottom: this.state.isFullScreen ? 206 : 19,
                left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => {

                        if(LetDevice.model == "a1MZkl613tF"||LetDevice.model == "a1B1tBn2SdK" ||LetDevice.model == "a1EVaCvJ43g" ){
                            return;
                        }
                        if (this.state.recording) {
                            showToast(I18n.t('screen_recording'));
                            return;
                        }
                       /* if (this.state.calling) {
                            showToast(I18n.t('camera_calling'));
                            return;
                        }*/
                        if(!this._canIsCalling())  return;
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible:false});
                        if(this.state.isFullScreen){ //横屏则退出全屏

                            Orientation.lockToPortrait();
                            this.props.navigation.setOptions({tabBarVisible: true});
                            this._onCloseFullScreenTools();
                            NavigationBar.setStatusBarHidden(false);

                            this.setState({isFullScreen: false},()=>{
                                IMIGotoPage.startAlbumPage(this.props.albumName);
                            });
                            return;
                        }
                        IMIGotoPage.startAlbumPage(this.props.albumName);
                    }}
                    accessibilityLabel={"home_page_picture_show"}

                />
            </View>
        )
    }

    render() {
        return (
            <View ref={(ref) => this.componentContainer = ref} style={{flex: 1, backgroundColor: this.props.pageBackgroundColor?this.props.pageBackgroundColor:"#F2F3F5", flexDirection: "column"}}>


                <View  style={this.state.isFullScreen?styles.containFull:{flex: 1}} >
                    <IMICameraVideoView
                        style={this.state.isFullScreen?styles.cameraViewStyles:{flex: 1}}
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        mute={this.state.mute}
                        playerClass={IMICameraVideoView.PlayerClass.LIVE}
                        dataSource={liveDataSourceParams}
                        scaleRatio={1}
                        onPrepared={() => {
                            console.log("直播流-----onPrepared------");
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PREPARED);
                            //this.IMIVideoView.start();
                        }}
                        onEventChange={this._onEventChange}

                        onErrorChange={(event) => {
                            console.log("直播流-----onErrorChange------   event.code " + event.code, event);
                            IMILog.logD("王 实时流 onErrorChange : ",event.toString())
                            // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示

                            do {
                                //判断如果是通话报错则此处进行判断是否为占线
                                /*if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    alert("量产5-12——直播流Error-------- 不是流意外关闭");
                                    continue
                                }*/
                                if (event.code == 1009 && this.state.netConnected){ //Android手机断网，也走1009
                                    // 设备不在线
                                    this.props.onDeviceStatusChange&&this.props.onDeviceStatusChange(false); //通知外部调用者设备离线，需要更新相关的UI
                                    return;
                                }

                                if(isIos()){
                                    // alert('通话错误码'+event.code);
                                    console.warn('通话错误码'+event.code);
                                    if(event.code==12||event.code==15||event.code==16||event.code==19||event.code==-88002||event.code==14){ //IOS的通话异常
                                        // alert("量产5-12——直播流Error-------- IOS其他设备正在通话中，请稍后重试");
                                        if(LetDevice.model == "a1Od0SjKPGt"){
                                            this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }else  if(LetDevice.model == "a1Godgpvr3D"){
                                            if (event.code==12){
                                                showToast(stringsTo('call_busy_tips'))
                                            }else {
                                                showToast(stringsTo('call_connect_error'));
                                            }
                                            this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }else {
                                            event.code==12?showToast(stringsTo('call_busy_tips')):showToast(stringsTo("action_fail"));
                                            event.code==12?this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code):
                                                this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }

                                        return;
                                    }else{ //不是通话异常，则是正常的实时流异常，走do-while之后的逻辑
                                        continue;
                                    }

                                }
                                if (isAndroid()) {  //休眠第一次10005时  播放,连接两次后自动取消连接
                                    if (event.code == 1005) {
                                        timeErr10005 = timeErr10005 + 1;
                                        if (timeErr10005 < 3) {
                                            this.IMIVideoView.start();
                                            return;
                                        }

                                    }
                                }
                                //判断是否为通话模式(Android)
                                if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {

                                    continue
                                }
                                //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                                if (event.extra.arg2 === "voice intercom existed") {
                                    console.log("量产5-12——直播流Error-------- 其他设备正在通话中，请稍后重试");
                                    // showToast(stringsTo('call_busy_tips'));
                                    // this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred()
                                    if(LetDevice.model == "a1Od0SjKPGt"){
                                        let code = 12;
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else if (LetDevice.model == "a1Godgpvr3D"){
                                        showToast(stringsTo('call_busy_tips'));
                                        let code = 12;
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else {
                                        showToast(stringsTo('call_busy_tips'));
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }

                                } else {
                                    console.log("量产5-12——直播流Error-------- 网络异常，通话中断");
                                    // showToast(stringsTo('call_connect_error'));
                                    // this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred()
                                    if(LetDevice.model == "a1Od0SjKPGt"){
                                        let code = 1;
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else if(LetDevice.model == "a1Godgpvr3D"){
                                        showToast(stringsTo('net_connect_error'));
                                        console.log("量产5-12——直播流Error-------- 网络异常，通话中断",event.code);
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //code通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else {
                                        showToast(stringsTo('call_connect_error'));
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }
                                }


                                return
                            } while (false);

                            this.setState({
                                isPlaying:false,
                                showErrorView: true,
                                showPauseView: false,
                                errorCode: event.code
                            });
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.ERROR);
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                        lensCorrect={this.props.lensCorrect}
                        onVideoViewClick={()=>{
                            this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                    />
                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{position: "absolute", width: "100%", height: "100%", flexDirection: "column", alignItems: "center"}}>
                        {
                            this.props.videoSubView ? this.props.videoSubView(this.state.isFullScreen,this.state.showFullScreenTools) : null
                        }
                        {
                            this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()
                        }
                        {
                            this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()
                        }
                        {
                            this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()
                        }
                        {
                            this._renderSnapshotView()
                        }
                        {
                            this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() : this._renderPortraitScreenVideoViewArea()
                        }
                        {
                            this.state.recording&&this.state.isPlaying ? this._renderRecordingView() : null

                        }
                    </View>
                </View>

                {/*全屏?null:渲染外层传入的UI*/}
                {this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}

                {/*渲染清晰度选择器，不占UI空间的*/}
                {this._renderLandscapeScreenQualityPopView()}
            </View>
        );
    }
}
let styles = StyleSheet.create({
    cameraViewStyles: {
        width:getScreenWidth()*16/9,         //(getScreenWidth())*16/9,  //等比播放 防止拉伸
        height:getScreenWidth(),
    },
    cameraViewFullStyles: {
        flex:1,
    },
    containFull:{flex: 1, backgroundColor: "#000000",
        justifyContent:"center",
        alignItems:"center",
       },
});
