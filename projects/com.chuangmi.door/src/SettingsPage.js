import React, {Component} from 'react';
import {
    View
} from 'react-native';

import {IMIGotoPage, LetDevice} from "../../../imilab-rn-sdk";
import {locales, stringsTo} from "../../../globalization/Localize";
import NavigationBar from "../../../imi-rn-commonView/NavigationBar/NavigationBar";
import SettingItemView from "./CommonView/SettingItemView/SettingItemView";
import BleTools from "./Lock/Tool/BleTools";
import {RRCAlert} from "imilab-design-ui/src/widgets/overlayer";
import {showToast} from "../../../imilab-design-ui";

export default class SettingsPage extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {}
        this.FAQ_URL = `https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=${locales[0]?.languageCode}`;
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    _onPressBack = () => {
        /*this.navBar.measure((x,y,width,height,pageX,pageY)=>{
            alert(`${x} ${y} ${width} ${height} ${pageX} ${pageY}`);
        })*/
        this.props.navigation.pop();
    };

    render() {
        return (
            <View style={{flex: 1, backgroundColor: "#FFF", flexDirection: "column"}}>
                <NavigationBar type={NavigationBar.TYPE.LIGHT} backgroundColor={"#FFFFFF"}
                               title={stringsTo('commTitleSettingText')}
                               left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                               right={[]}/>
                <View style={{flex: 1, flexDirection: "column"}}>
                    {/*入户系统管理*/}
                    <SettingItemView title={stringsTo("house_system_manager")}
                                     type={SettingItemView.TYPE.NONE}
                                     // topLine={SettingItemView.LINE.LONG}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         if (BleTools.bleConnectState) {
                                             this.props.navigation.navigate("HouseSystemSettingsPage");
                                         } else {
                                             //alert(stringsTo("door_disconnect"));
                                             RRCAlert.alert(stringsTo('prompt'), stringsTo('door_disconnect'), [
                                                 {
                                                     text: stringsTo('know_button'),
                                                     style:{color:'#1E5BA9'}
                                                 }
                                             ], (index) => {
                                                 if (index == 0) {
                                                     console.log("HouseSystemSettingsPage");
                                                 }
                                             });
                                         }
                                     }}/>
                    {/*可视系统管理*/}
                    <SettingItemView title={stringsTo("camera_system_manager")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ =>{
                                         if (!LetDevice.isAL){
                                             RRCAlert.alert(stringsTo('prompt'), stringsTo('door_need_bind_camera'), [
                                                 {
                                                     text: stringsTo('know_button'),
                                                     style:{color:'#1E5BA9'}
                                                 }
                                             ], (index) => {
                                                 if (index == 0) {
                                                     console.log("FaceManagerPage");
                                                 }
                                             });
                                             return;
                                         }
                                         this.props.navigation.navigate("CameraSettingsPage")
                                     } }/>
                    {/*蓝牙网关  解锁方式*/}
                    <SettingItemView title={stringsTo("door_unlock_type")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         if (LetDevice.isShareUser){
                                             showToast(stringsTo('shareUser_tip'));
                                             return;
                                         }
                                         this.props.navigation.navigate("UnlockTypePage");
                                     }}
                    />
                    {/*通用设置*/}
                    <SettingItemView title={stringsTo("bottom_video_album")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                     }}
                    />
                    <SettingItemView title={stringsTo("comm_setting_title")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         if (LetDevice.isShareUser){
                                             showToast(stringsTo('shareUser_tip'));
                                             return;
                                         }
                                         IMIGotoPage.starNativeCommSettingPage(LetDevice.deviceID);
                                     }}
                    />
                    {/*实验室功能*/}
                    {/*<SettingItemView title={stringsTo("AIlab_function")}*/}
                    {/*// type={SettingItemView.TYPE.NONE}*/}
                    {/*onPress={_ => {*/}
                    {/*//this.props.navigation.navigate("HouseSystemSettingsPage");*/}
                    {/*}}*/}
                    {/*/>*/}
                    {/*创米云服务*/}
                    <SettingItemView title={stringsTo("imi_cloud_service")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         if (!LetDevice.isAL){
                                             RRCAlert.alert(stringsTo('prompt'), stringsTo('door_need_bind_camera'), [
                                                 {
                                                     text: stringsTo('know_button'),
                                                     style:{color:'#1E5BA9'}
                                                 }
                                             ], (index) => {
                                                 if (index == 0) {
                                                     console.log("FaceManagerPage");
                                                 }
                                             });
                                             return;
                                         }
                                         IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
                                     }}
                    />
                    {/*设备共享*/}
                    {/*<SettingItemView title={stringsTo("device_share")}*/}
                    {/*// type={SettingItemView.TYPE.NONE}*/}
                    {/*onPress={_ => {*/}
                    {/*IMIGotoPage.starNativeCommShareDeviceListPage(LetDevice.deviceID);*/}
                    {/*}}*/}
                    {/*/>*/}
                    {/*智能场景*/}
                    {/*<SettingItemView title={stringsTo("intelligent_scene")}*/}
                    {/*// type={SettingItemView.TYPE.NONE}*/}
                    {/*onPress={_ => {*/}

                    {/*}}*/}
                    {/*/>*/}
                    {/*设备信息*/}
                    <SettingItemView title={stringsTo("device_info")}
                                     type={SettingItemView.TYPE.NONE}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     onPress={_ => {
                                         this.props.navigation.navigate("DeviceInfoPage");
                                     }}
                    />
                    {/*帮助与反馈*/}
                    {/*<SettingItemView title={stringsTo("help_callback")}*/}
                    {/*bottomLine={SettingItemView.LINE.NONE}*/}
                    {/*// type={SettingItemView.TYPE.NONE}*/}
                    {/*onPress={_ => {*/}
                    {/*IMIGotoPage.starNativeFeedbackPage(LetDevice.deviceID);*/}
                    {/*}}*/}
                    {/*/>*/}
                    {/*常见问题*/}
                    <SettingItemView title={stringsTo("comm_setting_faq")}
                                     bottomLine={SettingItemView.LINE.NONE}
                                     type={SettingItemView.TYPE.NONE}
                                     onPress={_ => {
                                         this.props.navigation.push('WebViewPage', {url: this.FAQ_URL})
                                     }}
                    />
                </View>
            </View>
        );
    }

}


