import React from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Dimensions,
    Modal,
    TouchableOpacity,
    TouchableWithoutFeedback,
    Alert,
    FlatList,
    Image,
    PanResponder,
    ImageBackground,
} from 'react-native';
import {imiThemeManager, colors, showToast, showLoading} from 'imilab-design-ui'
import ChoiceItem from "../../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem"
import ListItem from "../../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import I18n from '../../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText} from "react-native-easy-app";
import {TinyWindowLivePlayer} from '../../../../../imilab-modules/com.chuangmi.camera.moudle'
import RoundedButtonView
    from "../../../../../imilab-design-ui/src/widgets/settingUI/RoundedButtonView";
import {stringsTo} from "../../../../../globalization/Localize";
import HomePageLivePlayerComponent
    from "../../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import {RNLine} from 'imilab-design-ui';
import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";

import {
    IMICameraVideoView,
    IMIVideoView,
    LetDevice,
    LetIProperties,
    PlayerClass,
    CameraMethod,
    IntercomAudioFormatKey,
    IntercomModeKey,
    INTERCOM_MODE,

} from '../../../../../imilab-rn-sdk';
import {PLAYER_EVENT_CODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";


let windowWidth = Dimensions.get('window').width;
let windowHeight = Dimensions.get('window').height;

let minWidth = 64 / 320 * (windowWidth - 28);
let Height = (windowWidth - 28) * 187 / 332;
let minHeight = 64 / 192 * Height;
let area_Width = 320;
let area_Height = 192;

const BACKGROUNDCOLOR = '#4A6EE019';
const UNCHECKED_BACKGROUNDCOLOR = '#F2F3F5';

const UNCHECKED_TITLECOLOR = '#343434';
const CHECKED_TITLECOLOR = '#4A6EE0';

const PLAYER_TYPE = PlayerClass.LIVE;


export default class HuadingKeyArea extends BaseDeviceComponent {

    static propTypes = {};

    constructor(props, context) {
        super(props, context);
        this.myPanResponder = {};
        this.leftTopPanResponder = {};
        this.rightTopPanResponder = {};
        this.leftBottomPanResponder = {};
        this.rightBottomPanResponder = {};
        this.cameraGLView = null;
        this.state = {
            refer: false,
            checkValue: false,
            minX: -5,
            minY: -5,
            maxX: (windowWidth - 28),
            maxY: Height,
            selectedIndex: -1,
            addImgIndex: -1,
            drawColor: '#496EE0',
            data: [],
            areaArr: [],
            paramArr: [],
            isShow: false,//是否显示编辑框
            isEditArea: false,//是否处于编辑状态
            isShowAddBtn:true,// 是否显示添加按钮
            // imgPath:'',//图片路径

            isShowAreaFirst: true,//重点区域1
            firstLeft: 0,
            firstTop: 0,
            firstWidth: 0,
            firstHeight: 0,

            isShowAreaSec: true,//重点区域2
            secLeft: 0,
            secTop: 0,
            secWidth: 0,
            secHeight: 0,

            isShowAreaThd: true,//重点区域3
            thdLeft: 0,
            thdTop: 0,
            thdWidth: 0,
            thdHeight: 0,

            isShowAreaForth: true,//重点区域4
            forthLeft: 0,
            forthTop: 0,
            forthWidth: 0,
            forthHeight: 0,
        }
    }

    componentDidMount() {
        this._initVideoPrepare();
        this.getAllValue();
        // this.getData();
    }

    getData() {
        // var data = [{"y1":0.25,"x1":0.10,"y2":0.25,"x2":0.3,"y3":0.6,"x3":0.6,"y4":0.6,"x4":0.10,"bgColor":'#496EE0'},
        //             {"y1":0.4,"x1":0.5,"y2":0.4,"x2":0.5,"y3":0.5,"x3":0.5,"y4":0.5,"x4":0.50,"bgColor":'#496EE0'}];

        // var  data = [];
        // for (var i=0; i<4; i++) {
        //     // data[i] = {key: i, title: '第' + i + '行', index:i}
        //   data[i] = {"y1":0.25,"x1":0.10,"y2":0.25,"x2":0.6,"y3":0.6,"x3":0.6,"y4":0.6,"x4":0.10};
        // }
        // this.setState({ data: data });

        this.getAllValue();
    }

    //获取数据
    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data) => {
            showLoading(false);
            console.log(JSON.parse(data));
            let dataObject = JSON.parse(data)
            let stateProps = {};

            if (dataObject.AlarmDetectionArea) {
                stateProps.areaArr = dataObject.AlarmDetectionArea.value;
                stateProps.paramArr = dataObject.AlarmDetectionArea.value;

                if (stateProps.areaArr.length == 0) {
                    stateProps.isShow = false;
                    stateProps.drawColor = '#496EE0';
                    stateProps.isShowAreaFirst = false;
                    stateProps.isShowAreaSec = false;
                    stateProps.isShowAreaThd = false;
                    stateProps.isShowAreaForth = false;
                    stateProps.addImgIndex = 0;
                    stateProps.isShowAddBtn = true;
                    console.log('重点区域数组无值');
                } else {
                    stateProps.isShow = false;
                    if (stateProps.areaArr.length == 1) {
                        stateProps.isShowAreaFirst = true;
                        stateProps.isShowAreaSec = false;
                        stateProps.isShowAreaThd = false;
                        stateProps.isShowAreaForth = false;
                        stateProps.addImgIndex = 1;
                        stateProps.isShowAddBtn = true;
                        // stateProps.imgPath = `${IMIFile.storageBasePath}/${0}/area_snapshot.jpg`;

                        stateProps.firstLeft = parseFloat(stateProps.areaArr[0].x1) * (windowWidth - 28);
                        stateProps.firstTop = parseFloat(stateProps.areaArr[0].y1) * Height;
                        stateProps.firstWidth = (parseFloat(stateProps.areaArr[0].x2) - parseFloat(stateProps.areaArr[0].x1)) * (windowWidth - 28);
                        stateProps.firstHeight = (parseFloat(stateProps.areaArr[0].y4) - parseFloat(stateProps.areaArr[0].y1)) * Height;

                    } else if (stateProps.areaArr.length == 2) {
                        stateProps.isShowAreaFirst = true;
                        stateProps.isShowAreaSec = true;
                        stateProps.isShowAreaThd = false;
                        stateProps.isShowAreaForth = false;
                        stateProps.addImgIndex = 2;
                        stateProps.isShowAddBtn = true;
                        // stateProps.imgPath = `${IMIFile.storageBasePath}/${1}/area_snapshot.jpg`;


                        stateProps.firstLeft = parseFloat(stateProps.areaArr[0].x1) * (windowWidth - 28);
                        stateProps.firstTop = parseFloat(stateProps.areaArr[0].y1) * Height;
                        stateProps.firstWidth = (parseFloat(stateProps.areaArr[0].x2) - parseFloat(stateProps.areaArr[0].x1)) * (windowWidth - 28);
                        stateProps.firstHeight = (parseFloat(stateProps.areaArr[0].y4) - parseFloat(stateProps.areaArr[0].y1)) * Height;

                        stateProps.secLeft = parseFloat(stateProps.areaArr[1].x1) * (windowWidth - 28);
                        stateProps.secTop = parseFloat(stateProps.areaArr[1].y1) * Height;
                        stateProps.secWidth = (parseFloat(stateProps.areaArr[1].x2) - parseFloat(stateProps.areaArr[1].x1)) * (windowWidth - 28);
                        ;
                        stateProps.secHeight = (parseFloat(stateProps.areaArr[1].y4) - parseFloat(stateProps.areaArr[1].y1)) * Height;

                    } else if (stateProps.areaArr.length == 3) {
                        stateProps.isShowAreaFirst = true;
                        stateProps.isShowAreaSec = true;
                        stateProps.isShowAreaThd = true;
                        stateProps.isShowAreaForth = false;
                        stateProps.addImgIndex = 3;
                        stateProps.isShowAddBtn = true;
                        // stateProps.imgPath = `${IMIFile.storageBasePath}/${2}/area_snapshot.jpg`;


                        stateProps.firstLeft = parseFloat(stateProps.areaArr[0].x1) * (windowWidth - 28);
                        stateProps.firstTop = parseFloat(stateProps.areaArr[0].y1) * Height;
                        stateProps.firstWidth = (parseFloat(stateProps.areaArr[0].x2) - parseFloat(stateProps.areaArr[0].x1)) * (windowWidth - 28);
                        stateProps.firstHeight = (parseFloat(stateProps.areaArr[0].y4) - parseFloat(stateProps.areaArr[0].y1)) * Height;

                        stateProps.secLeft = parseFloat(stateProps.areaArr[1].x1) * (windowWidth - 28);
                        stateProps.secTop = parseFloat(stateProps.areaArr[1].y1) * Height;
                        stateProps.secWidth = (parseFloat(stateProps.areaArr[1].x2) - parseFloat(stateProps.areaArr[1].x1)) * (windowWidth - 28);
                        stateProps.secHeight = (parseFloat(stateProps.areaArr[1].y4) - parseFloat(stateProps.areaArr[1].y1)) * Height;

                        stateProps.thdLeft = parseFloat(stateProps.areaArr[2].x1) * (windowWidth - 28);
                        ;
                        stateProps.thdTop = parseFloat(stateProps.areaArr[2].y1) * Height;
                        stateProps.thdWidth = (parseFloat(stateProps.areaArr[2].x2) - parseFloat(stateProps.areaArr[2].x1)) * (windowWidth - 28);
                        stateProps.thdHeight = (parseFloat(stateProps.areaArr[2].y4) - parseFloat(stateProps.areaArr[2].y1)) * Height;

                    } else if (stateProps.areaArr.length == 4) {

                        stateProps.isShowAreaFirst = true;
                        stateProps.isShowAreaSec = true;
                        stateProps.isShowAreaThd = true;
                        stateProps.isShowAreaForth = true;
                        stateProps.addImgIndex = 3;
                        stateProps.isShowAddBtn = false;

                        stateProps.firstLeft = parseFloat(stateProps.areaArr[0].x1) * (windowWidth - 28);
                        stateProps.firstTop = parseFloat(stateProps.areaArr[0].y1) * Height;
                        stateProps.firstWidth = (parseFloat(stateProps.areaArr[0].x2) - parseFloat(stateProps.areaArr[0].x1)) * (windowWidth - 28);
                        stateProps.firstHeight = (parseFloat(stateProps.areaArr[0].y4) - parseFloat(stateProps.areaArr[0].y1)) * Height;

                        stateProps.secLeft = parseFloat(stateProps.areaArr[1].x1) * (windowWidth - 28);
                        stateProps.secTop = parseFloat(stateProps.areaArr[1].y1) * Height;
                        stateProps.secWidth = (parseFloat(stateProps.areaArr[1].x2) - parseFloat(stateProps.areaArr[1].x1)) * (windowWidth - 28);
                        stateProps.secHeight = (parseFloat(stateProps.areaArr[1].y4) - parseFloat(stateProps.areaArr[1].y1)) * Height;

                        stateProps.thdLeft = parseFloat(stateProps.areaArr[2].x1) * (windowWidth - 28);
                        stateProps.thdTop = parseFloat(stateProps.areaArr[2].y1) * Height;
                        stateProps.thdWidth = (parseFloat(stateProps.areaArr[2].x2) - parseFloat(stateProps.areaArr[2].x1)) * (windowWidth - 28);
                        stateProps.thdHeight = (parseFloat(stateProps.areaArr[2].y4) - parseFloat(stateProps.areaArr[2].y1)) * Height;

                        stateProps.forthLeft = parseFloat(stateProps.areaArr[3].x1) * (windowWidth - 28);
                        stateProps.forthTop = parseFloat(stateProps.areaArr[3].y1) * Height;
                        stateProps.forthWidth = (parseFloat(stateProps.areaArr[3].x2) - parseFloat(stateProps.areaArr[3].x1)) * (windowWidth - 28);
                        stateProps.forthHeight = (parseFloat(stateProps.areaArr[3].y4) - parseFloat(stateProps.areaArr[3].y1)) * Height;
                    }
                    console.log('重点区域数组有值');
                }
            }
            console.log('statprops--' + JSON.stringify(stateProps));
            // 统一设置从设备端获取的值
            this.setState(stateProps);
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    componentWillUnmount() {
        LetDevice.removeInfoChangeListener();
        LetIProperties.removePropertyChangeListener();
        this.toastTimer && clearTimeout(this.toastTimer);
        this.timer && clearTimeout(this.timer);
        if (this.cameraGLView) {
            this.cameraGLView.destroy();
        }
    }

    UNSAFE_componentWillMount() {
        this.zoomLastDistance = 0;
        this.myPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // console.log(`onStartShouldSetPanResponder ${ isMulti }`);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // // console.log("onMoveShouldSetPanResponder:" + isMulti);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            },
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                // console.log('000000');
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //   return false;
                // }
            },
            onPanResponderTerminationRequest: (evt, gestureState) => false,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                this._top = this.state.minY;
                this._left = this.state.minX;
                this.forceUpdate();
                if (evt.nativeEvent.changedTouches.length > 1) { // 双指时的中心点
                    let distantX = evt.nativeEvent.changedTouches[1].pageX - evt.nativeEvent.changedTouches[0].pageX;
                    let distantY = evt.nativeEvent.changedTouches[1].pageY - evt.nativeEvent.changedTouches[0].pageY;
                    this.touchDownDistant = Math.sqrt(distantX * distantX + distantY * distantY);
                    // console.log("双指:downDistance" + this.touchDownDistant);
                }
            },
            onPanResponderMove: (evt, gestureState) => {
                let isMulti = evt.nativeEvent.changedTouches.length > 1;

                // console.log(`onPanResponderMove ${ isMulti }`);
                if (isMulti) {
                    let minX = 0;
                    let maxX = 0;
                    if (evt.nativeEvent.changedTouches[0].locationX > evt.nativeEvent.changedTouches[1].locationX) {
                        minX = evt.nativeEvent.changedTouches[1].pageX;
                        maxX = evt.nativeEvent.changedTouches[0].pageX;
                    } else {
                        minX = evt.nativeEvent.changedTouches[0].pageX;
                        maxX = evt.nativeEvent.changedTouches[1].pageX;
                    }

                    let minY = 0;
                    let maxY = 0;
                    if (evt.nativeEvent.changedTouches[0].locationY > evt.nativeEvent.changedTouches[1].locationY) {
                        minY = evt.nativeEvent.changedTouches[1].pageY;
                        maxY = evt.nativeEvent.changedTouches[0].pageY;
                    } else {
                        minY = evt.nativeEvent.changedTouches[0].pageY;
                        maxY = evt.nativeEvent.changedTouches[1].pageY;
                    }
                    const widthDistance = maxX - minX;
                    const heightDistance = maxY - minY;
                    const diagonalDistance = Math.sqrt(widthDistance * widthDistance + heightDistance * heightDistance);
                    this.zoomCurrentDistance = Number.parseInt(diagonalDistance);
                    if (this.zoomLastDistance == 0) {
                        this.zoomLastDistance = this.zoomCurrentDistance;
                        this.touchDownDistant = this.zoomCurrentDistance;
                        // console.log("双指刚放下");

                    } else {
                        let diff = this.zoomCurrentDistance - this.zoomLastDistance;
                        let diffScale = Number(((diff / 100)).toFixed(2));
                        // console.log(`双指:缩放 zoomCurrentDistance:${ this.zoomCurrentDistance } zoomLastDistance:${ this.zoomLastDistance } touchDownDistance ${ this.touchDownDistant } diff:${ diff } diffScale${ diffScale }`);
                        this.onScaleChanged(diffScale);
                        this.zoomLastDistance = this.zoomCurrentDistance;
                    }
                } else {
                    let minX = this._left + gestureState.dx;
                    minX = minX < -5 ? -5 : minX;
                    minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                    let minY = this._top + gestureState.dy;
                    minY = minY < -5 ? -5 : minY;
                    minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                    this.setState({minX: minX, minY: minY});
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                this.zoomLastDistance = 0;
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minX: minX, minY: minY, maxX: maxX, maxY: maxY});
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                // let minX = this._left + gestureState.dx;
                // minX = minX < -5 ? -5 : minX;
                // minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minY: minY, maxX: maxX, maxY: maxY});
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                // let minY = this._top + gestureState.dy;
                // minY = minY < -5 ? -5 : minY;
                // minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minX: minX, maxX: maxX, maxY: maxY});
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({maxX: maxX, maxY: maxY});
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
    }

    onScaleChanged(diffScale) {
        let width = this.state.maxX;
        let height = this.state.maxY;
        width = width * (diffScale + 1);
        height = height * (diffScale + 1);
        width = width > (windowWidth - 28) ? (windowWidth - 28) : width;
        height = height > Height ? Height : height;
        width = width < minWidth ? minWidth : width;
        height = height < minHeight ? minHeight : height;
        let minX = ((windowWidth - 28 - 5) - width) / 2;
        minX = minX > 0 ? minX : -5;
        let minY = (Height - height) / 2;
        minY = minY > 0 ? minY : -5;
        this.setState({
            minX: minX,
            minY: minY,
            maxX: width,
            maxY: height
        });
    }


    render() {
        global.navigation = this.props.navigation;
        return (<View style={styles.container}>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                title={I18n.t('huadingKeyAreaStr')}
                left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                right={[]}
                rightText={{title:this.state.isEditArea ? I18n.t('imi_save') : null,onPress:_=> this.snapImg(),disable:false}}
            />

            <ScrollView showsVerticalScrollIndicator={false}>
                <XText style={styles.functionSettingStyle} allowFontScaling={false}
                       text={I18n.t('huadingKeyAreaSubStr')}
                       numberOfLines={2}/>
                <View style={styles.videoContainerNormal}>
                    {this._renderVideoLayout()}
                    {this._renderVideoView()}
                    {this._renderGestureLayoutView()}
                </View>
                <View style={styles.container}>
                    {this._renderAreaListView()}
                    {/*{this._renderAddBtn()}*/}
                </View>
            </ScrollView>

            <View style={{width: "100%", position: "absolute", bottom: 0}}>
                {this._renderAddBtn()}
            </View>
        </View>)
    }
    // 返回上一页
    _onPressBack = () => {
        if (this.props.route.params.callback) {
            console.log('数组长度---',this.state.areaArr.length);
            this.props.route.params.callback(this.state.areaArr.length);
        }
        navigation.pop();
    };

    //截图
    snapImg() {
        const screenShotPath = `${IMIFile.storageBasePath}/${this.state.addImgIndex}/area_snapshot.jpg`;
        console.log("  screenShotPath " + screenShotPath + " IMIVideoView  " + this.cameraGLView);
        this.cameraGLView.screenShot(screenShotPath).then(_ => {
            IMIFile.saveImageToPhotosAlbum(screenShotPath, LetDevice.deviceID).then(_ => {
                // alert("保存成功");
                this.saveArea();

                // this.setState({
                //     imgPath:screenShotPath
                // },()=>{
                //     console.log('截图成功路径--',screenShotPath);
                //     console.log('图片显示路径--',this.state.imgPath);
                //     this.saveArea();
                // })
                // let imgPath = `${IMIFile.storageBasePath}/${index}/area_snapshot.jpg`;
            }).catch(code => {
                showToast("code" + JSON.stringify(code));
            });
        })
    }

    //保存重点区域
    saveArea() {
        console.log('保存-');
        let x_start = parseInt(this.state.minX * area_Width / (windowWidth - 28)) > 0 ? parseInt(this.state.minX * area_Width / (windowWidth - 28)) : 0;
        let y_start = parseInt(this.state.minY * area_Height / Height) > 0 ? parseInt(this.state.minY * area_Height / Height) : 0;
        let x_end = parseInt((this.state.minX > 0 ? this.state.minX + this.state.maxX : 0 + this.state.maxX) * area_Width / (windowWidth - 28));
        let y_end = parseInt((this.state.minY > 0 ? this.state.minY + this.state.maxY : 0 + this.state.maxY) * area_Height / Height);
        console.log('x-start--', x_start);
        console.log('y-start--', y_start);
        console.log('x-end--', x_end);
        console.log('y-end--', y_end);
        let x_start_scale = parseFloat(parseFloat(x_start / area_Width).toFixed(2));
        let x_end_scale = parseFloat(parseFloat(x_end / area_Width).toFixed(2));
        let y_start_scale = parseFloat(parseFloat(y_start / area_Height).toFixed(2));
        let y_end_scale = parseFloat(parseFloat(y_end / area_Height).toFixed(2));

        console.log('x-x_start_scale--', x_start_scale);
        console.log('y-x_end_scale--', x_end_scale);
        console.log('y-start_scale--', y_start_scale);
        console.log('y-end_scale--', y_end_scale);
        console.log('x1_start', this.state.minX);
        console.log('y1_start', this.state.minY);
        console.log('x2_end', this.state.maxX);
        console.log('y2_end', this.state.maxY);

        let addData = {
            "x1": x_start_scale,
            "y1": y_start_scale,
            "x2": x_end_scale > 0.96 ? 1 : x_end_scale,
            "y2": y_start_scale > 0.96 ? 1 : y_start_scale,
            "x3": x_end_scale > 0.96 ? 1 : x_end_scale,
            "y3": y_end_scale > 0.96 ? 1 : y_end_scale,
            "x4": x_start_scale,
            "y4": y_end_scale > 0.96 ? 1 : y_end_scale
        };

        if (this.state.isEditArea == true) {
            //编辑已设置的重点区域
            this.state.paramArr.splice(this.state.selectedIndex, 1, addData);
        } else {
            //新增
            this.state.paramArr.push(addData);
        }
        // this.state.paramArr.push(addData);
        console.log('addData---', addData);
        console.log('allArr---', this.state.areaArr);
        // let params = {AlarmDetectionArea:this.state.paramArr};
        showLoading(stringsTo('commWaitText'), true);
        let params = {AlarmDetectionArea: this.state.paramArr};
        console.log('params---', params);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            console.log('添加重点区域成功');
            this.toastTimer = setTimeout(
                () => {
                    showLoading(false);
                    this.getAllValue();
                },
                5000
            );

        }).catch((error) => {
            console.log(JSON.stringify(error));
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });

    }

    //看护区域
    _renderVideoLayout() {
        return (
            <TinyWindowLivePlayer
                videoRef={this._assignRoot}
                playerMarginHorizontal={14}
            />
        )
    }

    _assignRoot = (component) => {
        this.cameraGLView = component;
    };

    //初始化VideoView
    _initVideoPrepare() {
        console.log(`_initVideoPrepare ${this.cameraGLView}`);
        this.cameraGLView.prepare();
    }

    /***   ---------------------------    监听器 start      --------------------------   *****/


    //看护区域View
    _renderVideoView() {
        return (
            <View style={{
                // position: "relative"// absolute以第一个非static的父类变量作为定位标准
                position: 'absolute',
            }}>
                {/*// <View>*/}
                {this._showKeyAreaOne()}
                {this._showKeyAreaTwo()}
                {this._showKeyAreaThree()}
                {this._showKeyAreaFour()}
            </View>
        );
    }

    //已经设置的重点区域
    _showKeyAreaOne() {
        if (this.state.isShowAreaFirst == false) {
            return null;
        }
        return (
            <View>
                <View style={{
                    backgroundColor: '#496EE0',
                    opacity: 0.5,
                    marginLeft: this.state.firstLeft,
                    marginTop: this.state.firstTop,
                    width: this.state.firstWidth,
                    height: this.state.firstHeight,
                    position: 'absolute',
                }}>
                </View>
            </View>
        );
    }

    _showKeyAreaTwo() {
        if (this.state.isShowAreaSec == false) {
            return null;
        }
        return (
            <View>
                <View style={{
                    backgroundColor: '#F19149',
                    opacity: 0.5,
                    marginLeft: this.state.secLeft,
                    marginTop: this.state.secTop,
                    width: this.state.secWidth,
                    height: this.state.secHeight,
                    position: 'absolute',
                }}>
                </View>
            </View>
        );
    }

    _showKeyAreaThree() {
        if (this.state.isShowAreaThd == false) {
            return null;
        }
        return (
            <View>
                <View style={{
                    backgroundColor: '#009944',
                    opacity: 0.5,
                    marginLeft: this.state.thdLeft,
                    marginTop: this.state.thdTop,
                    width: this.state.thdWidth,
                    height: this.state.thdHeight,
                    position: 'absolute',
                }}>
                </View>
            </View>
        );
    }

    _showKeyAreaFour() {
        if (this.state.isShowAreaForth == false) {
            return null;
        }
        return (
            <View>
                <View style={{
                    backgroundColor: '#00B7EE',
                    opacity: 0.5,
                    marginLeft: this.state.forthLeft,
                    marginTop: this.state.forthTop,
                    width: this.state.forthWidth,
                    height: this.state.forthHeight,
                    position: 'absolute',
                }}>
                </View>
            </View>
        );
    }

    _renderGestureLayoutView() {
        //不是显示状态不显示
        if (this.state.isShow == false) {
            return null;
        }
        return (<View style={{
            position: 'absolute',
            flexDirection: 'column',
            width: '100%',
            height: '100%'
            // borderTopLeftRadius:10,
            // borderTopRightRadius:10,
            // borderRadius: 10,
            // marginLeft: 14,
            // marginRight: 14,
            // height: height, lineHeight: height,
        }}>
            <View style={{
                marginTop: this.state.minY, flexDirection: 'row', zIndex: 2
            }}>
                <View style={{
                    marginLeft: this.state.minX,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }} hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} {...this.leftTopPanResponder.panHandlers}/>
                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.maxX - 10,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }}{...this.rightTopPanResponder.panHandlers}/>
            </View>
            <View style={{
                zIndex: 1,
                marginTop: -5,
                marginLeft: this.state.minX + 5,
                width: this.state.maxX,
                height: this.state.maxY,
                backgroundColor: this.state.drawColor,
                opacity: 0.5
            }} {...this.myPanResponder.panHandlers}/>
            <View style={{
                marginTop: -5, flexDirection: 'row', zIndex: 2
            }}>
                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.minX,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }}{...this.leftBottomPanResponder.panHandlers}/>
                <View hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} style={{
                    marginLeft: this.state.maxX - 10,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }}{...this.rightBottomPanResponder.panHandlers}/>
            </View>
        </View>);
    }

    //重点区域列表
    _renderAreaListView() {
        return (
            <View style={styles.container}>
                <FlatList ref={(flatList) => this._flatList = flatList}
                          style={{backgroundColor: '#fff', flex: 1,}}
                    // data={this.state.data}
                          data={this.state.areaArr}
                    // renderItem={({item, index}) => this._renderItemView(item)}
                    // keyExtractor={(item, index) => `key_${ index }`}
                          renderItem={({item, index}) => this._renderItemView(item, index)}
                          keyExtractor={(item, index) => index}

                    // horizontal = {true} 将列表横着放
                          refreshing={this.state.refer}
                          onRefresh={false}
                          onEndReachedThreshold={-0.05}
                          onEndReached={(info) => {
                              // Alert.alert("滑动到底部了");
                          }}
                />
            </View>)
    }

    //Item
    //  _renderItemView(item) {
    _renderItemView(item, index) {
        let bgColor;
        let imgPath;
        if (index === 0) {
            bgColor = '#496EE0';
            imgPath = `${IMIFile.storageBasePath}/${index}/area_snapshot.jpg`;
            console.log('第一张---',imgPath);
        } else if (index === 1) {
            bgColor = '#f19149';
            imgPath = `${IMIFile.storageBasePath}/${index}/area_snapshot.jpg`;
            console.log('第二张---',imgPath);
        } else if (index === 2) {
            bgColor = '#009944';
            imgPath = `${IMIFile.storageBasePath}/${index}/area_snapshot.jpg`;
            console.log('第三张---',imgPath);
        } else if (index === 3) {
            bgColor = '#00B7EE';
            imgPath = `${IMIFile.storageBasePath}/${index}/area_snapshot.jpg`;
            console.log('第四张---',imgPath);
        }

        let imageSource = require('../../../res/icon_area_delete.png')
        console.log('sourceStr====', imageSource);
        let fileStr = IMIFile.storageBasePath;
        // let imgPath = `${IMIFile.storageBasePath}/${item.index}/area_snapshot.jpg`;
        console.log('pathStr===' + imgPath);
        let whetherSel = (this.state.selectedIndex === index);//
        return (
            //styles.container
            <View style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                marginLeft: 14,
                marginRight: 14,
                marginTop: 0,
                marginBottom: 14,
                height: 60,
                borderRadius: 10,
                backgroundColor: whetherSel ? BACKGROUNDCOLOR : UNCHECKED_BACKGROUNDCOLOR
            }}>
                <TouchableWithoutFeedback
                    style={{flex: 1, alignItems: 'center', justifyContent: 'center',}}
                    onPress={() => {
                        // console.log('当前idx = ' + item.index);
                        console.log('当前idx = ' + index);

                        console.log('当前state idx = ' + this.state.selectedIndex);
                        if (whetherSel === false) {
                            console.log('是否设置');
                            // this.setState({selectedIndex: item.index})
                            // this.setState({selectedIndex: index})
                            this.setState({selectedIndex: index}, () => {
                                console.log('最后选中state idx = ' + this.state.selectedIndex);
                                // this.updateSelectedItem(item.index);
                            });
                        }
                        this.updateSelectedItem(index);

                    }}>
                    <View style={{
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        height: 60
                    }}>
                        <View style={{
                            height: 60,
                            width: 107,
                            borderTopLeftRadius: 10,
                            borderBottomLeftRadius: 10
                        }}>
                            <Image style={{
                                width: '100%',
                                height: '100%',
                                borderTopLeftRadius: 10,
                                borderBottomLeftRadius: 10
                            }}
                                // source = {require('../../../resources/images/itembg.png')}
                                   source={{uri: imgPath}}
                            >
                            </Image>
                            <View style={{
                                position: "absolute",
                                backgroundColor: bgColor,
                                opacity: 0.5,
                                borderTopLeftRadius: this.state.areaArr[index].x1 == 0 && this.state.areaArr[index].y1 == 0 ? 10 : 0,
                                borderBottomLeftRadius: this.state.areaArr[index].x4 == 0 && this.state.areaArr[index].y4 == 1 ? 10 : 0,
                                marginLeft: parseFloat(this.state.areaArr[index].x1) * 107,
                                marginTop: parseFloat(this.state.areaArr[index].y1) * 60,
                                width: (parseFloat(this.state.areaArr[index].x2) - parseFloat(this.state.areaArr[index].x1)) * 107,
                                height: (parseFloat(this.state.areaArr[index].y4) - parseFloat(this.state.areaArr[index].y1)) * 60
                            }}>
                            </View>
                        </View>
                        <View style={{
                            height: 60,
                            width: windowWidth - 107 - 40 - 20 - 28,
                            marginLeft: 20
                        }}>
                            <Text style={{
                                color: whetherSel ? CHECKED_TITLECOLOR : UNCHECKED_TITLECOLOR,
                                fontSize: 15,
                                fontWeight: 'bold',
                                width: '100%',
                                height: '100%',
                                lineHeight: 58
                            }}>{I18n.t('keyArea') + (index + 1) + I18n.t('keyAreaNum')}{item.title}</Text>
                        </View>
                        <View style={{
                            height: 60,
                            width: 40,
                            borderTopRightRadius: 10,
                            borderBottomRightRadius: 10,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <TouchableOpacity
                                onPress={() => {
                                    // console.log('当前idx =--- ' + item.index);
                                    console.log('当前idx =--- ' + index);
                                    this.deleteItem(index);
                                }}>
                                <Image style={{width: 20, height: 20}}
                                       source={imageSource}>
                                </Image>
                            </TouchableOpacity>
                        </View>

                    </View>
                </TouchableWithoutFeedback>
            </View>
        )
    };

    //删除选中item
    deleteItem(index) {
        if (index != this.state.selectedIndex) {
            showToast(I18n.t('onSelectAreaTip'));
            return;
        }
        this.deleteItemNew(index);

        // const screenShotPath = `${IMIFile.storageBasePath}/${this.state.addImgIndex}/area_snapshot.jpg`;
        // console.log("  screenShotPath " + screenShotPath + " IMIVideoView  " + this.cameraGLView);
        // this.cameraGLView.screenShot(screenShotPath).then(_ => {
        //     IMIFile.deleteImageToPhotosAlbum(screenShotPath, LetDevice.deviceID).then(_ => {
        //         alert("删除成功");
        //         this.deleteItemNew(index);
        //     }).catch(code => {
        //         console.log('删除失败');
        //         showToast("code" + JSON.stringify(code));
        //     });
        // })
    }

    deleteItemNew(index) {
        let result;
        let params;
        console.log('删除前数组---', this.state.paramArr);
        if (this.state.paramArr.length == 1) {
            params = {AlarmDetectionArea: []};
        } else {
            result = this.state.paramArr.splice(index, 1);
            params = {AlarmDetectionArea: this.state.paramArr};
        }
        console.log('删除后数组---', this.state.paramArr);
        console.log('删除后--result---', result);
        console.log('删除---params---', params);

        showLoading(stringsTo('commWaitText'), true);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            console.log('删除成功');
            this.timer = setTimeout(
                () => {
                    showLoading(false);
                    this.setState({isEditArea:false});
                    this.getAllValue();
                },
                5000
            );

        }).catch(() => {
            console.log('删除失败');
            showToast(I18n.t('delete_failed'));
            showLoading(false);
        });
    }

    //点击选中item所做的处理
    updateSelectedItem(index) {
        console.log('000----选中第几个---', index);
        console.log('选中----选中第几个---', this.state.selectedIndex);
        let x = parseFloat(this.state.areaArr[index].x1) * (windowWidth - 28);
        let y = parseFloat(this.state.areaArr[index].y1) * Height;
        let width = (parseFloat(this.state.areaArr[index].x2) - parseFloat(this.state.areaArr[index].x1)) * (windowWidth - 28);
        let height = (parseFloat(this.state.areaArr[index].y4) - parseFloat(this.state.areaArr[index].y1)) * Height;

        console.log('x-xone--', x);
        console.log('yyone--', y);
        console.log('xwh--', width);
        console.log('xht-===', height);

        this.setState({
            minX: x == 0 ? -5 : x,
            minY: y == 0 ? -5 : y,
            maxX: width >= minWidth ? width : minWidth,
            maxY: height >= minHeight ? height : minHeight,
            isEditArea: true,
        });

        console.log('--x---', x);
        console.log('--y--', y);
        console.log('--wd--', this.state.maxX);
        console.log('--ht-===', this.state.maxY);
        if (index == 0) {
            //选中第一个
            if (this.state.areaArr.length == 1) {
                this.setState({isShowAreaFirst: false, drawColor: '#496EE0', isShow: true});
            } else if (this.state.areaArr.length == 2) {
                this.setState({
                    isShowAreaFirst: false,
                    isShowAreaSec: true,
                    drawColor: '#496EE0',
                    isShow: true
                });
            } else if (this.state.areaArr.length == 3) {
                this.setState({
                    isShowAreaFirst: false,
                    isShowAreaSec: true,
                    isShowAreaThd: true,
                    drawColor: '#496EE0',
                    isShow: true
                });
            } else if (this.state.areaArr.length == 4) {
                this.setState({
                    isShowAreaFirst: false,
                    isShowAreaSec: true,
                    isShowAreaThd: true,
                    isShowAreaForth: true,
                    drawColor: '#496EE0',
                    isShow: true
                });
            }
        } else if (index == 1) {
            //选中第二个
            if (this.state.areaArr.length == 2) {
                this.setState({
                    isShowAreaFirst: true,
                    isShowAreaSec: false,
                    drawColor: '#F19149',
                    isShow: true
                });
            } else if (this.state.areaArr.length == 3) {
                this.setState({
                    isShowAreaFirst: true,
                    isShowAreaSec: false,
                    isShowAreaThd: true,
                    drawColor: '#F19149',
                    isShow: true
                });
            } else if (this.state.areaArr.length == 4) {
                this.setState({
                    isShowAreaFirst: true,
                    isShowAreaSec: false,
                    isShowAreaThd: true,
                    isShowAreaForth: true,
                    drawColor: '#F19149',
                    isShow: true
                });
            }
        } else if (index == 2) {
            //选中第三个
            if (this.state.areaArr.length == 3) {
                this.setState({
                    isShowAreaFirst: true,
                    isShowAreaSec: true,
                    isShowAreaThd: false,
                    drawColor: '#009944',
                    isShow: true
                });
            } else if (this.state.areaArr.length == 4) {
                this.setState({
                    isShowAreaFirst: true,
                    isShowAreaSec: true,
                    isShowAreaThd: false,
                    isShowAreaForth: true,
                    drawColor: '#009944',
                    isShow: true
                });
            }
        } else if (index == 3) {
            //选中第四个
            this.setState({
                isShowAreaFirst: true,
                isShowAreaSec: true,
                isShowAreaThd: true,
                isShowAreaForth: false,
                drawColor: '#00B7EE',
                isShow: true
            });
     }
    };

    _renderAddBtn() {
        if (this.state.isShowAddBtn === false){
            return null;
        }
        return (
            <View>
                <RoundedButtonView buttonText={I18n.t('addBtnStr')}
                                   disabled={this.state.buttonDisabled}
                                   buttonTextStyle={{color: this.state.buttonTextColor}}
                                   buttonStyle={{
                                       height: 45,
                                       margin: 14,
                                       // position: "absolute",// absolute以第一个非static的父类变量作为定位标准
                                       borderRadius: 22.5,
                                       backgroundColor: imiThemeManager.theme.primaryColor
                                   }}
                                   onPress={() => {
                                       console.log('添加重点区域')
                                       // navigation.push('FocusAreaDetectionSetting');
                                       navigation.navigate('SaveKeyArea',{
                                           imgIndex:this.state.areaArr.length,
                                           allArr:this.state.areaArr,
                                           callback:(() => {
                                               console.log('更新数据');
                                               this.getAllValue();
                                           })
                                       })
                                   }}/>
            </View>
        )
    };

    //添加重点区域  并根据数组个数改变背景色值
    addArea() {
        if (this.state.areaArr.length == 1) {
            this.setState({drawColor: '#F19149', isShow: true, addImgIndex: 1});
            console.log('1--drawColor--', this.state.drawColor);
        } else if (this.state.areaArr.length == 2) {
            this.setState({drawColor: '#009944', isShow: true, addImgIndex: 2});
            console.log('2--drawColor--', this.state.drawColor);
        } else if (this.state.areaArr.length == 3) {
            this.setState({drawColor: '#00B7EE', isShow: true, addImgIndex: 3});
            console.log('3--drawColor--', this.state.drawColor);
        }
        this.setState({
            minX: -5,
            minY: -5,
            maxX: (windowWidth - 28),
            maxY: Height,
        });
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: imiThemeManager.theme.pageBg,
    },
    functionSettingStyle: {
        color: '#7F7F7F',
        fontSize: 14,
        lineHeight: 50,
        alignItems: 'center',
        textAlign: 'center',
        marginLeft: 20,
        marginRight: 20,
    },
    videoContainerNormal: {
        backgroundColor: '#F2F3F5',
        marginLeft: 14,
        marginRight: 14,
        height: Height,
        marginBottom: 14,
        // position: "relative"// absolute以第一个非static的父类变量作为定位标准
    },

    videoContainerFull: {
        backgroundColor: '#F2F3F5',
        width: "100%",
        height: "100%",
        position: "relative"// absolute以第一个非static的父类变量作为定位标准
    },

    videoView: {
        position: "absolute",
        width: "100%",
        height: "100%"
    },
    itemRight: {
        // marginRight: '10%',
        paddingRight: 14,
        width: "100%",
        height: "100%",
        flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'center',
        justifyContent: 'flex-end',
    },
    itemImgSize: {
        width: 20,
        height: 20,
    }

});
