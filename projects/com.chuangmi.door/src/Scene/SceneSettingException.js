import React from 'react';
import {
    StyleSheet,
    View,
    ScrollView,
    StatusBar,
    Dimensions, BackHandler,
} from "react-native";
import ListView from 'deprecated-react-native-listview'
import SingleLineOnlyTitleArrowCell from '../Lock/View/SingleLineOnlyTitleArrowCell';
import ListCards from "../Lock/View/ListCards";
import {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {imiAlarmEventCloudApi, IMIGotoPage, LetDevice, LetIMIIotRequest} from "../../../../imilab-rn-sdk";

let screenWidth = Dimensions.get('window').width;
let screenHeight = Dimensions.get('window').height;

const ANY_EXCEPTION = 0;
const VARIED_EXCEPTION = 1;//todo 组合平凡开门报警
const PWD_EXCEPTION = 2;
const FP_EXCEPTION = 3;
const NFC_EXCEPTION = 4;
const FACE_EXCEPTION = 5;
const PRY_DOOR_EXCEPTION = 9;
const DOOR_NOT_CLOSE_EXCEPTION = 23;
const FIRE_EXCEPTION = 26;
const PWD_HIJACK_EXCEPTION = 27;//todo 反劫持密码开门报警
const FP_HIJACK_EXCEPTION = 28;//todo 反劫持指纹开门报警

let ANY_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "localizedCompareValueName": stringsTo('any_exception'),
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "OtherSmartScenes",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data":[
            {
                "identifier": "OtherScenesType",
                "compareValue": 0,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('any_exception')
            }] /*[
            {
                "identifier": "WarningType",
                "compareValue": 3,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('pwd_exception')
            },
            {
                "identifier": "WarningType",
                "compareValue": 4,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('fp_exception')
            },
            {
                "identifier": "WarningType",
                "compareValue": 5,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('pry_exception')
            },
            {
                "identifier": "WarningType",
                "compareValue": 7,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('door_not_close_exception')
            },
            {
                "identifier": "WarningType",
                "compareValue": 8,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('nfc_exception')
            },
            {
                "identifier": "WarningType",
                "compareValue": 10,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('varied_exception')
            }
        ]*/
    }/*, {
        "identifier": "FireWarningEvent",//火警
    }*/]
}

let PWD_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 3,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('pwd_exception')
            }
        ]
    }]
}

let FP_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 4,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('fp_exception')
            }
        ]
    }]
}

let NFC_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 8,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('nfc_exception')
            }
        ]
    }]
}

let FACE_EXCEPTION_VALUE = {}

let VARIED_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 10,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('varied_exception')
            }
        ]
    }]
}

let PRY_DOOR_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 5,
                "compareType": "==",
                "localizedCompareValueName": stringsTo('pry_exception')
            }
        ]
    }]
}

let DOOR_NOT_CLOSE_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 7,
                "compareType": "==",
                "localizedCompareValueName":stringsTo('door_not_close_exception')
            }
        ]
    }]
}

let FIRE_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "FireWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "PushMessage",
                "compareValue": "",
                "compareType": "!=",
                "localizedCompareValueName": stringsTo('fire_exception')
            }
        ]
    }]
}

let PWD_HIJACK_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 1,
                "compareType": "==",
                "localizedCompareValueName":stringsTo('hijack_pwd_exception')
            }
        ]
    }]
}

let FP_HIJACK_EXCEPTION_VALUE = {
    "express": 0,//1:Or(任意) 0:And
    "scene": [{
        "express": 0,//1:Or(任意) 0:And
        "identifier": "LockWarningEvent",
        "type":3,//功能类型1（表示属性）；2（表示服务）；3（表示事件）
        "data": [
            {
                "identifier": "WarningType",
                "compareValue": 2,
                "compareType": "==",
                "localizedCompareValueName":stringsTo('hijack_fp_exception')
            }
        ]
    }]
}

let pwdDataArr = [];
let fingerDataArr = [];
let faceDataArr = [];
let nfcDataArr = [];

const TAG = "scene";
export default class SceneSettingException extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            allArray: [],
            dataSource: null
        };
    }


    componentDidMount() {


        console.log(TAG,"SceneSettingException this.props.route",this.props.route);

        //let extraInfo = JSON.parse(this.props.route.params.params.extraInfo);
        //console.log(TAG,"SceneSettingException extraInfo",extraInfo);

        this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
            IMIGotoPage.exit();
            return true;
        });

        this.getUserPwdInfo();

        //this.getTslList();
    }

    componentWillUnmount() {
        this.backHandler && this.backHandler.remove();
    }

    render() {

        this.refresh();

        return (

            <View style={styles.container}>
                <StatusBar backgroundColor={'transparent'}/>

                <NavigationBar type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                               title={stringsTo('auto_all_exception')}
                               left={[{
                                   key: NavigationBar.ICON.BACK, onPress: () => {
                                       //this.props.navigation.pop();
                                       IMIGotoPage.exit();
                                   }
                               }]}
                />

                <View style={{height: 1}}/>

                <ScrollView>
                    <View style={styles.container}>

                        <SingleLineOnlyTitleArrowCell
                            key="cell_0"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('any_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(ANY_EXCEPTION)
                            }
                        />
                        <View style={{height: 1, marginTop: 32}}></View>
                        <SingleLineOnlyTitleArrowCell
                            key="cell_1"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('fp_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(FP_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_2"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('pwd_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(PWD_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_3"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('nfc_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(NFC_EXCEPTION)
                            }
                        />

                        {/*<SingleLineOnlyTilteArrowCell*/}
                        {/*    key="cell_4"*/}
                        {/*    style={{width: screenWidth,height:60, backgroundColor: '#ffffff'}}*/}
                        {/*    title={LocalizedStrings.face_exception}*/}
                        {/*    arrowSource={require("../../res/sub_arrow.png")}*/}
                        {/*    showLine={true}*/}
                        {/*    onTouchUpInside={() =>*/}
                        {/*        this.sceneSelectRow(FACE_EXCEPTION)*/}
                        {/*    }*/}
                        {/*/>*/}

                        <SingleLineOnlyTitleArrowCell
                            key="cell_5"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('varied_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(VARIED_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_6"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('hijack_pwd_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(PWD_HIJACK_EXCEPTION)
                            }
                        />
                        <SingleLineOnlyTitleArrowCell
                            key="cell_7"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('hijack_fp_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(FP_HIJACK_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_8"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('door_not_close_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(DOOR_NOT_CLOSE_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_9"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('pry_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(PRY_DOOR_EXCEPTION)
                            }
                        />

                        <SingleLineOnlyTitleArrowCell
                            key="cell_10"
                            style={{width: screenWidth, height: 60, backgroundColor: '#ffffff'}}
                            title={stringsTo('fire_exception')}
                            arrowSource={require("../../res/sub_arrow.png")}
                            showLine={true}
                            onTouchUpInside={() =>
                                this.sceneSelectRow(FIRE_EXCEPTION)
                            }
                        />

                        {/*<View style={{height: 1, marginTop: 32}}></View>

                        <ListView dataSource={this.state.dataSource}
                                  renderRow={this.renderMover.bind(this)}/>*/}
                    </View>
                </ScrollView>
            </View>

        );
    }

    sceneSelectRow(event) {
        let readableMap = {};
        let trigger = {};
        let deviceName = " ";

        switch (event) {
            case ANY_EXCEPTION:
                readableMap = ANY_EXCEPTION_VALUE;
                break;

            case PWD_EXCEPTION:
                readableMap = PWD_EXCEPTION_VALUE;
                break;

            case FP_EXCEPTION:
                readableMap = FP_EXCEPTION_VALUE;
                break;

            case NFC_EXCEPTION:
                readableMap = NFC_EXCEPTION_VALUE;
                break;

            case FACE_EXCEPTION:
                readableMap = FACE_EXCEPTION_VALUE;
                break;

            case PRY_DOOR_EXCEPTION:
                readableMap = PRY_DOOR_EXCEPTION_VALUE;
                break;

            case DOOR_NOT_CLOSE_EXCEPTION:
                readableMap = DOOR_NOT_CLOSE_EXCEPTION_VALUE;
                break;

            case FIRE_EXCEPTION:
                readableMap = FIRE_EXCEPTION_VALUE;
                break

            case VARIED_EXCEPTION:
                readableMap = VARIED_EXCEPTION_VALUE;
                break

            case PWD_HIJACK_EXCEPTION:
                readableMap = PWD_HIJACK_EXCEPTION_VALUE;
                break

            case FP_HIJACK_EXCEPTION:
                readableMap = FP_HIJACK_EXCEPTION_VALUE;
                break

            default:
                break;
        }

        //let extraInfo = JSON.parse(this.props.route.params.params.extraInfo);
        //let exData = extraInfo.exData;
        //console.log(TAG,"SceneSettingException this.props.route",this.props.route,exData);
        //readableMap.exData = exData;

        readableMap.exData = this.props.route.params.exData;

        console.log(TAG, "readableMap=", readableMap);

        IMIGotoPage.exitWithData(JSON.stringify(readableMap));
    }

    refresh() {
        let dataSource = new ListView.DataSource({rowHasChanged: (r1, r2) => r1 !== r2});
        this.state.dataSource = dataSource.cloneWithRows(this.state.allArray);
    }

    renderMover(data) {
        const {title, persons} = data;
        return (
            <ListCards title={title}
                       itemTextStyle={{
                           fontSize: 14,
                           alignItems: 'center',
                           alignSelf: 'center',
                           textAlign: 'left',
                           color: '#4A4C4E',
                           flex: 1,
                           marginLeft: 15,
                           marginRight: 15
                       }}
                       subArrowStyle={{width: 15, height: 7, marginRight: 15, alignSelf: 'center',}}
                       cars={persons}
                       detail={this.detail.bind(this)}/>
        );
    }

    detail(title, id, type) {

        console.log(TAG, "title=", title, ",type=", type, ",id=", id);

        let trigger = {};
        let deviceName = " ";

        switch (type) {

            case 1001://反劫持指纹
            {
                const FINGERPRINT_BASE = 500; //Push id错位添加
                let FP_ID = parseInt(id) + FINGERPRINT_BASE;
                const FP_HIJACK_EXCEPTION_VALUE = {}

                trigger.payload = {
                    value: FP_HIJACK_EXCEPTION_VALUE,
                    name: deviceName + stringsTo('hijack_fp_exception')
                };

                break;
            }

            case 1002://反劫持密码
            {
                const PASSWORD_BASE = 1000;
                let PWD_ID = parseInt(id) + PASSWORD_BASE;
                const PWD_HIJACK_EXCEPTION_VALUE = {}

                trigger.payload = {
                    value: PWD_HIJACK_EXCEPTION_VALUE,
                    name: deviceName + stringsTo('hijack_pwd_exception')
                };

                break;
            }

            default:
                break;
        }

        console.log(TAG, "trigger=", trigger);

        //Package.exitInfo = trigger;
        //Package.exit();
    }


    //获取密码列表（1 密码 2 指纹 3 人脸 4 NFC），根据锁中的密码type和index来获取用户名
    async getUserPwdInfo() {

        let allArray = [];

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID, 1).then(res => {
            pwdDataArr = res;

            console.log(TAG, 'getLockList Pwd ---', res);

            //只显示反劫持
            if (pwdDataArr.filter(item => item.property === 2).length > 0) {
                allArray.push({
                    title: stringsTo('hijack_fp_exception'),
                    persons: pwdDataArr.filter(item => item.property === 2)
                })
            }
        }).catch(error => {
            console.log(TAG, 'getLockList Pwd ---err-', error);
        });

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID, 2).then(res => {
            fingerDataArr = res;

            console.log(TAG, 'getLockList Finger---', res);

            //只显示反劫持
            if (fingerDataArr.filter(item => item.property === 2).length > 0) {
                allArray.push({
                    title: stringsTo('hijack_pwd_exception'),
                    persons: fingerDataArr.filter(item => item.property === 2)
                })
            }
        }).catch(error => {
            console.log(TAG, 'getLockList Finger---err-', error);
        });

        /*await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,3).then(res=>{
            faceDataArr = res;

            console.log(TAG,'getLockList FACE---',res);
        }).catch(error=>{
            console.log(TAG,'getLockList FACE---err-',error);
        });

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,4).then(res=>{
            nfcDataArr = res;

            console.log(TAG,'getLockList NFC---',res);
        }).catch(error=>{
            console.log(TAG,'getLockList NFC---err-',error);
        });*/


        console.log(TAG, "allArray=", JSON.stringify(allArray));

        this.setState({allArray: allArray});
    }

    //获取设备的trigger或condition或action功能列表与TSL定义
    getTslList() {
        const params = {
            Path: '/iotid/scene/ability/tsl/list',
            APIVersion: '1.0.2',
            ParamMap: {
                iotId: LetDevice.deviceID,
                flowType: 1,//流程类型：0-trigger；1-condition；2-action
            }
        };

        LetIMIIotRequest.sendIotServerRequest(params).then((data) => {
            console.log(TAG, 'getTslList then->' + JSON.stringify(data), data);

        }).catch((error) => {
            console.log(TAG, 'getTslList error ' + JSON.stringify(error));
        });

    }

}

let styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'column',
        backgroundColor: '#f8f8f8'
    },
    rowContainer: {
        alignSelf: 'stretch',
        flexDirection: 'row',
        flex: 1,
        backgroundColor: '#ffffff',
        height: 50,
        marginLeft: 20,
        marginRight: 20,
        alignItems: 'center'
    },
    title: {
        fontSize: 16,
        flex: 1,
    },
    subtitle: {
        fontSize: 14,
        flex: 1,
        color: 'rgb(138,138,138)',
        textAlign: "right",
        marginRight: 5
    },
    subArrow: {
        width: 6.5,
        height: 13,
    },
    separator: {
        height: 0.75,
        backgroundColor: '#dddddd',
        marginLeft: 20,
        marginRight: 20,
    }
});
