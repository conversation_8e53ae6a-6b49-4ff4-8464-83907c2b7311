'use strict';

import React from 'react';
import {
    StyleSheet,
    Text,
    TouchableHighlight,
    Image,
    View,
    Platform,
    RefreshControl
} from 'react-native';

import ListView from 'deprecated-react-native-listview'
import ConstDefine from '../Tool/ConstDefine';
import {EventType} from '../Tool/RegularConstDefine';
import {imiAlarmEventCloudApi, IMIGotoPage, IMILog, LetDevice} from "../../../../../imilab-rn-sdk";

import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {stringsTo} from "../../../../../globalization/Localize";
import TextImageButton from "../../../../../imi-rn-commonView/TextImageButton/TextImageButton";
import {RRCToast} from "imilab-design-ui/src/widgets/overlayer";
import BleTools from "../Tool/BleTools";
import DoorBleManager from "../../utils/DoorBleManager";
import {ASCII_To_Hex} from "../Tool/EnCodeTool";
import DataUtil from "../../utils/DataUtil";
import Transform from "../Tool/Transform";

const screenWidth = ConstDefine.screenWidth;
const screenHeight = ConstDefine.screenHeight;
const APPBAR_HEIGHT = ConstDefine.APPBAR_HEIGHT;
const NavigatorBarHeight = ConstDefine.NavigatorBarHeight;
const SafeBottomHeight = ConstDefine.SafeBottomHeight;
const sectionWidth = screenWidth - 20 * 2;
const headViewHeight = 50;
const MAXINT = 7 * 24 * 60 * 60 * 1000;//门锁记录查询时间跨度（7天）
const MAX_LEN = 100;//云端一次最大拉100条记录
const LOCK_PAGE_RECORD_SIZE = 20;//锁端一次最大拉记录数量

let dateArray = [];// 存放开锁记录日期
let securityDateArray = [];// 存放安全事件日期

let isBindCatEyE = LetDevice.isAL;
let isNeedFormLock = false;//是否需要从锁里读取开锁记录(仅限单锁的情况)

let pwdDataArr = [];
let fingerDataArr = [];
let faceDataArr = [];
let nfcDataArr = [];

let del_lock_date = [];

export default class LockRecordListPage extends React.Component {
    static navigationOptions = ({navigation}) => {
        return {
            header: null
        };
    };

    constructor(props, context) {
        super(props, context);
        console.log("lrlp --------页面构造函数--------");

        isNeedFormLock = (isBindCatEyE === false);

        this.state = {
            ...this.state,
            dataNormal: [],
            dataTimeArr:[],
            dataSourceNormal: null,
            warningNormal: [],
            warningTimeArr:[],
            warningSourceNormal: new ListView.DataSource({rowHasChanged: (r1, r2) => r1 !== r2}),
            isMore: false, // 是否还有更多的记录
            isRefreshing: false, // 是否正在下拉刷新请求和加载数据中
            isMoreLoading: false, // 是否正在上拉加载更多数据请求和加载数据中
            isShowDoorbellButton: false,
            isRecordListFocus:true,
            normalPage:0,
            warningPage:0,
            isRead:false,//门锁事件是否读过
            readNum:0,//未读条数

            //单锁处理
            lock_cur_page:1,//当前获取锁中开锁记录页数
            lock_total_page:1,//锁中总共有多少页记录
        };
    }

    componentDidMount() {
        // super.componentDidMount();
        let currDate = new Date();

        if(!isBindCatEyE){
            DoorBleManager.getInstance().isBleEnable().then(result=> {
                this._getLockData(1);
            });
        }else{
            //this._queryDayData(currDate.getTime());

            this._syncLockData();
        }

        //this._querySecurityEventList(currDate.getTime());

        imiAlarmEventCloudApi.getLockListEventNum(LetDevice.deviceID).then(res=>{
            console.log('getLockListEventNum--res------',JSON.stringify(res));
            this.setState({readNum:res.num>99?'99+':res.num});
        }).catch(error=>{
            console.log('getLockListEventNum--err------',JSON.stringify(error));
        });
    }

    componentWillUnmount() {
        console.log("lrlp --------页面退出--------");
        BleTools.hide();
        dateArray = [];// 存放开锁记录日期
        securityDateArray = [];//存放安全事件日期
        this.setState({
            dataNormal:[],
            dataTimeArr:[],
            warningNormal:[],
            warningTimeArr:[],
            isMore: false, // 是否还有更多的记录
            isRefreshing: false, // 是否正在下拉刷新请求和加载数据中
            isMoreLoading: false // 是否正在上拉加载更多数据请求和加载数据中
        });
    }

    refresh() {
        let dataSource = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 });
        this.state.dataSourceNormal = dataSource.cloneWithRows(this.state.isRecordListFocus
            ?this.state.dataNormal:this.state.warningNormal);
    }

    /** ********************************************门锁记录页面的数据渲染************************************************* */
    render() {
        this.refresh();
        // 如果还有数据未加载完，显示一个footView来提示用户
        let dataNormal = this.state.isRecordListFocus ?this.state.dataNormal:this.state.warningNormal;
        let footerView = (
            <View style={{
                flexDirection: "column",
                width: screenWidth,
                height: 60,
                alignItems: "center",
                justifyContent: "center"
            }}>
                {this.state.isRefreshing == true ? <View/> :
                    <Text style={{marginTop: 5}}>{this.state.isMore ? stringsTo("pull_up_refresh") : (dataNormal.length>10?stringsTo("alarm_none_data"):'')}</Text>}
            </View>);
        console.log("lrlp 刷新显示列表********************************************是否还有数据", `${this.state.isMore}，isRefreshing：--------${this.state.isRefreshing}`);

        return (
            <View style={{flex: 1, flexDirection: 'column', backgroundColor: 'white'}}>
                <NavigationBar
                    backgroundColor={'transparent'}
                    left={[{
                        key: NavigationBar.ICON.BACK,
                        onPress: (_) => {
                            if (this.props.route.params.isInRN){
                                this.props.navigation.pop();
                            } else {
                                IMIGotoPage.exit();
                            }
                        }
                    }]}
                    title={stringsTo('door_look_event')}
                    right={
                        this.state.isShowDoorbellButton
                            ? [{
                                key: NavigationBar.ICON.NEXT,
                                showDot: false,
                                onPress: () => {
                                    this.props.navigation.navigate('DoorbellListPage', {'title': stringsTo('door_look_record')});
                                }}]
                            : []
                    }
                />
                {isBindCatEyE===true?<View style={{height:50, flexDirection: 'row'}}>
                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("door_look_record")}
                                     imageVisible={false}
                                     endImageVisible={true}
                                     endImageStyle={{backgroundColor:this.state.isRecordListFocus?"#1E5BA9":'white',height:3,width:40,borderRadius:3,marginTop: 5}}
                                     textStyle={{color:'#000000',fontSize: 15,fontWeight:'bold'}}
                                     onPress={() => {

                                         dateArray = [];
                                         securityDateArray = [];
                                         this.setState({
                                             dataNormal:[],
                                             dataTimeArr:[],
                                             warningNormal:[],
                                             warningTimeArr:[],
                                             normalPage:0,
                                             warningPage:0,
                                         },()=>{
                                             (isBindCatEyE===false)?this._getLockData(1):this._queryDayData(new Date().getTime());
                                         })

                                     }}
                    />
                    <TextImageButton style={{flex: 1}}
                                     imageVisible={false}
                                     endImageVisible={true}
                                     endImageStyle={{backgroundColor:this.state.isRecordListFocus?"white":'#1E5BA9',height:3,width:40,borderRadius:3,marginTop: 5}}
                                     title={stringsTo("security_event")}
                                     textStyle={{color:'#000000',fontSize: 15,fontWeight:'bold'}}
                                     onPress={() => {

                                         dateArray = [];
                                         securityDateArray = [];
                                         this.setState({
                                             dataNormal:[],
                                             dataTimeArr:[],
                                             warningNormal:[],
                                             warningTimeArr:[],
                                             normalPage:0,
                                             warningPage:0,
                                         },()=>{
                                             let currDate = new Date();
                                             this.setState({isRead:true});
                                             this._querySecurityEventList(currDate.getTime());
                                         })

                                     }}
                    />
                    {this.state.readNum>0?(this.state.isRead?null:<View style={{position: "absolute",backgroundColor:'#E24C4C',borderRadius:10,width:20,height:20,top:10,right:20,justifyContent: "center",
                        alignItems: "center"}}>
                        <Text style={{color:'white',fontSize:10}}>{this.state.readNum}</Text>
                    </View>):null}
                </View>:null}
                <View style={{flex: 1, flexDirection: 'column'}}>
                    {this.state.isRecordListFocus
                        ? ((this.state.dataNormal.length > 0 || this.state.isRefreshing)
                            ? (this.showUnlockListView(footerView))
                            : (this.showEmptyView())
                        )
                        : ((this.state.warningNormal.length > 0 || this.state.isRefreshing)
                                ? (this.showUnlockListView(footerView))
                                : (this.showEmptyView())
                        )
                    }
                </View>
            </View>
        );
    }

    /***
     * 显示门锁记录
     */
    showUnlockListView(footerView){
        return(<ListView
                  style={{flex: 1}}
                  showsHorizontalScrollIndicator = {true}
                  enableEmptySections={true}
                  dataSource={this.state.dataSourceNormal}
                  renderRow={this.renderRow}
                  initialListSize={20}
                  automaticallyAdjustContentInsets={false}
                          scrollIndicatorInsets={{right: 1}}
                  refreshControl={
                      <RefreshControl
                          refreshing={this.state.isRefreshing}
                          onRefresh={this.onRefresh}
                          title={stringsTo('pull_down_refresh')}
                          titleColor="#00ff00"
                      />}
                  renderFooter={() => footerView}
                  onEndReachedThreshold={20}
                  onEndReached={this.onLoadMore}/>)
    }

    /***
     * 显示安全事件
     */
    showEventWarningListView(footerView){
        return(<ListView
                         style={{flex: 1}}
                         enableEmptySections={true}
                         dataSource={this.state.dataSourceNormal}
                         renderRow={this.renderRow}
                         initialListSize={20}
                         automaticallyAdjustContentInsets={false}
                         refreshControl={
                             <RefreshControl
                                 refreshing={this.state.isRefreshing}
                                 onRefresh={this.onRefresh}
                                 title={stringsTo('pull_down_refresh')}
                                 titleColor="#00ff00"
                             />}
                         renderFooter={() => footerView}
                         onEndReachedThreshold={20}
                         onEndReached={this.onLoadMore}/>)
    }
    /* 开门记录为空的EmptyView提示 */
    showEmptyView() {
        return (
            <View style={{
                flex: 1,
                flexDirection: 'column',
                backgroundColor: 'white',
                width: screenWidth,
            }}>
                <View style={{
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 80
                }}>
                    <Image style={{width: 300, height: 200}}
                           source={require("../../../res/new_ui/pic_search_bg.png")}/>
                    <Text style={{marginTop: 20, color: '#000000cc', fontSize: 16}}>
                        {this.state.isRecordListFocus?stringsTo('no_lock_record'):stringsTo('no_event_warning')}
                    </Text>
                </View>
            </View>
        );
    }

    // 加载更多数据
    onLoadMore = () => {
        console.log("dengying","上拉加载更多 onLoadMore");

        console.log(`lrlp ------------上拉加载更多数据监听事件触发-------------isMoreLoading:${this.state.isMoreLoading}isMore:${this.state.isMore}`);
        if (!this.state.isMoreLoading && this.state.isMore) {
            console.log('lrlp ------------监听事件触发，上拉加载更多数据-------------');
            this.setState({
                isMoreLoading: true
            }, () => {
                if(this.state.isRecordListFocus) {
                    if(isNeedFormLock){
                        this._getLockData(this.state.lock_cur_page+1);
                    }else{
                        let len = this.state.dataTimeArr.length;
                        if (len > 0) {
                            let startTime = parseInt(this.state.dataTimeArr[len - 1]) - 1000;
                            // let page = this.state.normalPage+1;
                            // this.setState({normalPage:page},()=>{
                            this._queryDayData(startTime);
                            // });
                        } else {
                            let curDate = new Date();
                            this._queryDayData(curDate.getTime());
                        }
                    }
                }else{
                    let len = this.state.warningTimeArr.length;
                    if (len > 0) {
                        let startTime = parseInt(this.state.warningTimeArr[len - 1]) - 1000;
                        // let page = this.state.warningPage+1;
                        // this.setState({warningPage:page},(zz)=>{
                            this._querySecurityEventList(startTime);
                        // });
                    } else {
                        let curDate = new Date();
                        this._querySecurityEventList(curDate.getTime());
                    }
                }

                this.setState({
                    isMoreLoading: false,
                    isMore: false
                }, () => {
                });
            });

        }
    };

    // 刷新数据，即下拉刷新或者进入门锁记录页面自动加载数据
    onRefresh = () => {
        console.log("dengying","下拉刷新 onRefresh");

        console.log(`lrlp ------------监听事件触发，下拉刷新数据-------------isRefreshing:${this.state.isRefreshing},isMoreLoading:${this.state.isMoreLoading}`);
        if (!this.state.isRefreshing && !this.state.isMoreLoading) {
            console.log('lrlp ------------监听事件触发------------');
            this.setState({
                defaultSelect: EventType.NewBle
            });
            let currDate = new Date();
            this.state.isRecordListFocus
                ? (isBindCatEyE === false) ? this._getLockData(1) : this._queryDayData(currDate.getTime())
                : this._querySecurityEventList(currDate.getTime());
        }
    };

    /* 解析和显示每一行 */
    renderRow = (rowData, sectionID, rowID) => {
        let formatValue = "";
        let formatName = "";
        if (rowData.type != "title") {
            /*this.state.isRecordListFocus
                ? formatName = rowData.userName
                : formatName = ""*/

            if(rowData.userName!==undefined && rowData.userName!=="null"){
                formatName = rowData.userName;
            }

            this.state.isRecordListFocus
                ? formatValue = this.getUnlockFormatValue(rowData.openType)
                : formatValue = this.getWarningFormatValue(rowData.warningType)
        }
        if (formatName != "") {
            formatName = formatName + " ";
        }
        let rowItemTime = this.state.isRecordListFocus?rowData.openTime:rowData.eventTime;
        let time = parseInt(rowItemTime)>1000000000000?parseInt(rowItemTime):parseInt(rowItemTime)*1000;

        let timeValue = this.dateFormat(new Date(time), "yyyy/MM/dd")
        console.log("jeff",timeValue,time,"sectionID = ",sectionID,"rowID = ",rowID,"type = ",rowData.type);
        return this.renderRowItemView(timeValue,time,formatValue,formatName,rowData.type,rowData.openType);
    };

    /***
     * 获取开锁方式字串
     * @param type 开锁方式
     * @returns {string} 开锁方式描述
     */
    getUnlockFormatValue(type){
        let formatValue = "";
        switch (type) {
            case 0:
                formatValue = stringsTo('is_no_user_pwd_lock');//密码开锁
                break;
            case 1:
                formatValue = stringsTo('is_no_user_fp_lock');//指纹开锁
                break;
            case 2:
                formatValue = stringsTo('is_no_user_once_pwd_lock');//临时密码开锁
                break;
            case 3:
            case 4:
            case 5:
                formatValue = stringsTo('is_double_lock');//双重验证开锁
                // formatValue = stringsTo('is_no_user_double_fp_lock');
                break;
            case 6:
                formatValue = stringsTo('is_no_user_app_lock');//手机开锁
                break;
            case 7:
                formatValue = stringsTo('is_no_user_open_lock');//门内开锁
                break;
            case 8:
                formatValue = stringsTo('is_no_user_close_lock');//门内上锁
                break;
            case 9:
                formatValue = stringsTo('is_no_user_out_close_lock');//门外上锁
                break;
            case 10:
                formatValue = stringsTo('is_no_user_nfc_lock');//NFC开锁
                break;
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
                formatValue = stringsTo('is_double_lock');
                // formatValue = stringsTo('is_no_user_double_nfc_lock');
                break;
            case 16:
                formatValue = stringsTo('is_no_user_face_lock');//人脸开锁
                break;
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
                formatValue = stringsTo('is_double_lock');
                // formatValue = stringsTo('is_no_user_face_nfc_lock');
                break;
            case 24://认证--门锁记录 //dengying@20220107
                formatValue = stringsTo('add_pwd_event');
                break;
            case 25:
                formatValue = stringsTo('edit_pwd_event');
                break;
            case 26:
                formatValue = stringsTo('delete_pwd_event');
                break;
            case 27:
                formatValue = stringsTo('add_finger_event');
                break;
            case 28:
                formatValue = stringsTo('edit_finger_event');
                break;
            case 29:
                formatValue = stringsTo('delete_finger_event');
                break;
            case 30:
                formatValue = stringsTo('add_nfc_event');
                break;
            case 31:
                formatValue = stringsTo('edit_nfc_event');
                break;
            case 32:
                formatValue = stringsTo('delete_nfc_event');
                break;
            case 33:
                formatValue = stringsTo('add_face_event');
                break;
            case 34:
                formatValue = stringsTo('edit_face_event');
                break;
            case 35:
                formatValue = stringsTo('delete_face_event');
                break;
        }
        return formatValue;
    }

    /***
     * 获取开锁方式字串
     * @param type 安全事件类型
     * @returns {string} 安全事件描述
     */
    getWarningFormatValue(type){
        let formatValue = "";
        switch (type) {
            case 1:
                formatValue = stringsTo('event_warning_hijacked_password');//挟持密码报警
                break;
            case 2:
                formatValue = stringsTo('event_warning_hijacked_finger');//挟持指纹报警
                break;
            case 3:
                formatValue = stringsTo('event_warning_feel_password');//密码试探开门报警
                break;
            case 4:
                formatValue = stringsTo('event_warning_feel_finger');//指纹试探开门报警
                break;
            case 5:
                formatValue = stringsTo('event_warning_prying_resistant');//防撬报警
                break;
            case 6:
                formatValue = stringsTo('event_warning_low_battery');//低电量报警
                break;
            case 7:
                formatValue = stringsTo('event_warning_lock_door_timeout');//开门未关超时报警
                break;
            case 8:
                formatValue = stringsTo('event_warning_feel_nfc');//NFC试探开门报警
                break;
            case 9:
                formatValue = stringsTo('event_warning_feel_3d');//结构光试探开门报警
                break;
            case 10:
                formatValue = stringsTo('event_warning_feel_combination');//组合试探开门报警
                break;
            case 11:
                formatValue = stringsTo('event_warning_fire_combination');//防火报警
                break;
        }
        return formatValue;
    }

    /***
     * 单条记录显示
     * @param timeValue 今天的日期
     * @param rowItemTime 当前时间
     * @param formatValue 记录描述
     * @param formatName 操作用户
     * @param type 记录类型
     * @returns {*} 记录显示
     */
    renderRowItemView(timeValue,rowItemTime,formatValue,formatName,type,openType){

        let txt = `${this.dateFormat(new Date(parseInt(rowItemTime)), "HH:mm:ss")}  ${formatName.length > 9 ? `${formatName.substring(0, 6)}…` : formatName}${formatValue} `;

        //认证--门锁记录 //dengying@20220107
        if(openType>23){
            txt = `${this.dateFormat(new Date(parseInt(rowItemTime)), "HH:mm:ss")}  ${formatValue} ${formatName.length > 9 ? `${formatName.substring(0, 6)}…` : formatName} `;
        }else if(this.state.isRecordListFocus && (openType === 0 || openType === 1 || openType === 10 || openType === 16 )){
            txt = `${this.dateFormat(new Date(parseInt(rowItemTime)), "HH:mm:ss")}  `+
                Transform.stringFormat_s(formatValue,formatName.length > 9 ? `${formatName.substring(0, 6)}…` : formatName);
        }

        return (
            <TouchableHighlight underlayColor="#838383">
                {
                    type== "title"
                        ? (<View style={{flexDirection: 'column', width: screenWidth}}>
                            <View style={{flexDirection: 'row', height: 47, backgroundColor: "white", width: screenWidth, justifyContent: 'space-between'}}>
                                <Text style={{fontSize: 14, marginLeft: 20, alignSelf: 'center', color: '#7F7F7F', flex: 1}}>
                                    {this.isToday(timeValue) ? stringsTo('plug_timer_today') : timeValue}
                                </Text>
                                <Text style={{fontSize: 14, marginRight: 20, color: '#7F7F7F', alignSelf: 'center', justifyContent: "flex-end"}}>
                                    {this.getWeekDayString(new Date(parseInt(rowItemTime)))}
                                </Text>
                            </View>
                            {/*<View style={{backgroundColor: '#c5c5c5', height: 1}}/>*/}
                        </View>)
                        : (<View style={{flexDirection: 'column', width: screenWidth}}>
                            <View style={{flexDirection: 'row', height: 60, justifyContent:'center',backgroundColor: "white"}}>
                                {this._renderTimeAxis(type)}
                                <Text style={{fontSize: 15, marginLeft: 10,marginRight:10, alignSelf: 'center',flex: 1,color: '#333333'}}>
                                    {txt}
                                </Text>
                                {this.state.isRecordListFocus
                                    ? null
                                    : (<View style={{flexDirection: 'row', width:30, justifyContent: 'flex-end'}}>
                                        <Image style={{width: 30, height: 30, marginRight: 10, alignSelf: 'center'}}
                                               source={require("../../../res/lock/NewImage/home_record_icon_warning.png")}
                                        />
                                    </View>)
                                }
                            </View>
                            {/*<View style={{backgroundColor: '#c5c5c5', height: 1, marginLeft: 40}}/>*/}
                        </View>)
                }
            </TouchableHighlight>
        );
    }

    /**
     * 渲染时间轴
     * @private
     */
    _renderTimeAxis(type){
        return (
            <View style={{flexDirection: 'column', justifyContent:'flex-start',alignItems: 'center',width: 30, height: 60, marginLeft: 15}}>
                {type== "none" ? null :<View style={{backgroundColor: '#CCCCCC', marginTop:type== "down"?30:0,
                    width: 0.5,height:(type== "up"||type== "down")?30:60}}/>}
                <Image style={{marginTop:15,width: 30, height: 30,position:"absolute"}} source={require('../../../res/lock/NewImage/pic_none.png')}/>
            </View>);
    }
    /* 每一行的点击事件 */
    _pressRow(rowData) {

    }

    dateFormat(date, fmt) {
        let o = {
            "M+": date.getMonth() + 1, // 月份
            "d+": date.getDate(), // 日
            "H+": date.getHours(), // 小时
            "m+": date.getMinutes(), // 分
            "s+": date.getSeconds(), // 秒
            "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
            "S": date.getMilliseconds() // 毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (`${date.getFullYear()}`).substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp(`(${k})`).test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : ((`00${o[k]}`).substr((`${o[k]}`).length)));
        return fmt;
    }

    // 米家keyId转为16进制
    keyIdToHex(keyId) {
        let hex = parseInt(keyId).toString(16).toUpperCase();
        while (hex.length < 8) {
            hex = `0${hex}`;
        }
        let hexList = [];
        for (let i = 0; i + 2 <= hex.length; i += 2) {
            let str = hex.substring(i, i + 2);
            hexList.unshift(str);
        }
        // console.log('------',hexList, hexList.join(''));
        return hexList.join('');
    }

    // 判断是否是今天
    isToday(date) {
        return this.dateFormat(new Date(), "yyyy/MM/dd") == date;
    }

    // 返回今天是周几
    getWeekDayString(date) {
        let str = "";
        let week = new Date(date).getDay();
        if (week == 0) {
            str = stringsTo('week_7');
        } else if (week == 1) {
            str = stringsTo('week_1');
        } else if (week == 2) {
            str = stringsTo('week_2');
        } else if (week == 3) {
            str = stringsTo('week_3');
        } else if (week == 4) {
            str = stringsTo('week_4');
        } else if (week == 5) {
            str = stringsTo('week_5');
        } else if (week == 6) {
            str = stringsTo('week_6');
        }
        return str;
    }


    /***
     * 单锁的时候获取门锁中的门锁记录
     * @private
     */
    _getLockData(cur_page) {

        if (cur_page > this.state.lock_total_page) {
            return;
        }

        let dataArr = this.state.dataNormal;
        let timeArr = this.state.dataTimeArr;
        let isMore = true;

        console.log("dengying","_getLockData,cur_page=",cur_page);

        BleTools.loading(stringsTo("loading"));

        DoorBleManager.getInstance().sendCmd_device_event_list(cur_page,LOCK_PAGE_RECORD_SIZE).then(res=>{

            let page_num = res.page_num;
            let cur_record_num = res.cur_record_num;

            console.log("dengying",'---获取锁端开锁记录---sendCmd_device_event_list cur_page=',cur_page,'page_num=',page_num,
                ',cur_record_num=',cur_record_num,',res=',JSON.stringify(res));

            /*
            aa0106051600200003070000150c070f162808080000150c080e040008060000150c080e060f0830

            eventName------------ AA01060516 {status: 0, data: "0003070000150c070f162808080000150c080e040008060000150c080e060f08"}

            getNormalEvent:3
            getNormalEvent:["07","00","00","15","0c","07","0f","16","28","08"]
            getNormalEvent:["08","00","00","15","0c","08","0e","04","00","08"]
            getNormalEvent:["06","00","00","15","0c","08","0e","06","0f","08"]

            res
             [
                 ["07","00","00","15","0c","07","0f","16","28","08"],
                 ["08","00","00","15","0c","08","0e","04","00","08"],
                 ["06","00","00","15","0c","08","0e","06","0f","08"]
             ]
            */

            /*[
            {"id":43231,"identityId":"50f1op8c95462a5d4f9e5c99f34947e734661703","iotId":"K2mA6JDnWSJ1LCTOorVX000000","openTime":1638861760309,"userName":"","openType":7,"message":""},
            {"id":38579,"identityId":"50f1op8c95462a5d4f9e5c99f34947e734661703","iotId":"K2mA6JDnWSJ1LCTOorVX000000","openTime":1638775993579,"userName":"","openType":9,"message":""}
            ]*/

            let data = res.data;
            let resLen = data.length;
            if(dataArr.length > 0 ){
                //重置底部时间轴
                if(dataArr[dataArr.length -1].type === "none"){
                    dataArr[dataArr.length -1].type = "down";
                }else if(dataArr[dataArr.length -1].type === "up"){
                    dataArr[dataArr.length -1].type = "double";
                }
                //重置顶部时间轴
                if(dataArr[1].type == "down"){
                    dataArr[1].type = "double";
                }else if(dataArr[1].type == "none"){
                    dataArr[1].type = "up";
                }
            }

            for (let i = 0; i < resLen; i++) {
                let openType = parseInt(data[i][0], 16);
                if(openType >= 24){//todo 过滤锁端开锁记录里大于23的type记录
                    continue;
                }
                let userID = parseInt(data[i][1], 16);

                let year = parseInt(new Date().getFullYear()/100)*100+parseInt(data[i][3], 16);
                let month = parseInt(data[i][4], 16)-1;
                let day = parseInt(data[i][5], 16);
                let hour = parseInt(data[i][6], 16);
                let min = parseInt(data[i][7], 16);
                let second = parseInt(data[i][8], 16);
                let local = parseInt(data[i][9], 16);

                console.log("dengying",'year=',year,'month=',month,'day=',day,'hour=',hour,'min=',min,'second=',second,);

                let openTime = new Date(year,month,day,hour,min,second).getTime();
                let userName = "";
                let message = "";

                let item_data = {"openTime":openTime,"userName":userName,"openType":openType,"message":message};
                console.log("jeffdengying",item_data);
                let offset = timeArr.indexOf(openTime);
                if (offset < 0) {
                    let time = parseInt(openTime)>1000000000000?parseInt(openTime):parseInt(openTime)*1000;
                    let timestring = this.dateFormat(new Date(parseInt(time)), "yyyy/MM/dd");
                    if(dateArray.indexOf(timestring)<0){
                        console.log('timestring------',timestring,time);
                        dateArray.push(timestring);
                        let dateItem = new Date(parseInt(time));
                        let dateMax= (new Date(dateItem.getFullYear(), dateItem.getMonth(), dateItem.getDate(), 23, 59, 59,999)).getTime();
                        if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                            dataArr[dataArr.length -1].type = "none";
                        }else if(dataArr.length > 0){
                            dataArr[dataArr.length -1].type = "up";
                        }
                        //恢复顶部时间轴
                        if(dataArr.length > 0){
                            if(dataArr[1].type == "double"){
                                dataArr[1].type = "down";
                            }else if(dataArr[1].type == "up"){
                                dataArr[1].type = "none";
                            }
                        }
                        dataArr.push({type: "title", time: timestring, openTime:dateMax.toString()});
                        item_data.type = "down";
                    }
                    dataArr.push(item_data);
                    timeArr.push(openTime);
                }
            }
            if (cur_page < page_num) {
                isMore = true;
                isNeedFormLock = true;
            }else {
                isMore = false;
                isNeedFormLock = false;
            }

            dataArr.sort((a, b) => {
                let aTime = parseInt(a.openTime)>1000000000000?parseInt(a.openTime):parseInt(a.openTime)*1000;
                let bTime = parseInt(b.openTime)>1000000000000?parseInt(b.openTime):parseInt(b.openTime)*1000;

                return bTime - aTime;
            });
            //更新底部时间轴
            if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                dataArr[dataArr.length -1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[dataArr.length -1].type = "up";
            }
            //更新顶部时间轴
            if(dataArr.length > 2 && dataArr[2].type === "title"){
                dataArr[1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[1].type = "down";
            }
            console.log("dengying",'---获取锁端开锁记录---getOpenLockList--dataArr---',JSON.stringify(dataArr));

            this.setState((state) => {
                return {
                    isRefreshing: false,
                    isRecordListFocus:true,
                    dataNormal: dataArr,
                    dataTimeArr:timeArr,
                    // dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
                    isMore: isMore,
                    lock_cur_page:cur_page,
                    lock_total_page:page_num,
                };
            }, () => {
                console.log("_getLockData  resLen="+resLen+" dataArr="+JSON.stringify(dataArr));
            });

            BleTools.hide();

            if(!isNeedFormLock && cur_record_num < LOCK_PAGE_RECORD_SIZE){
                this._queryDayData(new Date().getTime());
            }

        }).catch(error=>{
            BleTools.hide();
            console.log('dengying','sendCmd_device_event_list err--------',error);
        })
    }


    async _syncLockData(){
        await this._getUserPwdInfo();
        DoorBleManager.getInstance().isBleEnable().then(async result=> {
            await this._getLockDataAndSyncCloud();
        });
        this._queryDayData(new Date().getTime());
    }

    /***
     * 获取门锁中的门锁记录并同步到云端然后删除锁端
     * @private
     */
     async _getLockDataAndSyncCloud(cur_page=1) {

        console.log("jeffdengying","_getLockDataAndSyncCloud cur_page=",cur_page);

        let dataArr = [];
        let isMore = true;

        DoorBleManager.getInstance().sendCmd_device_event_list(cur_page,LOCK_PAGE_RECORD_SIZE).then(async res => {

            let page_num = res.page_num;
            let cur_record_num = res.cur_record_num;

            console.log("jeffdengying", '---获取锁端开锁记录---_getLockDataAndSyncCloud cur_page=', cur_page, 'page_num=', page_num,
                ',cur_record_num=', cur_record_num, ',res=', JSON.stringify(res));

            let data = res.data;
            let resLen = data.length;

            for (let i = 0; i < resLen; i++) {
                let openType = parseInt(data[i][0], 16);
                if(openType >= 24){//todo 过滤锁端开锁记录里大于23的type记录
                    continue;
                }

                let userID = -1;
                let userName = "";

                // 0x00----单密码用户 开锁
                // 0x01----单指纹用户 开锁
                // 0x0a---nfc 开锁
                // 0x10---单结构光

                if (openType === 0) {//单密码用户
                    userID = parseInt(data[i][1], 16);

                    for (let i = 0; i < pwdDataArr.length; i++) {
                        if (userID === parseInt(pwdDataArr[i].keyId)) {
                            userName = pwdDataArr[i].name;
                            break;
                        }
                    }
                } else if (openType === 1) {//单指纹用户
                    userID = parseInt(data[i][1], 16);
                    for (let i = 0; i < fingerDataArr.length; i++) {
                        if (userID === parseInt(fingerDataArr[i].keyId)) {
                            userName = fingerDataArr[i].name;
                            break;
                        }
                    }
                } else if (openType === 10) {//nfc 开锁
                    userID = parseInt(data[i][1], 16);
                    for (let i = 0; i < nfcDataArr.length; i++) {
                        if (userID === parseInt(nfcDataArr[i].keyId)) {
                            userName = nfcDataArr[i].name;
                            break;
                        }
                    }
                } else if (openType === 16) {//单结构光
                    userID = parseInt(data[i][1], 16);
                    for (let i = 0; i < faceDataArr.length; i++) {
                        if (userID === parseInt(faceDataArr[i].keyId)) {
                            userName = faceDataArr[i].name;
                            break;
                        }
                    }
                }

                let year = parseInt(new Date().getFullYear() / 100) * 100 + parseInt(data[i][3], 16);
                let month = parseInt(data[i][4], 16) - 1;
                let day = parseInt(data[i][5], 16);
                let hour = parseInt(data[i][6], 16);
                let min = parseInt(data[i][7], 16);
                let second = parseInt(data[i][8], 16);
                let local = parseInt(data[i][9], 16);

                console.log("dengying", 'year=', year, 'month=', month, 'day=', day, 'hour=', hour, 'min=', min, 'second=', second,);

                if (cur_page === 1 && i === 0) {
                    del_lock_date = DataUtil.hexStringToNumberArray(
                        DataUtil.numberToHexString(year)
                        + DataUtil.numberToHexString(month)
                        + DataUtil.numberToHexString(day)
                        + DataUtil.numberToHexString(hour)
                        + DataUtil.numberToHexString(min)
                        + DataUtil.numberToHexString(second)
                        + DataUtil.numberToHexString(local)
                    )
                }

                let openTime = new Date(year, month, day, hour, min, second).getTime();
                let message = "";

                let item_data = {iotId: LetDevice.deviceID,openTime: openTime.toString(), userName: userName, openType: openType, message: message};
                console.log("jeffdengying",item_data);
                dataArr.push(item_data);
            }

            if (dataArr.length > 0) {
                await imiAlarmEventCloudApi.uploadUnlockRecord(dataArr).then(res=>{
                    console.log('uploadUnlockRecord--res------',JSON.stringify(res));
                    if (cur_page < page_num) {
                        isMore = true;
                    } else {
                        isMore = false;
                    }

                    if (isMore) {
                        this._getLockDataAndSyncCloud(++cur_page);
                    } else {
                        //删除已经同步过的开锁记录
                        if(del_lock_date.length>0){
                            DoorBleManager.getInstance().sendCmd_del_event_list(del_lock_date).then(res => {
                                console.log("dengying", "del_event_list,res=", res);
                            }).catch(err => {
                                console.log("dengying", "del_event_list,err=", err);
                            })
                        }
                    }
                }).catch(error=>{
                    console.log('uploadUnlockRecord--err------',JSON.stringify(error));
                });
            }

        }).catch(error=>{
            console.log('jeffdengying','_getLockDataAndSyncCloud err--------',error);
        })
    }

    //获取密码列表（1 密码 2 指纹 3 人脸 4 NFC），根据锁中的密码type和index来获取用户名
    async _getUserPwdInfo(){
        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,1).then(res=>{
            pwdDataArr = res;

            console.log('dengying','getLockList Pwd ---',res);
        }).catch(error=>{
            console.log('dengying','getLockList Pwd ---err-',error);
        });

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,2).then(res=>{
            fingerDataArr = res;

            console.log('dengying','getLockList Finger---',res);
        }).catch(error=>{
            console.log('dengying','getLockList Finger---err-',error);
        });

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,3).then(res=>{
            faceDataArr = res;

            console.log('dengying','getLockList FACE---',res);
        }).catch(error=>{
            console.log('dengying','getLockList FACE---err-',error);
        });

        await imiAlarmEventCloudApi.getLockList(LetDevice.deviceID,4).then(res=>{
            nfcDataArr = res;

            console.log('dengying','getLockList NFC---',res);
        }).catch(error=>{
            console.log('dengying','getLockList NFC---err-',error);
        });
    }
    /***
     * 获取门锁记录
     * @param currTime 当前时间
     * @private
     */
    _queryDayData(currTime) {

        console.log("dengying","_queryDayData isBindCatEyE="+isBindCatEyE);
        // let endDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 23, 59, 59);
        // let endTime = endDate.getTime();
        let endTime = currTime;
        let startTime = endTime - MAXINT;
        let dataArr = this.state.dataNormal;
        let timeArr = this.state.dataTimeArr;
        let isMore = true;
        console.log("lrlp _queryDayData currTime="+this.state.normalPage);
        //console.log("dengying",'_queryDayData dataArr=',JSON.stringify(dataArr),"timeArr="+JSON.stringify(timeArr));

        // const MAX_LEN = 2;
        BleTools.loading(stringsTo("loading"));
        imiAlarmEventCloudApi.getOpenLockList(LetDevice.deviceID,this.state.normalPage,MAX_LEN).then(res=>{
            // console.log('getOpenLockList--res------',JSON.stringify(res));
            let dataJson =res;
            // let resultObjectToJson =JSON.parse(JSON.stringify(result));
            // if (resultObjectToJson.LockOpenList != undefined) {
            //     dataJson = JSON.parse(resultObjectToJson.LockOpenList);
            // }
            let resLen = dataJson.length;
            console.log("dengying",'---获取云端开锁记录---getOpenLockList--res------',JSON.stringify(res));
            if(dataArr.length > 0 ){
                //重置底部时间轴
                if(dataArr[dataArr.length -1].type === "none"){
                    dataArr[dataArr.length -1].type = "down";
                }else if(dataArr[dataArr.length -1].type === "up"){
                    dataArr[dataArr.length -1].type = "double";
                }
                //重置顶部时间轴
                if(dataArr[1].type == "down"){
                    dataArr[1].type = "double";
                }else if(dataArr[1].type == "none"){
                    dataArr[1].type = "up";
                }
            }
            for (let i = 0; i < resLen; i++) {
                let openTime = dataJson[i].openTime;
                let offset = timeArr.indexOf(openTime);
                if (offset < 0) {
                    let time = parseInt(openTime)>1000000000000?parseInt(openTime):parseInt(openTime)*1000;
                    let timestring = this.dateFormat(new Date(parseInt(time)), "yyyy/MM/dd");
                    if(dateArray.indexOf(timestring)<0){
                        console.log("jeff----",'timestring------',timestring,time);
                        dateArray.push(timestring);
                        let dateItem = new Date(parseInt(time));
                        let dateMax= (new Date(dateItem.getFullYear(), dateItem.getMonth(), dateItem.getDate(), 23, 59, 59,999)).getTime();
                        if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                            dataArr[dataArr.length -1].type = "none";
                        }else if(dataArr.length > 0){
                            dataArr[dataArr.length -1].type = "up";
                        }
                        //恢复顶部时间轴
                        if(dataArr.length > 0){
                            if(dataArr[1].type == "double"){
                                dataArr[1].type = "down";
                            }else if(dataArr[1].type == "up"){
                                dataArr[1].type = "none";
                            }
                        }
                        dataArr.push({type: "title", time: timestring, openTime:dateMax.toString()});
                        dataJson[i].type = "down";
                    }
                    // console.log("jeff----","openTime = ",openTime);
                    dataArr.push(dataJson[i]);
                    timeArr.push(openTime);
                }
            }

            let page ;
            if (resLen < MAX_LEN) {
                isMore = false;
                page = 0;
            }else {
                page = this.state.normalPage+1;
            }
            dataArr.sort((a, b) => {
                let aTime = parseInt(a.openTime)>1000000000000?parseInt(a.openTime):parseInt(a.openTime)*1000;
                let bTime = parseInt(b.openTime)>1000000000000?parseInt(b.openTime):parseInt(b.openTime)*1000;

                return bTime - aTime;
            });
            BleTools.hide()
            //更新底部时间轴
            if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                dataArr[dataArr.length -1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[dataArr.length -1].type = "up";
            }
            //更新顶部时间轴
            if(dataArr.length > 2 && dataArr[2].type === "title"){
                dataArr[1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[1].type = "down";
            }
            console.log("dengying",'---获取云端开锁记录---getOpenLockList--dataArr---',JSON.stringify(dataArr));

            this.setState((state) => {
                return {
                    isRefreshing: false,
                    isRecordListFocus:true,
                    dataNormal: dataArr,
                    dataTimeArr:timeArr,
                    // dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
                    isMore: isMore,
                    normalPage:page,
                };
            }, () => {
                console.log("lrlp _queryDayData eeeeeeeeee ="+resLen+" dataArr="+JSON.stringify(dataArr));
            });
        }).catch(error=>{
            console.log('getOpenLockList--err------',JSON.stringify(error));
            IMILog.logD("jeff getOpenLockList",JSON.stringify(error));
            RRCToast.show(stringsTo('commLoadingFailText'))
            BleTools.hide()
            this.setState({ // 刷新显示列表
                isRefreshing: false,
                isRecordListFocus:true,
                dataNormal: dataArr,
                dataTimeArr:timeArr,
                // dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
                isMore: false
            });
        });
        if (isMore) {
            // LetDevice.sendDeviceServerRequest("LockOpenRecordList", {
            //     "BeginTime": startTime.toString(),
            //     "EndTime": endTime.toString(),
            //     "QuerySize": MAX_LEN,//传10会返回MAX_LEN
            //     "OpenType": -1
            // }, true).then(result => {
            //     let dataJson =[];
            //     console.log("lrlp _queryDayData="+JSON.stringify(result));
            //     let resultObjectToJson =JSON.parse(JSON.stringify(result));
            //     if (resultObjectToJson.LockOpenList != undefined) {
            //         dataJson = JSON.parse(resultObjectToJson.LockOpenList);
            //     }
            //     let resLen = dataJson.length;
            //     for (let i = 0; i < resLen; i++) {
            //         let openTime = dataJson[i].OpenTime;
            //         let offset = timeArr.indexOf(openTime);
            //         if (offset < 0) {
            //             let timestring = this.dateFormat(new Date(parseInt(openTime)), "yyyy/MM/dd");
            //             if(dateArray.indexOf(timestring)<0){
            //                 dateArray.push(timestring);
            //                 let dateItem = new Date(parseInt(openTime));
            //                 let dateMax= new Date(dateItem.getFullYear(), dateItem.getMonth(), dateItem.getDate(), 23, 59, 59,999);
            //                 dataArr.push({type: "title", time: timestring, OpenTime:dateMax.getTime().toString()});
            //             }
            //             dataArr.push(dataJson[i]);
            //             timeArr.push(openTime);
            //         }
            //     }
            //     if (resLen < MAX_LEN) {
            //         isMore = false;
            //     }
            //     dataArr.sort((a, b) => {
            //         return parseInt(b.OpenTime) - parseInt(a.OpenTime);
            //     });
            //     BleTools.hide()
            //     this.setState((state) => {
            //         return {
            //             isRefreshing: false,
            //             isRecordListFocus:true,
            //             dataNormal: dataArr,
            //             dataTimeArr:timeArr,
            //             dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
            //             isMore: isMore
            //         };
            //     }, () => {
            //         console.log("lrlp _queryDayData eeeeeeeeee ="+resLen+" dataArr="+JSON.stringify(dataArr));
            //     });
            //
            // }).catch(err => {
            //     console.log("lrlp 获取开锁记录有误---11111---" + JSON.stringify(err));
            //     console.log("lrlp 获取开锁记录有误---11111---", err);
            //
            //     RRCToast.show(stringsTo('commLoadingFailText'))
            //     BleTools.hide()
            //     this.setState({ // 刷新显示列表
            //         isRefreshing: false,
            //         isRecordListFocus:true,
            //         dataNormal: dataArr,
            //         dataTimeArr:timeArr,
            //         dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
            //         isMore: false
            //     });
            //
            // });
        }
    }

    /***
     * 获取安全事件列表
     * @param currTime 当前时间
     * @private
     */
    _querySecurityEventList(currTime) {
        let endTime = currTime;
        let startTime = endTime - MAXINT;
        let dataArr = this.state.warningNormal;
        let timeArr = this.state.warningTimeArr;
        let isMore = true;
        console.log("lrlp _querySecurityEventList currTime="+this.state.warningPage);
        BleTools.loading(stringsTo("loading"));
        imiAlarmEventCloudApi.getOpenLockEventList(LetDevice.deviceID,this.state.warningPage,MAX_LEN).then(res=>{
            console.log('getOpenLockList--res------',JSON.stringify(res));
            let dataJson = res;
            // let resultObjectToJson =JSON.parse(JSON.stringify(result));
            // if (resultObjectToJson.WarningEventList != undefined) {
            //     dataJson = JSON.parse(resultObjectToJson.WarningEventList);
            // }
            let resLen = dataJson.length;
            if(dataArr.length > 0 ){
                //重置底部时间轴
                if(dataArr[dataArr.length -1].type === "none"){
                    dataArr[dataArr.length -1].type = "down";
                }else if(dataArr[dataArr.length -1].type === "up"){
                    dataArr[dataArr.length -1].type = "double";
                }
                //重置顶部时间轴
                if(dataArr[1].type == "down"){
                    dataArr[1].type = "double";
                }else if(dataArr[1].type == "none"){
                    dataArr[1].type = "up";
                }
            }
            for (let i = 0; i < resLen; i++) {
                // let dateTime = dataJson[i].DateTime;
                let dateTime = dataJson[i].eventTime;
                let offset = timeArr.indexOf(dateTime);
                if (offset < 0) {
                    let time = parseInt(dateTime)>1000000000000?parseInt(dateTime):parseInt(dateTime)*1000;
                    let timestring = this.dateFormat(new Date(parseInt(time)), "yyyy/MM/dd");
                    if(securityDateArray.indexOf(timestring)<0){
                        securityDateArray.push(timestring);
                        let dateItem = new Date(parseInt(time));
                        let dateMax= (new Date(dateItem.getFullYear(), dateItem.getMonth(), dateItem.getDate(), 23, 59, 59,999)).getTime();
                        if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                            dataArr[dataArr.length -1].type = "none";
                        }else if(dataArr.length > 0){
                            dataArr[dataArr.length -1].type = "up";
                        }
                        //恢复顶部时间轴
                        if(dataArr.length > 0){
                            if(dataArr[1].type == "double"){
                                dataArr[1].type = "down";
                            }else if(dataArr[1].type == "up"){
                                dataArr[1].type = "none";
                            }
                        }
                        dataArr.push({type: "title", time: timestring, eventTime:dateMax.toString()});
                        dataJson[i].type = "down";
                    }
                    dataArr.push(dataJson[i]);
                    timeArr.push(dateTime);
                }
            }
            let page ;
            if (resLen < MAX_LEN) {
                isMore = false;
                page = 0;
            }else {
                page = this.state.warningPage+1;
            }
            console.log("lrlp _querySecurityEventList currTime="+page,isMore);

            dataArr.sort((a, b) => {
                let aTime = parseInt(a.eventTime)>1000000000000?parseInt(a.eventTime):parseInt(a.eventTime)*1000;
                let bTime = parseInt(b.eventTime)>1000000000000?parseInt(b.eventTime):parseInt(b.eventTime)*1000;

                return bTime - aTime;
            });
            console.log("lrlp _querySecurityEventList currTime="+page,isMore,'---',dataArr);
            //更新底部时间轴
            if(dataArr.length >1 && dataArr[dataArr.length -2].type === "title"){
                dataArr[dataArr.length -1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[dataArr.length -1].type = "up";
            }
            //更新顶部时间轴
            if(dataArr.length > 2 && dataArr[2].type === "title"){
                dataArr[1].type = "none";
            }else if(dataArr.length > 0){
                dataArr[1].type = "down";
            }
            BleTools.hide()

            this.setState({ // 刷新显示列表
                isRefreshing: false,
                isRecordListFocus:false,
                warningNormal: dataArr,
                warningTimeArr:timeArr,
                // dataSourceNormal: dataArr,
                isMore: isMore,
                warningPage:page,
            });

        }).catch(error=>{
            console.log('getOpenLockList--err------',JSON.stringify(error));
            RRCToast.show(stringsTo('commLoadingFailText'))
            BleTools.hide()
            if (this.state.isRead){
                this.setState({ // 刷新显示列表
                    isRefreshing: false,
                    isRecordListFocus:false,
                    warningNormal: dataArr,
                    warningTimeArr:timeArr,
                    // dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
                    isMore: false
                });
            }else {
            }

        });
        // if (isMore) {
        //     LetDevice.sendDeviceServerRequest("WarningEventList", {
        //         "BeginTime": startTime.toString(),
        //         "EndTime": endTime.toString(),
        //         "QuerySize": MAX_LEN,//传10会返回MAX_LEN
        //         "WarningType": -1
        //     }, true).then(result => {
        //         let dataJson =[];
        //         console.log("lrlp _querySecurityEventList resultObjectToJson="+JSON.stringify(result));
        //         let resultObjectToJson =JSON.parse(JSON.stringify(result));
        //         if (resultObjectToJson.WarningEventList != undefined) {
        //             dataJson = JSON.parse(resultObjectToJson.WarningEventList);
        //         }
        //         let resLen = dataJson.length;
        //         for (let i = 0; i < resLen; i++) {
        //             let dateTime = dataJson[i].DateTime;
        //             let offset = timeArr.indexOf(dateTime);
        //             if (offset < 0) {
        //                 let timestring = this.dateFormat(new Date(parseInt(dateTime)), "yyyy/MM/dd");
        //                 if(securityDateArray.indexOf(timestring)<0){
        //                     securityDateArray.push(timestring);
        //                     let dateItem = new Date(parseInt(dateTime));
        //                     let dateMax= new Date(dateItem.getFullYear(), dateItem.getMonth(), dateItem.getDate(), 23, 59, 59,999);
        //                     dataArr.push({type: "title", time: timestring, DateTime:dateMax.getTime().toString()});
        //                 }
        //                 dataArr.push(dataJson[i]);
        //                 timeArr.push(dateTime);
        //             }
        //         }
        //         if (resLen < MAX_LEN) {
        //             isMore = false;
        //         }
        //         dataArr.sort((a, b) => {
        //             return parseInt(b.DateTime) - parseInt(a.DateTime);
        //         });
        //         BleTools.hide()
        //         this.setState({ // 刷新显示列表
        //             isRefreshing: false,
        //             isRecordListFocus:false,
        //             warningNormal: dataArr,
        //             warningTimeArr:timeArr,
        //             dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
        //             isMore: isMore
        //         });
        //     }).catch(err => {
        //         console.log("lrlp 获取安全事件有误---222---" + JSON.stringify(err));
        //         RRCToast.show(stringsTo('commLoadingFailText'))
        //         BleTools.hide()
        //         this.setState({ // 刷新显示列表
        //             isRefreshing: false,
        //             isRecordListFocus:false,
        //             warningNormal: dataArr,
        //             warningTimeArr:timeArr,
        //             dataSourceNormal: this.state.dataSourceNormal.cloneWithRows(dataArr),
        //             isMore: false
        //         });
        //
        //     });
        // }
    }
}

let styles = StyleSheet.create({
    containerAll: {
        flex: 1,
        flexDirection: 'column',
        backgroundColor: 'rgb(244,244,244)'
        // justifyContent:'center',
        // alignItems:'center',
        // alignSelf:'center',
    },
    contentView: {
        height: screenHeight - NavigatorBarHeight,
        marginTop: NavigatorBarHeight,
        flexDirection: 'column'
    },
    headView: {
        height: headViewHeight,
        width: screenWidth,
        flexDirection: 'row',
        backgroundColor: 'white'
    },
    tableView: {
        alignSelf: 'stretch',
        height: (screenHeight - NavigatorBarHeight) - headViewHeight
    },
    remoterListRowCell: {
        flexDirection: 'row',
        padding: 22,
        height: 64
    },
    remoterImage: {
        alignSelf: 'center',
        width: 18,
        height: 64
        // backgroundColor:'#0ff',
    },
    remoterName: {
        fontSize: 15,
        opacity: 0.7,
        alignSelf: 'center',
        color: '#000000',
        marginLeft: 15
    },
    subArrow: {
        width: 9,
        height: 18
    },
    separator: {
        height: 0.5,
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
        marginLeft: 20 + 50 + 15,
        marginRight: 0
    }
});

