'use strict';

import BleTools from "../Tool/BleTools";

import React from 'react';
import {
  StyleSheet,
  View,
  StatusBar,
  BackHandler,TouchableOpacity,Text
} from 'react-native';

import DoubleLineSwitchCell from '../Cell/DoubleLineSwitchCell';
import SingleLineTextInputCell from '../Cell/SingleLineTextInputCell';
import SingleLineArrowCell from '../Cell/SingleLineArrowCell';
import SectionTitleCell from '../Cell/SectionTitleCell';
import { ExpireDateType, UserPwdType, LockListType, MiRawStyle} from '../Tool/RegularConstDefine';
import ConstDefine from '../Tool/ConstDefine';
import { base_GetRowSubTitle, _getSubTitle_WithType, _getModel_WithType } from '../Tool/TimerManager';
import {RRCToast,RRCAlert} from "imilab-design-ui/src/widgets/overlayer";
import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import DoorBleManager from "../../utils/DoorBleManager";
import {stringsTo} from "../../../../../globalization/Localize";
import RRCTopView from "../../../../../imilab-design-ui/src/widgets/overlayer/RRCTopView";
import SettingItemView from "../../CommonView/SettingItemView/SettingItemView";
import {imiAlarmEventCloudApi, LetDevice} from "../../../../../imilab-rn-sdk";
import IMILogUtil from "../../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import NetInfo, {NetInfoStateType} from "@react-native-community/netinfo";
import MiRawUtils from "../Tool/MiRawUtils";

const screenWidth = ConstDefine.screenWidth;
const screenHeight = ConstDefine.screenHeight;
const APPBAR_HEIGHT = ConstDefine.APPBAR_HEIGHT;
const NavigatorBarHeight = ConstDefine.NavigatorBarHeight;
const SafeBottomHeight = ConstDefine.SafeBottomHeight;
const TextFounsListener = 'TextFounsListener';

export default class AddPassWordPage extends React.Component {
  constructor(props, context) {
    super(props, context);

    let dataTypeDefaultSubTitle = _getSubTitle_WithType(ExpireDateType.ForeverDay, null, null);

    this.state = {
      ...this.state,
      passWordJsonModel: {}, /* 保存了当前所有状态的 json对象 */
      passwordName: null, /* 密码名称 */
      passWord: null, /* 密码名称 */
      dateTypeSubTitle: dataTypeDefaultSubTitle,
      timeString: '',
      dateType: ExpireDateType.ForeverDay,
      hijackValue: UserPwdType.UserType,
      hijack: false,
      saveState: false, // 保存按钮状态
      jumpToOther: false
    };
    this.disState = false;// 页面是否消失了
    this.saveCilck = false;// 保存按钮点击状态 点击事件处理中不执行事件
  }

  static navigationOptions = ({ navigation }) => {
    return {
      header: null
    };
  };
//     right={this.state.saveState === true
//         ? [{key: NavigationBar.ICON.COMPLETE, showDot: false, onPress: () => this.onRightSaveButton()}]
//         : []
// }
  render() {

    return (
        <View style={styles.containerAll}>
          <StatusBar/>
          <NavigationBar
              type={NavigationBar.TYPE.LIGHT}
              backgroundColor={"#FFFFFF"}
              left={[{key: NavigationBar.ICON.BACK, onPress: (_) => this.onBackButton()}]}
              title={stringsTo("pwd_add")}

          />
          <View style={styles.contentView}>
            {
              this.showContentView()
            }
          </View>
          {this._renderBottomButton()}
        </View>
    );
  }

  showContentView() {

    return (
        <View style={styles.emptyView}>
          <SingleLineTextInputCell
              style={{width: screenWidth, height: 60, backgroundColor: '#ffffff' }}
              title={stringsTo("pwd_add_name_hint")}
              placeholder={stringsTo("pwd_add_name_input_hint")} showLine={false}
              inputValue={this.state.passwordName}
              onChangeText={(event) => this.nameInputOnChange(event)}
          />
          <SingleLineTextInputCell
              style={{width: screenWidth, height: 60, backgroundColor: '#ffffff' }}
              title={stringsTo("pwd_add_pwd_hint")} placeholder={stringsTo("pwd_add_pwd_input_hint")} showLine={false}
              ButtonTitle={stringsTo("pwd_random")}
              keyboardType="number-pad"
              inputValue={this.state.passWord}
              showInputLength={6}
              maxInputLength={6}
              onPressTitleButton={() => this.onPress_GetRandPassWordButton()}
              onChangeText={(event) => this.textInputOnChange(event)}
              textInputRefer={'textInputRefer'}
          />
          <SectionTitleCell
              style={{ paddingLeft:10,width: screenWidth, height: 56, backgroundColor: 'rgb(244,244,244)' }}
              title={stringsTo("pwd_save_hint")}
              showLine={false}
          />

          <SettingItemView title={stringsTo("pwd_valid_time")}
                           style={{
                             backgroundColor: (this.state.hijack ? 'rgb(244,244,244)' : '#ffffff')
                           }}
                           value={this.state.dateTypeSubTitle}
                           bottomLine={SettingItemView.LINE.NONE}
                           onPress={() => this.onSelectRow()}/>

          <SettingItemView title={stringsTo("pwd_anti_kidnapping_set")}
                           titleStyle={{color:'#4A4C4E'}}
                           subtitle={stringsTo('pwd_anti_kidnapping_hint')}
                           bottomLine={SettingItemView.LINE.NONE}
                           type={SettingItemView.TYPE.SWITCH}
                           switchValue={this.state.hijack}
                           onSwitchChange={(event) => this.switchOnValueChange(event)}/>
        </View>
    );
  }
  _renderBottomButton(){
    return(
        <TouchableOpacity
            disabled={!this.state.saveState}
            style={{
              position: 'absolute',
              bottom:20,
              width:screenWidth-40,
              height:48,
              borderRadius: 7,
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
              backgroundColor: '#1E5BA9',opacity: this.state.saveState ? 1 : 0.4, }} onPress={() => {
          this.onRightSaveButton();
        }}>
          <Text style={{ color: '#ffffff', fontSize: 15 }}>{stringsTo('account_next')}</Text>
        </TouchableOpacity>
    )
  }
  switchOnValueChange(event) {
    if (event == true) {
      this.setState({ hijackValue: UserPwdType.AlarmType, hijack: true });
      this.UserSelectDateTypeAndTimer(4, null, null);
    } else {
      this.setState({ hijackValue: UserPwdType.UserType, hijack: false });
    }
  }

  nameInputOnChange(inputText) {
    this.state.passwordName = inputText;
    if (this.state.passwordName === null
        || this.state.passwordName === ''
        || this.state.passWord === null
        || this.state.passWord === '') {
      this.setState({ saveState: false });
    } else {
      this.setState({ saveState: true });
    }
  }

  /* 输入密码监听事件 */
  textInputOnChange(inputWordText) {

    this.state.passWord = inputWordText;
    if (this.state.passwordName === null
        || this.state.passwordName === ''
        || this.state.passWord === null
        || this.state.passWord === '') {
      this.setState({ saveState: false });
    } else {
      this.setState({ saveState: true });
    }
  }

  /* 点击了获取随机密码的按钮 */
  onPress_GetRandPassWordButton() {

    let randomPassWord = this.GetRandomPassword();
    this.setState({ passWord: randomPassWord }, () => {
      if (this.state.passwordName === null
          || this.state.passwordName === ''
          || this.state.passWord === null
          || this.state.passWord === '') {
        this.setState({ saveState: false });
      } else {
        this.setState({ saveState: true });
      }
    });
  }

  onBackButton() {
    if (this.saveCilck)return;
    if (this.state.saveState === true) {
      RRCAlert.alert(stringsTo("prompt"), stringsTo('exit_without_save_hint'), [
        {
          text: stringsTo("cancel"),
            style:{color:'#B2B2B2'}
        },
        {
          text: stringsTo("quit"),
            style:{color:'#1E5BA9'}
        }
      ], (index) => {
        if (index != 0) {
          this.props.navigation.goBack();
        }
      });
    } else {
      this.props.navigation.goBack();
    }
  }

  /* 点击了保存按钮 */
  onRightSaveButton() {
    if (!this.state.saveState) {
      return;
    }
    if (this.saveCilck) {
      return;
    }

    // 无网弹窗提醒
    NetInfo.fetch().then(state => {
      if(!state.isConnected && state.type === NetInfoStateType.none) {
        this._noNetworkShow();
      } else {
        if (this.state.dateType == null) {
          RRCToast.show(stringsTo('date_failnull'));
          return;
        } else if (this.state.passWord == null || (this.state.passWord.length != 6) || (this.state.passWord === undefined)) {
          RRCToast.show(stringsTo('pwd_add_pwd_input_hint'));
          return;
        } else if (this.state.passwordName == null || (this.state.passwordName === undefined) || (this.state.passwordName == '')) {
          RRCToast.show(stringsTo('pwd_add_name_input_hint'));
          return;
        } else if (BleTools.bleConnectState === false) {
          RRCToast.show(stringsTo('ble_disconnect_prompt'));
          return;
        }
        BleTools.loading(stringsTo('commLoadingText'));
        BleTools.passWordInfoManager.getNewPassWordIndex((newIndex) => {
          // let self = this;
          if (this.state.passWordJsonModel.validityType === undefined) {
            this.state.passWordJsonModel.validityType = this.state.dateType;
          }
          this.state.passWordJsonModel.name= this.state.passwordName;
          this.state.passWordJsonModel.passWord = this.state.passWord;
          this.state.passWordJsonModel.keyId = newIndex;
          this.state.passWordJsonModel.property = this.state.hijackValue;
          this.state.passWordJsonModel.timeString = this.state.timeString;
          this.saveCilck = true;
          this.StartWriteToBleTooth(newIndex);
        });
      }
    });
  }

  StartWriteToBleTooth(passwordIndex) {
    let self = this;
    let passWord = this.state.passWord;
    let validityType = this.state.passWordJsonModel.validityType;
    let startTime = this.state.passWordJsonModel.startData;
    let endTime = this.state.passWordJsonModel.endData;
    let weekArray = this.state.passWordJsonModel.repeat;
    let hijackValue = this.state.hijackValue;
    let failedCallBack = () => {
      RRCToast.show(stringsTo('operate_time_out'));
      this.saveCilck = false;
    };
    BleTools.protocolManager.Add_PassWord(passwordIndex, passWord, validityType, startTime, endTime, weekArray, hijackValue, (commandStr) => {
      DoorBleManager.getInstance().sendCmd_device_add_pwd(commandStr).then(
          data => {
            BleTools.failedTimeout && clearTimeout(BleTools.failedTimeout);
            if (!this.disState) {
              failedCallBack = null;
              if (data.isSuccess == true) {
                RRCToast.show(stringsTo('save_success'));
                IMILogUtil.uploadClickEventValue({DoorLockAddPwd:this.state.passwordName})
                this.createPwdSuccessLogic();
              } else {
                BleTools.hide();
                if (data.message === stringsTo('fail_repeat')) {
                  RRCToast.show(stringsTo('fail_add_pwd'));
                } else {
                  RRCToast.show(data.message);
                }
                setTimeout(() => {
                  this.saveCilck = false;
                }, 100);
              }
            }
          }).catch(err=> {
            BleTools.hide();
            if (!this.disState) {
              if (failedCallBack) {
                failedCallBack();
              }
            }
          });
      }, () => {
      BleTools.hide();
      if (!this.disState) {
        if (failedCallBack) {
          failedCallBack();
        }
      }
    });
  }
  // // 上传密码push名称
  // upLoadPsName() {
  //   let index = ASCII_To_Hex(this.state.passWordJsonModel.index);
  //   if (index.length < 2) {
  //     index = `0${ index }`;
  //   }
  //   let keyId = `${ LockListType.Head + LockListType.PassWord + index }00`;
  //   BleTools.getMiCloudApi().setSetting(`keyid_${ parseInt(keyId, 16) }_ali`, this.state.passwordName, (response) => {
  //     console.log('----------------', response);
  //   });
  // }

  createPwdSuccessLogic() {
    let self = this;
    BleTools.passWordInfoManager.uploadNewPassWord(self.state.passWordJsonModel.keyId, self.state.passWordJsonModel, (response) => {
      BleTools.hide();
      if(response) {
        RRCToast.show(stringsTo('success_upload'));

        let miRawData = MiRawUtils.getMiRawData(self.state.passWordJsonModel.keyId, LockListType.PassWord,
            this.state.passwordName, MiRawStyle.ADD);
        //认证--添加密码 //dengying@20220107
        let array = [];
        let jsonModel = {
          iotId: LetDevice.deviceID,
          openTime:new Date().getTime().toString(),
          userName:this.state.passwordName,
          openType:24,
          message:"",
          MiRaw: miRawData,
        };
        array.push(jsonModel);
        imiAlarmEventCloudApi.uploadUnlockRecord(array).then(res=>{
          console.log('uploadUnlockRecord--res------',JSON.stringify(res));
        }).catch(error=>{
          console.log('uploadUnlockRecord--err------',JSON.stringify(error));
        });

        setTimeout(() => {
          self.props.navigation.goBack();
        }, 1000);
        if (self.props.route.params.createPassWordSuccess) {
          self.props.route.params.createPassWordSuccess();
        }
      }else{
        this.saveCilck = false;
        this._noNetworkShow();
        this._delLockPwd(self.state.passWordJsonModel.keyId);
        RRCToast.show(stringsTo('fail_upload'));
        self.props.navigation.goBack();
      }
    });
  }

  onSelectRow(event) {
    let self = this;
    if (this.state.hijack) return;
    this.setState({ jumpToOther: true });
    console.log("self.state.pawordJsonModle = "+JSON.stringify(this.state.passWordJsonModel))
    this.props.navigation.push('EffectiveDatePage', {
      title: stringsTo('pwd_valid_time'),
      validityType: this.state.passWordJsonModel.validityType,
      startTimer: this.state.passWordJsonModel.startData,
      endTimer: this.state.passWordJsonModel.endData,
      weekArray: this.state.passWordJsonModel.repeat,
      timeString: this.state.timeString,
      selectDateRow: (dateType, startTimer, endTimer) => {
        this.setState({ jumpToOther: false });
        console.log("跳转回来，一天，一周");
        this.UserSelectDateTypeAndTimer(dateType, startTimer, endTimer);
      },
      selectCustomerDateRow: (startTimer, endTimer, message) => {
        console.log("跳转回来，自定义时段");
        this.setState({ jumpToOther: false });
        this.UserSelectCustomerDate(startTimer, endTimer, message);
      },
      selectIntervalCustomerDateRow: (startTimer, endTimer, weekArray, message) => {
        console.log("跳转回来，定时密码");
        this.setState({ jumpToOther: false });
        this.UserSelectIntervalCustomerDateAndWeeks(startTimer, endTimer, weekArray, message);
      }
    });
  }

  /* important 用户选择了时间类型 和开始时间--结束时间 */
  UserSelectDateTypeAndTimer(dateType, startTimer, endTimer) {
    let subTitle = _getSubTitle_WithType(dateType);
    let jsonModel = _getModel_WithType(dateType, startTimer, endTimer);
    let timeString = base_GetRowSubTitle(dateType, jsonModel.startData, jsonModel.endData);
    let startTime = new Date(jsonModel.startData).getTime();
    let endTime = new Date(jsonModel.endData).getTime();
    jsonModel.startData = startTime;
    jsonModel.endData = endTime;
    this.setState({ dateTypeSubTitle: subTitle, passWordJsonModel: jsonModel, timeString: timeString });
    console.log(`dateType:${ dateType }`);
    console.log(`startTimer:${ startTimer }`);
    console.log(`endTimer:${ endTimer }`);
  }

  /* important 用户选择了自定义时间 开始时间--结束时间 */
  UserSelectCustomerDate(startTimer, endTimer, message) {
    let jsonModel = _getModel_WithType(ExpireDateType.IntervalTime, startTimer, endTimer);
    let timeString = base_GetRowSubTitle(ExpireDateType.IntervalTime, startTimer, endTimer);
    this.setState({ dateTypeSubTitle: message, passWordJsonModel: jsonModel, timeString: timeString });
    console.log(`AddPass--_UserSelect_CustomerDate:${ JSON.stringify(this.state.passWordJsonModel) }`);
  }

  /* important 用户选择了周期自定义时间 和开始时间--结束时间--周几 */
  UserSelectIntervalCustomerDateAndWeeks(startTimer, endTimer, weekArray, message) {
    let jsonModel = _getModel_WithType(ExpireDateType.TimingDay, startTimer, endTimer, weekArray);
    this.setState({ dateTypeSubTitle: message, passWordJsonModel: jsonModel, timeString: message });
    console.log(`AddPass--_UserSelect_IntervalCustomerDate_AndWeeks:${ JSON.stringify(this.state.passWordJsonModel) }`);
  }

  _delLockPwd(indexKeyId) {
    BleTools.protocolManager.Delete_PassWord(indexKeyId, (commandStr) => {
      DoorBleManager.getInstance().sendCmd_device_pwd_del(commandStr).then(data => {
      }).catch(error => {
      });
    }, () => {
    });
  }

  _noNetworkShow() {
    RRCAlert.alert(stringsTo('prompt'), stringsTo('no_network_msg'), [
      {
        text: stringsTo('know_button'),
        style:{color:'#1E5BA9'}
      }
    ], (index) => {});
  }

  /* ----------------------------------功能--分割线---------------------------------------------------- */

  componentDidMount() {
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        if (this.saveCilck)return true;
        RRCTopView.removeAll();
        if (this.state.saveState && !this.state.jumpToOther) {
        console.log("添加密码----未保存提示对话框");
        RRCAlert.alert(stringsTo('prompt'), stringsTo('exit_without_save_hint'), [
          {
            text: stringsTo('cancel'),
              style:{color:'#B2B2B2'}
          },
          {
            text: stringsTo('quit'),
              style:{color:'#1E5BA9'}
          }
        ], (index) => {
          if (index != 0) {
            this.props.navigation.goBack();
          }
        });
      } else {
        return false;
      }
      return true;
    });
  }

  componentWillUnmount() {
    BleTools.hide();
    this.disState = true;
    BleTools.failedTimeout && clearTimeout(BleTools.failedTimeout);
    BleTools.failedTimeout = null;
    this.backHandler.remove();
  }

  /* 获取随机密码 */
  GetRandomPassword() {
    const passWordLenght = 6;
    let randomArray = [0, 0, 0, 0, 0, 0];
    randomArray.forEach((element, index) => {
      let number = Math.floor(Math.random() * 10);
      randomArray[index] = number;
    });
    let resultString = randomArray.join('');
    return resultString;
  }
}

const sectionWidth = screenWidth - 20 * 2;
const marginHeight = 10;
const emptyViewMarginTop = (screenHeight - SafeBottomHeight - NavigatorBarHeight) / 3;

var styles = StyleSheet.create({

  containerAll: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 0,
    backgroundColor: 'rgb(244,244,244)'
  },
  contentView: {
    marginTop: 0,
    flexDirection: 'column'

  },
  emptyView: {
    flexDirection: 'column',
    backgroundColor: '#ffffff'
  },
  addPassWordButtonView: {
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 15,
    paddingRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    backgroundColor: 'rgb(64,109,226)',
    marginTop: marginHeight,
    borderRadius: 5
  },
  addPassWordButton: {
    height: 30
  },
  Title: {
    fontSize: 16,
    color: 'rgba(0,0,0,0.8)',
    marginTop: marginHeight
  },
  subTitle: {
    fontSize: 15,
    color: '#808080',
    marginTop: marginHeight
  },
  addPassWordButton1: {},
  addPassWordButton2: {}

});

