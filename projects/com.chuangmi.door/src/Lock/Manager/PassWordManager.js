import React from 'react';
import { ASCII_To_Hex, DATA_TO_STRING,StrToHexCharCode } from '../Tool/EnCodeTool';
import { CRC8_MaxIM } from '../Tool/CRC8MaxIM';
let ValidityTimerType = { OneDay: 0, SevenDay: 1, IntervalTime: 2, TimingDay: 3, ForeverDay: 4 };
import BleTools from '../Tool/BleTools';

import SerialClass from '../Tool/SerialClass';

import { PROTOCOL_HEADER, FUN_HEADER } from './FunctionDefine';
import DoorCmdManager from "../../utils/DoorCmdManager";

const Password_Hex = 'key_num_pwd_data_';
const Finger_Hex = 'key_fp_pwd_data_';
// NFC密码数据头
const NFC_Header = 'key_nfc_data_';
export {DoorCmdManager};

class PassWordManager extends React.Component {
  constructor(props) {
    super(props);
  }

  /* 创建密码 */

  /* 密码--时间段类型-- */
  getCreateUserPassord(passwordIndex, PassWord, TimerType, startTime, endTime, WeeksDay, hijack) {

    if (passwordIndex == null) {
      passwordIndex = BleTools.uplodManager.getNewPassWordIndex();
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }

    let header = this.getPassWordHeader(FUN_HEADER.Add_PassWord);
    let data = this.createUserPassord(passwordIndex, PassWord, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }


  /* 修改密码属性（不修改密码）-- */
  getEditUserPassord(passwordIndex, TimerType, startTime, endTime, WeeksDay, hijack) {

    if (passwordIndex == null) {
      console.log('入参--passwordIndex==null');
      return '';
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }


    let header = this.getPassWordHeader(FUN_HEADER.Edit_PassWord);
    let data = this.editUserPassword(passwordIndex, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }


  /* 密码--查询-- */
  getValidationPassord(passwordIndex, PassWord, newPassWord) {

    if (passwordIndex == null) {
      console.log('入参--passwordIndex==null');
      return '';
    }

    let header = this.getPassWordHeader(FUN_HEADER.Validation_PassWord);
    let data = this.createValidationPassord(passwordIndex, PassWord, newPassWord);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }


  /* 创建指纹 */

  /* 密码--时间段类型--(非定时) */
  getCreateUserFinger(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack) {

    console.log('fingerIndex----getCreateUserFinger----',fingerIndex);
    if (fingerIndex == null) {

      BleTools.uploadManager.getNewFingerIndex((index) => {
        if (typeof (startTime) == 'string') {
          startTime = new Date(startTime);
        }
        if (typeof (endTime) == 'string') {
          endTime = new Date(endTime);
        }

        let header = this.getFingerHeader(FUN_HEADER.Add_Finger);
        let data = this.createUserFinger(index, TimerType, startTime, endTime, WeeksDay, hijack);
        let temp = header.concat(data);

        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
      });
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }

    let header = this.getFingerHeader(FUN_HEADER.Add_Finger);
    let data = this.createUserFinger(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 修改用户指纹 */
  getEditUserFinger(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack) {
    let fgIndex = fingerIndex;
    if (fingerIndex.toString().startsWith(Finger_Hex)) {
      fgIndex = fingerIndex.toString().substring(Finger_Hex.length);
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }
    let header = this.getFingerHeader(FUN_HEADER.Edit_Finger);
    let data = this.editUserPassword(fgIndex, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 取消用户指纹 */
  getCancelUserFinger(fingerIndex) {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Cancel_Finger;
      // this.deletePasswordData([fingerIndex], 0)
    let dataArray = [0x00, 0x01,ASCII_To_Hex(parseInt(fingerIndex))];
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

    /* 密码--时间段类型--(非定时) */
    getValidationUserFinger(fingerIndex) {
        let header = this.getFingerHeader(FUN_HEADER.Validation_Finger);
        let fgIndex = fingerIndex;
        if (fingerIndex.toString().startsWith(Finger_Hex)) {
            fgIndex = fingerIndex.toString().substring(Finger_Hex.length);
        }
        console.log(`创建指纹index:${ fgIndex }`);
        let userFingerArray = [ASCII_To_Hex(parseInt(fgIndex))];
        /* 添加 data 长度 */
        let DataSection = this.packageData(userFingerArray);
        let temp = header.concat(DataSection);
        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
    }
  /* 删除密码 */
  getDeletePassWord(passwordIndex) {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Del_PassWord;
    let dataArray = this.deletePasswordData([passwordIndex], 0);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 删除密码 */ // 可选择类型
  getDeletePassWordWithType(passwordIndex, type) {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Del_PassWord;
    let dataArray = this.deletePasswordData(passwordIndex, type);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 删除指纹 */
  getDeleteFinger(fingerIndex) {
    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Del_Finger;
    let dataArray = this.deletePasswordData([fingerIndex], 0);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 删除指纹 */ // 可选择类型
  getDeleteFingerWithType(fingerIndex, type) {
    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Del_Finger;
    let dataArray = this.deletePasswordData(fingerIndex, type);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 删除NFC */
  getDeleteNFC(Index) {
    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.NFC_Delete;
    let dataArray = this.deletePasswordData([Index], 0);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 删除NFC */ // 可选择类型
  getDeleteNFCWithType(Index, type) {
    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.NFC_Delete;
    let dataArray = this.deletePasswordData(Index, type);
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 查询所有密码 */
  GetAllPassWord() {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Query_AllPassWord;
    let dataArray = [0x00, 0x00];
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }


  /* 查询所有指纹 */
  getAllFinger() {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.Query_AllFinger;
    let dataArray = [0x00, 0x00];
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* -----------------------------NFC----------------------------------------- */

  /* 创建NFC */

  /* NFC--时间段类型--(非定时) */
  getCreateUserNFC(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack) {

    if (fingerIndex == null) {

      BleTools.uploadManager.getNewNFCIndex((index) => {
        if (typeof (startTime) == 'string') {
          startTime = new Date(startTime);
        }
        if (typeof (endTime) == 'string') {
          endTime = new Date(endTime);
        }

        let header = this.getFingerHeader(FUN_HEADER.NFC_Add);
        let data = this.createUserNFC(index, TimerType, startTime, endTime, WeeksDay, hijack);
        let temp = header.concat(data);

        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
      });
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }

    let header = this.getFingerHeader(FUN_HEADER.NFC_Add);
    let data = this.createUserNFC(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 修改用户NFC */
  getEditUserNFC(fingerIndex, TimerType, startTime, endTime, WeeksDay, hijack) {
    let fgIndex = fingerIndex;
    if (fingerIndex.toString().startsWith(NFC_Header)) {
      fgIndex = fingerIndex.toString().substring(NFC_Header.length);
    }
    if (typeof (startTime) == 'string') {
      startTime = new Date(startTime);
    }
    if (typeof (endTime) == 'string') {
      endTime = new Date(endTime);
    }
    let header = this.getFingerHeader(FUN_HEADER.NFC_AddBack);
    let data = this.editUserPassword(fgIndex, TimerType, startTime, endTime, WeeksDay, hijack);
    let temp = header.concat(data);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 取消用户指纹 */
  getCancelUserNFC(fingerIndex) {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.NFC_Cancel;
    let dataArray = [0x00, 0x00];
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

  /* 查询所有NFC */
  getAllNFC() {

    let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
    let functionArray = FUN_HEADER.NFC_Query;
    let dataArray = [0x00, 0x00];
    let temp = commadArray.concat(functionArray).concat(dataArray);

    let PacketData = this.packetArray(temp);
    let PacketString = DATA_TO_STRING(PacketData);
    return PacketString;
  }

    /* 人脸--时间段类型-- */
    getCreateUserFace(name, TimerType, startTime, endTime, WeeksDay) {

        if (typeof (startTime) == 'string') {
            startTime = new Date(startTime);
        }
        if (typeof (endTime) == 'string') {
            endTime = new Date(endTime);
        }

        let header = this.getPassWordHeader(FUN_HEADER.FACE_Add);
        let data = this.createUserFace(name, TimerType, startTime, endTime, WeeksDay);
        let temp = header.concat(data);

        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
    }


    /* 人脸--时间段类型-- */
    getEditUserFace(id,name, TimerType, startTime, endTime, WeeksDay) {

        if (typeof (startTime) == 'string') {
            startTime = new Date(startTime);
        }
        if (typeof (endTime) == 'string') {
            endTime = new Date(endTime);
        }

        let header = this.getPassWordHeader(FUN_HEADER.FACE_EDIT);
        let data = this.editUserFace(id,name, TimerType, startTime, endTime, WeeksDay);
        let temp = header.concat(data);

        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
    }

    deleteUserFace(id){
        let commadArray = PROTOCOL_HEADER.concat(this.getSerialNumber());
        let functionArray = FUN_HEADER.FACE_DELETE;
        let dataArray = [0x00, 0x01, ASCII_To_Hex(parseInt(id))];
        let temp = commadArray.concat(functionArray).concat(dataArray);

        let PacketData = this.packetArray(temp);
        let PacketString = DATA_TO_STRING(PacketData);
        return PacketString;
    }
  /* -----------------------------功能分割线----------------------------------------- */


  deletePasswordData(ID_Array, deleteType) {
    /* 0删除一个密码/ 1删除多个密码/2 删除所有密码 */


    if (deleteType == 0) {
      return this.deleteOnePassword(ID_Array[ID_Array.length - 1]);

    } else if (deleteType == 1) {
      return this.deleteMutablePassword(ID_Array);

    } else if (deleteType == 2) {
      return this.deleteAllPassword();
    }

  }

  /* 删除一个密码 */
  deleteOnePassword(password_ID) {
    let length = [0x00, 0x02];
    let pw_ID = password_ID;
    if (password_ID.toString().startsWith(Password_Hex)) {
      pw_ID = password_ID.toString().substring(Password_Hex.length);
    }
    if (password_ID.toString().startsWith(Finger_Hex)) {
      pw_ID = password_ID.toString().substring(Finger_Hex.length);
    }
    if (password_ID.toString().startsWith(NFC_Header)) {
      pw_ID = password_ID.toString().substring(NFC_Header.length);
    }
    let array = [0x00, ASCII_To_Hex(parseInt(pw_ID))];
    let result = length.concat(array);
    return result;
  }

  /* 删除多个密码 */
  deleteMutablePassword(passwordArray) {

    let Hex_Array = [0x01];
    passwordArray.forEach((element, index) => {
      let pw_ID = element;
      if (element.toString().startsWith(Password_Hex)) {
        pw_ID = element.toString().substring(Password_Hex.length);
      }
      if (element.toString().startsWith(Finger_Hex)) {
        pw_ID = element.toString().substring(Finger_Hex.length);
      }
      if (element.toString().startsWith(NFC_Header)) {
        pw_ID = element.toString().substring(NFC_Header.length);
      }
      let password_Hex = ASCII_To_Hex(parseInt(pw_ID));
      Hex_Array.push(password_Hex);
    });
    let passWordLength = passwordArray.length + 1;
    let length = [0x00, ASCII_To_Hex(passWordLength)];

    let result = length.concat(Hex_Array);
    return result;
  }

  /* 删除所有密码 */
  deleteAllPassword() {

    let result = [0x00, 0x1, 0x02];
    return result;
  }


  createUserFinger(fingerIndex, timerType, startTime, endTime, WeeksDay, hijack) {

      console.log('fingerIndex--------',fingerIndex);
    let fgIndex = fingerIndex;
    if (fingerIndex.toString().startsWith(Finger_Hex)) {
      fgIndex = fingerIndex.toString().substring(Finger_Hex.length);
    }
    console.log(`创建指纹index:${ fgIndex }`);
    let userFingerArray = [hijack, ASCII_To_Hex(parseInt(fgIndex))];
    /* 第二位指纹位置不能为0 ,第二位表示指纹的索引 */
    let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
    let newArray = userFingerArray.concat(dateArray);
    /* 添加 data 长度 */
    let DataSection = this.packageData(newArray);
    return DataSection;
  }


  createUserPassord(passwordIndex, password, timerType, startTime, endTime, WeeksDay, hijack) {
    let pwIndex = passwordIndex;
    if (passwordIndex.toString().startsWith(Password_Hex)) {
      pwIndex = passwordIndex.toString().substring(Password_Hex.length);
    }
    /* 此处拼接data部分 */
    let dataArray = [hijack, 0x01, 0x06];
    console.log(`创建密码index:${ pwIndex }`);
    dataArray[1] = ASCII_To_Hex(parseInt(pwIndex));

    let passwordArray = this.dealPassword(password);
    /* 时效 */
    let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
    let newArray = dataArray.concat(passwordArray).concat(dateArray);
    /* 添加 data 长度 */
    let DataSection = this.packageData(newArray);
    return DataSection;
  }

  createUserNFC(NfcIndex, timerType, startTime, endTime, WeeksDay, hijack) {

    let nfcIndex = NfcIndex;
    if (NfcIndex.toString().startsWith(NFC_Header)) {
      nfcIndex = NfcIndex.toString().substring(NFC_Header.length);
    }
    console.log(`创建指纹index:${ nfcIndex }${timerType}`);
    let userFingerArray = [hijack, ASCII_To_Hex(parseInt(nfcIndex))];
    /* 第二位指纹位置不能为0 ,第二位表示指纹的索引 */
    let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
    let newArray = userFingerArray.concat(dateArray);
    /* 添加 data 长度 */
    let DataSection = this.packageData(newArray);
      console.log(`创建指纹index:${ DataSection }`);
      return DataSection;
  }

  createValidationPassord(passwordIndex, password, newPassword) {
    let pwIndex = passwordIndex;
    if (passwordIndex.toString().startsWith(Password_Hex)) {
      pwIndex = passwordIndex.toString().substring(Password_Hex.length);
    }
    /* 此处拼接data部分 */
    let dataArray = [0x00, 0x01, 0x06];
    console.log(`创建密码index:${ pwIndex }`);
    dataArray[1] = ASCII_To_Hex(parseInt(pwIndex));

    let passwordArray = this.dealPassword(password);
    let newPdArray = this.dealPassword(newPassword);
    /* 时效 */

    let dateArray = this.foreverDayExpiryData();
    let newArray = dataArray.concat(passwordArray).concat(newPdArray).concat(dateArray);
    /* 添加 data 长度 */
    let DataSection = this.packageData(newArray);
    return DataSection;
  }

  /* 编辑密码部分-不修改密码部分 */
  editUserPassword(passwordIndex, timerType, startTime, endTime, WeeksDay, hijack) {

    let pwIndex = passwordIndex;
    if (passwordIndex.toString().startsWith(Password_Hex)) {
      pwIndex = passwordIndex.toString().substring(Password_Hex.length);
    }
    /* 此处拼接data部分 */
    let pwArray = [hijack, ASCII_To_Hex(parseInt(pwIndex))];
    /* 时效 */
    let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
    let newArray = pwArray.concat(dateArray);
    /* 添加 data 长度 */
    let DataSection = this.packageData(newArray);
    return DataSection;
  }

    createUserFace(name, timerType, startTime, endTime, WeeksDay) {


        let passwordArray = this.dealName(name);
        let dataArray = [ASCII_To_Hex(passwordArray.length)];
        /* 时效 */
        let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
        let newArray = dataArray.concat(passwordArray).concat(dateArray);
        /* 添加 data 长度 */
        let DataSection = this.packageData(newArray);
        return DataSection;
    }

    editUserFace(id,name, timerType, startTime, endTime, WeeksDay) {


        let passwordArray = this.dealName(name);
        let dataArray = [ASCII_To_Hex(parseInt(id)),ASCII_To_Hex(passwordArray.length)];
        /* 时效 */
        let dateArray = this.getExpiryDateWithType(timerType, startTime, endTime, WeeksDay);
        let newArray = dataArray.concat(passwordArray).concat(dateArray);
        /* 添加 data 长度 */
        let DataSection = this.packageData(newArray);
        return DataSection;
    }

  /* */
  createUserPassword_WithTiming() {

  }


  getPassWordHeader(headType) {
    let OneArray = PROTOCOL_HEADER;// 协议号
    let TwoArray = this.getSerialNumber();// 消息号
    let ThreeArray = headType;// 消息号
    let HeaderSection = OneArray.concat(TwoArray).concat(ThreeArray);
    return HeaderSection;
  }


  getFingerHeader(headType) {
    let OneArray = PROTOCOL_HEADER;// 协议号
    let TwoArray = this.getSerialNumber();// 消息号
    let ThreeArray = headType;// 消息号

    let HeaderSection = OneArray.concat(TwoArray).concat(ThreeArray);
    return HeaderSection;
  }


  /* 序列号 每次递增 0-254 */
  getSerialNumber() {

    let currentNumber = DoorCmdManager._getOrderCode();
    let searialNumberArray = [ASCII_To_Hex(currentNumber)];
    console.log(`searialNumberArray-----------${ searialNumberArray }`);
    return searialNumberArray;
  }

  /* data 长度 */
  packageData(dataArray) {

    let dataLength = ASCII_To_Hex(dataArray.length);
    let dataLengthArray = this.packageDatalength(dataLength, 2);
    let newData = dataLengthArray.concat(dataArray);
    // console.log('length---'+dataArray.length);
    // console.log('length--------'+dataArray.length +'-----data:'+dataLength);
    // console.log('dataArray--------'+dataArray);
    return newData;
  }


  /* 打包长度拼接 2byte */
  packageDatalength(testString, length) {

    let array = [];
    while (testString.length) {

      let index = (testString.length - length);
      let Item = testString.substring(index);
      testString = testString.substr(0, (testString.length - length));

      if (Item.length < 2) {
        Item = `0${ Item }`;
      }
      array.push(Item);
    }

    if (array.length == 0) {
      array = [0x00, 0x00];
    } else if (array.length < 2) {
      array = [0x00].concat(array);

    } else if (array.length == 2) {
      return array;
    } else { /* 大于2 */
      console.log(`数据包长度有误-------${ array.length }`);
    }
    // console.log('packageDatalength-------'+array);
    return array;
  }

  /* 通过日期类型 获取有效期 */

  /* 类型-开始时间-结束时间-周几数组 */

  getExpiryDateWithType(type, startTime, endTime, WeeksDay) {

    if (typeof (WeeksDay) == 'string') {
      WeeksDay = WeeksDay.split(",");
    }

    let dataArray = [];
    switch (type) {
      case 0:
        dataArray = this.oneDayExpiryData(startTime, endTime);
        break;

      case 1:
        dataArray = this.sevenDayExpiryData(startTime, endTime);
        break;

      case 2:
        dataArray = this.intervalTimeExpiryData(startTime, endTime);
        break;

      case 3:
        dataArray = this.orderlyTimeExpiryData(startTime, endTime, WeeksDay);
        break;

      case 4:
        dataArray = this.foreverDayExpiryData();
        break;
    }

    return dataArray;
    /*
         0-1天有效，1-7天有效，2-时间段，3-定时，4-永久
        */
  }

  /* 1天有有效 */
  oneDayExpiryData(startTime, endTime) {
    let timerArray = [0x00];
    // console.log('-------currentDate--------'+currentDate);
    // console.log('-------oneDayDate---------'+oneDayDate);
    /* 转数组 */
    let currentDateArray = this.getDataWithDate(startTime);
    let oneDayDateArray = this.getDataWithDate(endTime);

    let newDateArray = timerArray.concat(currentDateArray).concat(oneDayDateArray);
    // console.log('--------oneDayExpiryData----------'+newDateArray);
    return newDateArray;
  }

  /* 7天有有效 */
  sevenDayExpiryData(startTime, sevenDayDate) {

    let timerArray = [0x01];
    /* 转数组 */
    let currentDateArray = this.getDataWithDate(startTime);
    let oneDayDateArray = this.getDataWithDate(sevenDayDate);
    let newDateArray = timerArray.concat(currentDateArray).concat(oneDayDateArray);
    // console.log('--------sevenDayExpiryData----------'+newDateArray);
    return newDateArray;
  }

  /* 时间段有效 */
  intervalTimeExpiryData(startTime, endTime) {

    let timerArray = [0x02];
    let startDateArray = this.getDataWithDate(startTime);
    let endDateArray = this.getDataWithDate(endTime);
    let newDateArray = timerArray.concat(startDateArray).concat(endDateArray);
    // console.log('--------intervalTimeExpiryData----------'+newDateArray);
    return newDateArray;
  }


  /* 循环周期有效（周一到周五） */
  orderlyTimeExpiryData(startTime, endTime, dayArray) {


    /* 定时有效 */
    let timerArray = [0x03];
    let weeksHex = this.getWeeksWithArray(dayArray);
    let startTimeArray = this.getDateWithHoursMinute(startTime);
    let endTimeArray = this.getDateWithHoursMinute(endTime);
    let temp = timerArray.concat(weeksHex).concat(startTimeArray).concat(endTimeArray);


    return temp;
  }

  foreverDayExpiryData() {
    /* 永久有效 */
    let timerArray = [0x04];
    return timerArray;
  }


  /* 选择周期 -- 获取一位 byte */
  getWeeksWithArray(weeksArray) {
    /*
        [{'index':0,selected:0},{'index':1,selected:0},]

        先写数量 7 ，后再补一位；
        */

    console.log("dengying","getWeeksWithArray weeksArray=",weeksArray);

    let instanceArray = [0, 0, 0, 0, 0, 0, 0];
    weeksArray.forEach((element, index) => {
      if (element.index >= 0) {
        instanceArray[element.index] = element.selected;
      } else {
        console.log(element);
        instanceArray[index] = element;
      }
    });
    instanceArray = instanceArray.concat([0]);
    instanceArray.reverse();
    let binaryString = instanceArray.join('');
    let binaryNumber = parseInt(binaryString, 2);
    let hexNumber = ASCII_To_Hex(binaryNumber);

    return hexNumber;
  }

  /* -----------------------------功能分割线------------------------------------------------ */

  /* 把输入的密码 转成 16进制数组 */
  dealPassword(password) {

    let array = [];
    let numberArray = password.split("");
    numberArray.forEach((element, index) => {

      // console.log('element----'+element);
      let HexItem = ASCII_To_Hex(Number(element));
      array.push(HexItem);
    });

    // console.log('dealPassword--array---'+array);
    return array;
  }

    /* 把输入的名称 转成 16进制数组 */
    dealName(password) {

        let array = [];
        let nameAry = password.split("$");
        nameAry.forEach((element, index) => {

            console.log('element----'+element);
            // let HexItem = ASCII_To_Hex(Number(element));
            if (element){
                let numberArray = element.split("%");

                numberArray.forEach((element, index) => {

                    console.log('element----'+element);
                    if (element){
                      if (index == 0){
                          // let HexItem = StrToHexCharCode(element);
                          for (let i = 0; i < element.length; i++) {
                              array.push(element.charCodeAt(i)).toString(16);
                          }
                          // array.push(HexItem);
                      } else array.push(element);
                    }
                });
            }
        });


        console.log('dealPassword--array---'+array);
        return array;
    }


  /* 通过日期获取-时-分 */
  getDateWithHoursMinute(sourceDate) {
    let currentDate = new Date();
    if (typeof (sourceDate) === 'number') {
      sourceDate = this.format(sourceDate, "yyyy/MM/dd HH:mm:ss");
    }
    if (isNaN(Date.parse(sourceDate))) {
      console.log('入参不为Date 日期类型');
      return;
    }
    let hour = sourceDate.getHours();
    let minute = sourceDate.getMinutes();
    hour = ASCII_To_Hex(Number(hour));
    minute = ASCII_To_Hex(Number(minute));
    if (hour.length < 2) {
      hour = `0${ hour }`;
    }
    if (minute.length < 2) {
      minute = `0${ minute }`;
    }

    let resultArray = [
      hour,
      minute
    ];
    console.log(`getDateWithHoursMinute------------------${ resultArray }`);
    return resultArray;
  }

  /* 获取日期对应的数组数据 */
  getDataWithDate(sourceDate) {

    let currentDate = new Date();
    if (typeof (sourceDate) === 'number') {
      sourceDate = this.format(sourceDate, "yyyy/MM/dd HH:mm:ss");
    }
    if (isNaN(Date.parse(sourceDate))) {
      console.log('入参不为Date 日期类型');
      return;
    }

    let SourceYear = String(sourceDate.getYear());

    let year = SourceYear.substring(SourceYear.length - 2);
    let month = sourceDate.getMonth() + 1;
    let date = sourceDate.getDate();
    let hour = sourceDate.getHours();
    let minute = sourceDate.getMinutes();

    year = ASCII_To_Hex(Number(year));
    month = ASCII_To_Hex(Number(month));
    date = ASCII_To_Hex(Number(date));
    hour = ASCII_To_Hex(Number(hour));
    minute = ASCII_To_Hex(Number(minute));

    if (month.length < 2) {
      month = `0${ month }`;
    }
    if (date.length < 2) {
      date = `0${ date }`;
    }
    if (hour.length < 2) {
      hour = `0${ hour }`;
    }
    if (minute.length < 2) {
      minute = `0${ minute }`;
    }

    let dataArray = [
      year,
      month,
      date,
      hour,
      minute
    ];
    // console.log('--------getDataWithDate----------'+dataArray);
    return dataArray;
  }


  /* 当前时间 N 天后 */
  getDateAfter(N) {
    if (typeof (N) != "number") {
      console.log('入参不为Number类型');
      return;
    }

    let date = new Date();
    let milliseconds = date.getTime() + 1000 * 60 * 60 * 24 * N;
    let newDate = new Date(milliseconds);
    return newDate;
  }

  /* ******打包数据 */
  packetArray(newArray) {

    let newResult = [];
    newArray.forEach((element, index) => {

      let Hex = ASCII_To_Hex(element);
      newResult.push(Hex);
    });

    let lastItem = CRC8_MaxIM(newResult).toString(16);
    newResult.push(lastItem);
    return newResult;
  }

  /**
     * 将输入的毫秒字符串or毫秒数转换成指定的字符串格式
     * @param {string} msStr 毫秒字符串 or 毫秒数
     * @param {string} format yyyy-MM-dd or yyyy-MM-dd hh:mm:ss
     * @return {string} 转换后的字符串
     */
  format(msStr, format) {
    const date = new Date(msStr / 1);
    let fmt = format;

    const obj = {
      'M+': date.getMonth() + 1, // 月份
      'd+': date.getDate(), // 日
      'H+': date.getHours(), // 小时
      'm+': date.getMinutes(), // 分
      's+': date.getSeconds(), // 秒
      'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
      S: date.getMilliseconds() // 毫秒
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (String(date.getFullYear())).substr(4 - RegExp.$1.length));
    }
    const keys = Object.keys(obj);
    for (let i = 0; i <= keys.length; i += 1) {
      const k = keys[i];
      if (new RegExp(`(${ k })`).test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (obj[k]) : ((`00${ obj[k] }`).substr((String(obj[k])).length)));
      }
    }
    return new Date(fmt);
  }
}


module.exports = PassWordManager;