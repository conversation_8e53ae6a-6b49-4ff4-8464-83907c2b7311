/**
 * TinyWindowLivePlayer.js 设置页面常用到的直播流播放组件
 *
 * @property {function} videoRef - 播放器videoView的ref
 * @property {number} playerMarginHorizontal - 播放器的水平外边距，
 * @property {function} onBpsChange - 数据量变化回调函数
 *
 * 示例:
 _renderVideoLayout() {
        let playerMarginHorizontal = 14;
        return (<View style={{ marginHorizontal: playerMarginHorizontal }}>
            <TinyWindowLivePlayer
                videoRef = { ref => this.cameraGLView = ref }
                playerMarginHorizontal={ playerMarginHorizontal }
            />
        </View>)
        然后在需要启动直播流的地方调用this.cameraGLView.prepare()即可
 }
 *
 * @author: fugui
 * @date: 2021/01/17
 */

import React, {Component} from 'react';
import {
    View, Text,  ActivityIndicator, Dimensions
} from 'react-native';
import { LetDevice} from "../../../../../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import I18n, {stringsTo} from "../../../../../../globalization/Localize";
const ScreenWidth = Dimensions.get('window').width;
import PropTypes from 'prop-types';
import {XText} from "react-native-easy-app";
import {colors, RoundedButtonView} from "../../../../../../imilab-design-ui";
import ImageButton from "../../../../../../imi-rn-commonView/ImageButton/ImageButton";
import {PLAYER_EVENT_CODE} from "../../../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import {showToast} from "../../../../../../imilab-design-ui/src/widgets/Loading";

const LIVE_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading',
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
Object.freeze(LIVE_PLAYER_STATUS);


export default class TinyWindowLivePlayer extends Component {

    constructor(props, context) {
        super(props, context);
        this.state = {
            isLoading: false,
            isPlaying: false,
            showErrorView: false,
            showPauseView: false,
            errorCode: null,
            isOnline: true
        };
    }

    static propTypes = {
        playerStyle:PropTypes.object, //播放器的样式，如果这里传入了width和height，将作为播放器的宽高，playerMarginHorizontal失效
        playerMarginHorizontal: PropTypes.number, //播放器距离屏幕边左右边缘的距离，作用仅仅是用来计算播放器的宽度和高度
        videoRef: PropTypes.func,
        onBpsChange: PropTypes.func,
        onLivePlayerStatusChange: PropTypes.func,
        onVideoClick: PropTypes.func,
    };

    static defaultProps = {
        playerStyle:null,
        playerMarginHorizontal: 14
    };

    componentDidMount() {

    }

    componentWillUnmount() {
        this.IMIVideoViewNew && this.IMIVideoViewNew.stop();
        // this.IMIVideoViewNew && this.IMIVideoViewNew.destroy();
    }


    _onEventChange = (event) => {
        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log("直播流----_onEventChange,回调网速值");
            this.setState({isLoading: false, isPlaying: true, showPauseView: false, showErrorView: false});
            this.props.onBpsChange && this.props.onBpsChange(event.extra.bps);

        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("直播流----_onEventChange,开始启用");
            this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log("直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false, isPlaying: true});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            console.log("直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.setState({isLoading: false, isPlaying: false, showPauseView: true}); //Android会调用两次，所以停止了也没有暂停按钮
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            console.log("直播流----_onEventChange,开始播放");
        }
    };


    _loadingView() {
        if (!this.state.isOnline) return null;
        if (!this.state.isLoading) return;
        return (<View
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ActivityIndicator
                style={{width: 45, height: 45}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    marginTop: 10,
                    fontSize: 13,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.state.isOnline) return null;
        if (!this.state.showErrorView) return;
        if (this.state.isLoading) return;
        return (
            <View
                style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    justifyContent: "center",
                    alignItems: "center"
                }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 13,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonTextStyle={{fontSize: 13}}
                                   buttonStyle={{
                                       margin: 10,
                                       paddingHorizontal: 12,
                                       height: 35
                                   }}
                                   onPress={() => this.pressErrorView()}/>
            </View>
        );
    }


    pressErrorView() {
        this.IMIVideoViewNew.start();
        this.setState({
            showErrorView: false,
            isLoading: true
        }, () => {
            LetDevice.updateAllPropertyCloud().then((data) => {
                console.log('updateAllPropertyCloud:', JSON.parse(data));
                let dataPackage = JSON.parse(data);
                let isOnline = true;

                if (dataPackage.ActiveStatus) {
                    isOnline = LetDevice.isOnline && dataPackage.ActiveStatus.value != 0;
                }
                if (isOnline) {
                    this.IMIVideoViewNew.start();
                } else {
                    this.setState({
                        isLoading: false,
                        isOnline: isOnline
                    });
                }
            }).catch(() => {
                this.IMIVideoViewNew.start();
            });
        });
    }

    _pauseView() {
        if (!this.state.isOnline) return null;
        if (!this.state.showPauseView) return null;
        if (this.state.showErrorView) return null; //IOS上会先ERROR后PAUSE，如果已经显示errorView，则不显示pauseView
        if (this.state.isLoading) return null;
        return (<View
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ImageButton
                style={{width: 45, height: 45}}
                source={require("./res/icon_play.png")}
                highlightedSource={require("./res/icon_play_p.png")}
                onPress={() => {
                    this.IMIVideoViewNew.prepare();
                    this.setState({
                        showPauseView: false,
                        isLoading: true
                    });
                }}
            />
        </View>);
    }


    _deviceOffLineView() {
        if (this.state.isOnline) return null;
        return (
            <View pointerEvents="box-none"
                  style={{
                      position: "absolute",
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center"
                  }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('device_offline')}
                />
            </View>
        );
    }

    //获取云端物模型数据
    dealWithError(event) {
        LetDevice.updateAllPropertyCloud().then((data) => {
            console.log('updateAllPropertyCloud:', JSON.parse(data));
            let dataPackage = JSON.parse(data);
            let stateProps = {isOnline:LetDevice.isOnline};


            if (dataPackage.ActiveStatus) {
                stateProps.isOnline = LetDevice.isOnline && dataPackage.ActiveStatus.value != 0;
                console.log("离线问题-----" + stateProps.isOnline, dataPackage.ActiveStatus.value, LetDevice.isOnline)
            }

            this.setState({
                isPlaying: false,
                showErrorView: stateProps.isOnline,
                showPauseView: false,
                errorCode: event.code,
                isOnline: stateProps.isOnline
            });
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    render() {
        //首先根据调用者传入的播放器水平外边距,通过视频画幅宽高9:16来确定播放器的宽度和高度
        let videoWidth = ScreenWidth - this.props.playerMarginHorizontal * 2;
        let videoHeight = videoWidth * 9 / 16;
        //如果传入了playerStyle，则以playerStyle
        if(this.props.playerStyle&&this.props.playerStyle.width){
            videoWidth = this.props.playerStyle.width;
        }
        if(this.props.playerStyle&&this.props.playerStyle.height){
            videoHeight = this.props.playerStyle.height;
        }
        return (
            <View style={this.props.playerStyle?this.props.playerStyle:{marginHorizontal: this.props.playerMarginHorizontal}}>

                <IMICameraVideoView
                    style={{height: videoHeight, width: videoWidth}}
                    ref={ref => {
                        this.IMIVideoViewNew = ref;
                        this.props.videoRef && this.props.videoRef(this.IMIVideoViewNew);
                    }}
                    onCommCallback={this.props.onCommCallback}
                    //mute={true}
                   // playerClass={IMICameraVideoView.PlayerClass.LIVE}
                    dataSource={{
                        //playerClass: IMICameraVideoView.PlayerClass.LIVE,
                        iotId: LetDevice.deviceID,
                    }}
                    onPrepared={() => {
                        // this.IMIVideoViewNew.start();
                    }}
                    onVideoViewClick={() => {
                        // this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                        this.props.onVideoClick && this.props.onVideoClick();
                    }}
                    onEventChange={this._onEventChange}

                    onErrorChange={(event) => {
                        // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                        // alert(JSON.stringify(event));

                        do {
                            //判断如果是通话报错则此处进行判断是否为占线
                            if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                continue
                            }
                            //判断是否为对讲模式
                            if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {
                                continue
                            }
                            //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                            if (event.extra.arg2 === "voice intercom existed") {
                                showToast(stringsTo('call_busy_tips'));
                            } else {
                                showToast(stringsTo('call_connect_error'));
                            }
                            return
                        } while (false);

                        this.dealWithError(event);
                    }}

                    /*  lensCorrect={{use: true, x: 468.0 / 1920.0, y: 48.0 / 1088.0}}*/

                />
                {/*20220826@byh 修改loading errorview等不居中问题*/}
                <View pointerEvents="box-none" style={{
                    position: "absolute",
                    width: videoWidth,
                    height: "100%",
                    flexDirection: "column"
                }}>
                    {
                        this._loadingView()
                    }
                    {
                        this._errorView()
                    }
                    {
                        this._pauseView()
                    }
                    {
                        this._deviceOffLineView()
                    }
                </View>

            </View>
        );
    }
}
