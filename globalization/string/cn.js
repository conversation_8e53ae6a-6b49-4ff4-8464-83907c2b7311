const localeConfig = {
  calendarLocaleConfig: {
    formatAccessibilityLabel: "dddd d 'of' MMMM 'of' yyyy",
    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    monthNamesShort: [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ],
    dayNames: ['日', '一', '二', '三', '四', '五', '六'],
    dayNamesShort: ['日', '一', '二', '三', '四', '五', '六'],
  },
};

export default {
    ...localeConfig,
    Sunday: "周日",
    Monday: "周一",
    Tuesday: "周二",
    Wednesday: "周三",
    Thursday: "周四",
    Friday: "周五",
    Saturday: "周六",
    SundayShort: "日",
    MondayShort: "一",
    TuesdayShort: "二",
    WednesdayShort: "三",
    ThursdayShort: "四",
    FridayShort: "五",
    SaturdayShort: "六",
    MonthAndDay: "M月D日",
    popo_setting_camera_text: "摄像机设置",
    alarmSettingText: "看家助手设置",
    playBackText: "回看",
    cameraTestTipsTitle2: "网速测试",
    help_callback: "帮助与反馈",
    quality_360: "360P",
    quality_25k_v2: "2.5K",
    sd_360p: "标清",
    hd_1080p: "超高清",
    quality_1080: "1080P",
    resolution_sd: "高清",
    resolution_qhd: "超清",
    quality_1440p: "超高清",
    quality_sd_new: "标清",
    quality_uhd: "超高清",
    quality_sd: "标清",
    quality_fhd: "高清",
    quality_common_sd: "480P",
    quality_fhd_3k: "3K高清",
    quality_common_3K: "3K",
    quality_2k: "2K",
    all_events_str: "全部事件",
    peopleDetectStr: "人形事件",
    bellEventStr: "门铃事件",
    detectDistanceModalSubStr033: "调节灵敏度时，PIR检测到人，门铃led灯开始橙色闪烁。",
    doorkeyAreaDetectAlarmSubStr: "重点区域中检测到有人时，门铃会发出声光报警，并持续10秒。",
    doorbellSetting: "门铃设置",
    record_files_sdcard: "内存卡",
    gatewaySettingTitle: "网关设置",
    wifiSettingText: "WIFI设置",
    gateWayLightEffectText: "网关指示灯灯效说明",
    quality_low: "流畅",
    network_not_connected: "网络连接异常，请检查连接后重试",
    device_offline: "设备离线",
    power_off: "设备已休眠",
    onlyDoInLive: "请打开实时画面后进行该操作",
    imi_speaking_block: "通话中，无法使用",
    screen_recording: "录制中，无法使用",
    saved_system_album: "已保存到系统相册",
    action_fail: "操作失败",
    storage_permission_denied: "没有存储权限, 请手动设置",
    save_system_album_failed: "录制时长过短",
    save_album_failed: "视频录制失败",
    commLoadingText: "加载中，请稍候...",
    error_code_common_retry: "点击重试",
    error_help: "查看帮助",
    camera_calling: "通话中，无法使用",
    call_busy_error: "通话被占用",
    call_busy_tips: "其他设备正在通话中，请稍后重试",
    call_connect_error: "网络异常，通话中断",
    net_connect_error: "网络异常",
    device_offline_aready: "设备已离线,",
    offlineTime: "离线时间:",
    connect_err: "连接失败",
    connect_error_help_one: "1.请分别检查手机和摄像机是否联网正常，若修改过WLAN名称或密码，请",
    offline_help_tip_reconect: "重新连接",
    dot_for_modal: "。",
    connect_error_help_two: "2.若修改过WIFI密码或更换网络，请",
    connect_error_help_three: "3.请尝试重启路由器。",
    connect_error_help_four: "4.如以上无法解决，请",
    offline_help_tip_feed_question: "反馈问题",
    know_button: "我知道了",
    offline_help_tip_one: "1.请检查设备是否断电，并请确保使用原装电源适配器。",
    offline_help_two: "2.如供电正常，请查看指示灯：",
    offline_help_two_first: "蓝灯慢闪/快闪：请检查摄像机联网是否正常，如修改过WLAN名称或密码，请",
    offline_help_two_two: "黄灯快闪：摄像机已被重置，请",
    offline_help_tip_bind: "重新绑定",
    offline_help_two_three: "黄灯慢闪：摄像机正在升级，请升级完成后再访问，切勿断电。",
    offline_three1: "3.请尝试将设备尽量靠近路由器，并减少墙等障碍物。二者距离越近，设备",
    offline_three2: "网络RSSI值",
    offline_three3: "越大，理论上网络信号越强。建议RSSI值大于-50。",
    offline_help_tip_the_first: "黄灯常亮：请",
    offline_help_tip_connect_service: "联系客服",
    offline_help_tip_the_thd: "获软件升级包。",
    offline_help_tip_the_sec: "蓝灯常亮/闪烁：请",
    offline_help_tip_forth: "4.请查看摄像机",
    offline_help_tip_rssi: "网络信息",
    offline_help_tip_forth_first: "RSSI值，若小于-50，请尽量靠近路由器",
    offline_help_four: "4.请尝试重启路由器。",
    offline_help_five: "5.如以上无法解决，请",
    offline_help_tip_the: "3.请观察摄像机指示灯",
    offline_help_tip_fth: "5.若以上操作均无法解决，请",
    offline_help_tip_fth_old: "4.若以上操作均无法解决，请",
    offline_help_tip_green_blink: "绿灯闪烁：请",
    alarm_turn_on_str: "请打开看家开关",
    fees_for_renewal: "续费",
    toolbar_text_resolution: "清晰度",
    toolbar_text_sound: "声音",
    toolbar_text_snap: "截图",
    toolbar_text_record: "录屏",
    toolbar_text_fullscreen: "全屏",
    select_all: "全选",
    unselect_all: "全不选",
    cancel: "取消",
    commWaitText: "请稍后...",
    ok_button: "确定",
    dialog_never_show: "不再提醒",
    cruise_seconds: "秒",
    am: "上午",
    pm: "下午",
    year: "年",
    month: "月",
    day: "日",
    hour: "时",
    minute: "分",
    give_away: "免费送",
    cloud_give_price_str: "价值",
    cloud_give_ok: "立即领取",
    picker_start_time: "开始时间",
    picker_end_time: "结束时间",
    alarm_direction_custom: "自定义时间",
    step_preview: "上一步",
    account_next: "下一步",
    stayDetectStr: "逗留检测",
    video_downloading: "下载中...  ",
    noAlarmVideo: "暂无可观看视频",
    cloud_time_out_str: "云存储已过期",
    buy_again: "立即续费",
    cloudExpireShowImageHint: "购买云存储，开启云端视频备份  >",
    network_available_need_check: "网络不可用，请检查网络设置",
    housekeeping_no_event: "暂无内容",
    commLoadingFailText: "加载失败，请重试",
    commLoadingMoreDataText: "加载更多数据，请稍候...",
    commNoMoreDataText: "已加载全部数据",
    commLoadingClickText: "点击刷新",
    abnormal_event_fire: "防火报警",
    abnormal_event_damaged: "您的门被撬了",
    abnormal_event_anti_password: "反劫持密码开门",
    abnormal_event_anti_fingerprint: "反劫持指纹开门",
    abnormal_event_password: "错误密码开门次数过多",
    abnormal_event_fingerprint: "指纹识别失败次数过多",
    abnormal_event_nfc: "NFC卡识别失败次数过多",
    abnormal_event_face: "错误人脸频繁开门",
    abnormal_event_multiple: "组合方式开门失败次数过多",
    abnormal_event_door_time_out: "门未关超时报警",
    abnormal_event: "异常事件",
    lingerChangeText: "有人在门前逗留",
    alarm_change_string: "看护区域有变动",
    doorbellPressTitle: "有人按门铃",
    downloadingDeleteError: "视频正在下载中，请稍后再试",
    delete_success: "删除成功",
    delete_failed: "删除失败",
    video_downloaded: "下载完成",
    save_failed: "保存失败",
    delete: "删除",
    chargeGuide: "充电提示",
    category_doorbell: "门铃",
    category_camera: "摄像机",
    chargeGuideSubtitle: "使用5V/2A的电源适配器进行充电，从0%到100%满电大约需要5个小时。",
    normalCharge: "充电方式一",
    hubCharging: "充电方式二",
    hubConnectHintTitle: "为了让信号更好，建议这么做:",
    hubConnectHintSubtitle: "1.尝试将网关放置在离地面更高的地方。\n2.当网关连接网线时，将它放置在距离路由器0.5m远处。\n3.将网关放置在距离墙壁0.5m远处。\n4.避免将网关放置在墙角。",
    button_finish: "完成",
    button_add_device: "添加更多设备",
    cameraTestTips2: "若安装位置网络环境较差,\n可能会导致连接失败或浏览视频卡顿",
    noSignalText: "没有信号",
    perfectText: "信号超好",
    strongText: "信号很好",
    goodText: "信号尚可",
    weakText: "信号较弱",
    cameraTestStrong: "当前位置很适合安装%{code}",
    cameraTestGood: "当前位置适合安装%{code}",
    cameraTestWeak: "当前位置Wi-Fi信号较弱，请把%{code}摆放在更靠近网关的地方",
    cameraTestNoSignal: "当前的位置没有Wi-Fi信号，请把%{code}摆放在更靠近网关的地方",
    no_sd_card_go_buy_cloud: "购买云存储",
    downloadTip: "请播放要下载的视频",
    alarm_download_downloading: "下载中...",
    storage_no_sdcard_please_buy: "暂无内存卡，请插入或购买",
    no_sd_card_tips_text: "云端视频砸不坏偷不走，存云端更安全",
    cameraTestWifiSpeedTitle: "安装须知",
    cameraTestTipsTitle1: "安装指导",
    cameraTestTips1: "%{code}尽量靠近网关放置，在安装前携带手机和%{code}进行网速测试。",
    re_play: "重播",
    doorbellIndicatorLightPageTitle: "门铃指示灯说明",
    doorbellPressLight: "门外按门铃",
    doorbellPressSubtitle: "点击门铃键，门铃键边缘指示灯出现跑马灯效果，并可听到门铃铃声",
    cameraOnSubtitle: "长按sync按键2秒，直到蓝色指示灯常亮（5秒后熄灭），并听到开机音乐，开机成功。",
    cameraOffSubtitle: "长按sync按键8秒，直到红色指示灯闪烁（一次），并听到关机音乐，关机成功。",
    doorbellUsbChargeTitle: "利用USB给门铃充电",
    usbChargeSubtitle: "红色闪烁三次，开始充电",
    doorbefullChargeTitle: "门铃充电完成（充满电）",
    fullChargeSubtitle: "绿色指示灯常亮",
    cameraResetSubTitle: "长按sync按键5秒，直到蓝色指示灯闪烁，放开按键，开始重置。重置过程中，摄像机会重新启动。",
    reset_success_text: "重置成功",
    resetSuccessSubtitle: "蓝色灯闪烁",
    pairedSuccessTitle: "配对成功",
    pairedSuccessSubtitle: "蓝色灯常亮5秒后熄灭",
    pairedFailTitle: "配对失败",
    pairedFailSubtitle: "蓝色和橙色交替闪烁3次",
    pirSensitivityAdjustSubtitle: "在进行PIR灵敏度调节时，每当PIR检测到有人，会橙色闪烁一次，如果持续检测到有人，则橙色持续闪烁。",
    pirGetPeopleTitle: "PIR检测到有人",
    pirGetPeopleSubtitle: "橙色闪烁一次",
    pirAlwaysGetPeopleTitle: "PIR持续检测到有人",
    pirAlwaysGetPeopleSubtitle: "橙色持续闪烁",
    gateWayLightPageTitle: "网关指示灯说明",
    light_blue: "蓝色指示灯",
    light_blue_flash: "蓝色闪烁",
    light_blue_flash_case: "网络连接中（未连接网络状态）",
    light_blue_on: "蓝色常亮",
    light_blue_on_case: "网络连接成功（已连接网络状态）",
    light_orange: "橙色指示灯",
    light_orange_flash: "橙色闪烁",
    light_orange_flash_case: "等待连接（设备与App绑定中）",
    light_orange_on: "橙色常亮",
    light_orange_on_case1: "正在开机中",
    light_orange_on_case3: "正在OTA升级中",
    light_blue_orange_mix_head: "蓝色/",
    light_blue_orange_mix_tail: "橙色指示灯",
    light_blue_orange_mix_flash: "蓝色和橙色交替闪烁3次",
    light_blue_orange_mix_flash_case: "OTA升级失败",
    gateway_reset: "网关重置",
    gateway_reset_hint1: "网关绑定过程中，指示灯一直未显示橙色闪烁，此时需要对网关进行重置。",
    gateway_reset_hint2: "请长按网关背面的reset按键，保持5秒左右，至指示灯橙色常亮，松开按键。重置过程中，网关会重新启动。当指示灯开始橙色闪烁时，表示重置成功，可以开始绑定。",
    indicatorLightPageTitle: "摄像机指示灯说明",
    cameraOnOffLight: "摄像机开关机",
    cameraOnTitle: "摄像机开机",
    cameraOffTitle: "摄像机关机",
    cameraChargeTitle: "摄像机充电",
    usbChargeTitle: "利用USB给摄像机充电",
    fullChargeTitle: "摄像机充电完成（充满电）",
    sunChargeTitle: "利用太阳能板给摄像机充电",
    cameraResetTitle: "摄像机重置",
    pirSensitivityAdjustTitle: "摄像机PIR检测灵敏度调节",
    injectSuccess: "弹出成功",
    sdcard_format_success: "格式化成功",
    sdcard_format_fail: "格式化失败",
    sdCardFormating: "SD卡正在格式化",
    injectFailed: "弹出失败",
    noSdCardTitle: "暂无SD卡",
    injectSdCardTitle: "安全弹出SD卡",
    injectSdCardSubtitle: "安全弹出SD卡，防止录制文件损坏",
    sdCardFormat: "格式化SD卡",
    sdCardDamaged: "存储卡异常",
    injectSdCardHint: "确定安全弹出SD卡？",
    formatTitle: "确认格式化SD卡？",
    formatMessage: "格式化后，SD卡内容将被清空",
    stayTimeStr: "逗留时长",
    houseDetectedPeopleStr: "家门前检测到有人时",
    peopleRecordPowerMoreStr: "有人出现立即录像(非常耗电)",
    peopleRecordPowerMoreSubStr: "适合门口环境比较简单，邻居出入较少，有人出现立即录像，第一时间查看异常情况。",
    peopleRecordPowerCommonStr: "长时间停留才录像(一般耗电)",
    peopleRecordPowerCommonSubStr: "利用AI算法智能过滤无效信息，有人长时间逗留才录像，避免打扰。",
    peopleRecordPowerLessStr: "关闭逗留检测功能(较少耗电)",
    peopleRecordPowerLessSubStr: "适合门口环境比较复杂，邻居出入较多，关闭逗留检测，降低功耗。",
    firstSetStayTimeFailed: "已使用默认设置，请至【看家助手设置】中调整",
    commTitleSettingText: "设置",
    feature_set: "功能设置",
    comm_setting_title: "通用设置",
    device_name: "设备名称",
    check_update: "检查更新",
    shareUser_tip: "共享设备无权限",
    shared_setting: "设备共享",
    confirm_deletion_device: "是否确认删除设备?",
    preset_sleep_set: "固定位置休眠设置",
    comm_setting_remove_device: "删除设备",
    update_device_name: "修改设备名称",
    input_name: "请输入名称",
    settings_set_success: "设置成功",
    play_back_text_all: "全部回看视频",
    wake_up: "立即唤醒",
    operationFailed: "设置失败",
    people_event: "人形检测",
    move_event: "移动检测",
    alarm_loud_switch: "异响检测",
    no_human_event: "无人检测",
    fence_detect_switch: "围栏检测",
    cry_event: "哭声检测",
    keyAreaDetectStr: "重点区域检测",
    moveEvent: "移动检测",
    peopleEvent: "人形检测",
    soundEvent: "听到异常声响",
    bottom_house_keeping: "看家",
    str_housekeeping_tip_guide_people: "o 看护区域检测到有人",
    str_housekeeping_tip_guide_sound: "o 检测到异响",
    str_housekeeping_tip_guide_fence: "o 看护区域出入围栏",
    str_housekeeping_tip_guide_nobody: "o 看护区域检测到无人",
    str_housekeeping_tip_guide_move: "o 看护区域检测到画面变化",
    str_housekeeping_tip_guide_cry: "o 检测到有哭声",
    str_housekeeping_tip_guide_important: "o 重点区域检测到有人",
    str_housekeeping_tip_title: "打开看家开关后，出现以下场景摄像机会立即保存报警图片并向您发送通知",
    go_to_open: "去打开",
    delete_alert: "确认删除?",
    delete_title_loading: "正在删除…",
    cloudTip: "开通云存享无限存储，提供更多事件识别",
    x_flat_list_no_more_data: "已加载全部数据",
    downLoadTitle: "下载",
    select_tip: "请选择需要操作的文件",
    delete_title: "删除",
    save_success: "保存成功",
    buy_cloud_for_info: "购买云存储，开启云端备份",
    download_system_album: "下载成功",
    video_download_fail: "下载失败",
    delete_alert_tip: "此视频包含",
    delete_alert_tip1: "个事件，是否继续删除",
    bottom_house_video_keeping: "视频详情",
    currentPlay: "当前播放",
    loadMsgInfo: "加载消息",
    loadMsgInfoError: "未匹配到消息详情",
    bottom_video_album: "相册",
    select_title_1: "请选择",
    album_video_play: "播放",
    album_video_pause: "暂停",
    album_video_mute: "静音",
    album_video_voice: "声音",
    album_video_full_screen: "全屏",
    video_download_fail_warning: "视频上传中，请稍后重试",
    bottom_cloud_storage: "云存储",
    change_event_str: "切换事件",
    open_setting: "立即设置",
    no_video_data_new: "暂无视频",
    playback_no_video_data_tip: "吖，今天没有视频，换一天试试吧",
    date_format_yyyy_mm_dd: "yyyy年MM月dd日",
    panoramicSuccess: "绘制成功",
    panoramicError: "全景图绘制失败，请重试",
    direction_end_009: "我转不动了",
    isDataUsageTip: "当前是移动网络，自动暂停中",
    Panoramic_loading: "正在绘制全景图，请稍等…",
    set_onekey_tit: "设置一键警告功能?",
    set_onekey_msg: "设置后,一键触发灯光和提示音,实现快速警告。",
    set_onekey_sure: "去设置",
    popo_setting_storage_text: "存储设置",
    sdcard_error_out: "内存卡已弹出,请重新插拔内存卡!",
    audio_permission_denied: "没有录音权限",
    panoramicing_tip: "全景图绘制中……",
    camera_guide_for_zoomed_str: "双击/捏合\n就能放大/缩小画面啦",
    camera_guide_for_panoramic_str: "点击此处进入全景模式",
    expiredHint: "   您的云存储套餐今天到期，过期后将无法查看或录制看护视频",
    sdcard_tip_cancel: "知道了",
    sd_need_format_no_index_massage: "内存卡格式化后才能正常录像，是否立即格式化并清空内存卡里所有内容?",
    sd_need_format_has_old_index_massage: "设备软件已升级，格式化内存卡后可获得更好回看体验，是否立即格式化并清空内存卡里所有内容?",
    is_the_calibration: "正在校准…",
    calibration_completed: "摄像机校准成功",
    netWorkError: "网络连接中断",
    operate_time_out: "操作超时",
    hotspot_connect_hint: "请将手机Wi-Fi连接到“imi_xxxxxx” 后返回此APP",
    goto_wifi: "去设置Wi-Fi",
    play_back_text: "回看",
    sdcard_status7: "内存卡未初始化",
    toolbar_text_sleep: "休眠",
    quality_auto: "自动",
    sdcard_format_title_tips: "请完成内存卡格式化",
    sdcard_format_title_tips_content: "内存卡未初始化，正常使用前需进行格式化，格式化会清除内存卡中所有数据，仍进行格式化？",
    upgrade_state_content: "检测到设备有最新固件版本",
    upgrade_state_content_end: "是否升级",
    sleepTipTitle: "设备休眠",
    sleepTipContent: "摄像机将停止工作和记录视频信息",
    cloud_will_time_out_str: "您的云存储即将到期",
    cloud_time_out_effect_str: "到期后将无法使用云存储服务",
    temporarily_not: "暂不",
    formatting_btn: "格式化",
    storageCardFormating: "内存卡正在格式化",
    waitFailedTip: "请稍后再试",
    targetPushTitle_subtitle: "点我解决",
    targetPush_sdcard_format: "内存卡需格式化后使用！",
    sdcard_status_error: "存储卡异常",
    get_success: "领取成功",
    Panoramic_tip: "绘制一张全景图，想看哪里点哪里",
    Panoramic_title: "绘制全景图",
    Panoramic_title_reset: "是否要重新绘制全景图？",
    storage_services: "云存储服务",
    storage_services_content: "什么是云存储?\n",
    storage_services_title: "云存储套餐权益",
    imi_cloud_storage_rights_title1: "全天候看护",
    imi_cloud_storage_rights_detail1: "视频一键下载",
    imi_cloud_storage_rights_title2: "智能检测异常",
    imi_cloud_storage_rights_detail2: "不放过细节",
    imi_cloud_storage_rights_title3: "云端存储",
    imi_cloud_storage_rights_detail3: "随时回看防丢失",
    imi_cloud_storage_rights_title4: "信息加密上传",
    imi_cloud_storage_rights_detail4: "隐私多重保障",
    storage_after: "以后再说",
    imi_cloud_experience_now: "立即体验",
    play_back_tit: "回看视频",
    play_back_change_time: "切换时间",
    alarmText: "看家助手",
    goto_live_view: "实时视频",
    str_housekeeping_tip_value: "o 看护区域检测到有人\no 看护区域检测到画面变化\no 检测到异响",
    common_error: "加载失败，请检查设备状态",
    sdCardName: "SD卡",
    no_video_data_failed: "获取视频文件失败",
    retry_connect: "重新连接",
    collapse: "收起",
    noData: "暂无数据，请重新选择",
    playback_no_event: "普通录像",
    keyArea: "重点区域",
    change_date: "切换日期",
    todayTitle: "今天",
    preDayTitle: "昨天",
    sdcard_status0: "良好",
    sdcard_status1: "无内存卡",
    sdcard_status6: "内存卡已满",
    sdcard_status3: "异常",
    sdcard_status4: "正在格式化",
    sdcard_status5: "内存卡弹出",
    sdcard_status9: "修复中",
    sdcard_status10: "弹出中",
    sdcard_status2: "空间不足",
    sdcard_status_normal_new1: "存储卡状态",
    play_back_text_all_title: "全部视频",
    max_download_limit: "仅支持单个下载",
    delete_failed_limit: "一次最多操作50个",
    delete_connect_failed: "设备连接已断开",
    delete_failed_inPlay: "播放中的视频不支持删除",
    sensitivity_for_high_tit: "高灵敏度",
    sensitivity_for_low_tit: "低灵敏度",
    alarm_sensitivity: "报警灵敏度",
    sensitivity_for_high_subtit: "检测有人或者物体移动，任何风吹草动不会错过",
    sensitivity_for_low_subtit: "家中发生较大动静时报警",
    date_picker_time_title: "请选择开始结束时间",
    alarm_time_set_time: "选择看护时间",
    time_equal: "开始时间不能等于结束时间",
    alarm_time_night: "晚上照看",
    alarm_time_all: "全天照看",
    alarm_time_day: "白天照看",
    alarm_time_set: "报警时间段",
    full_color_vision_title: "全彩夜视",
    alarm_time_24: "24小时",
    alarm_time_info: "陌生人闯入或者物体移动时提醒",
    alarm_time_208: "20:00-次日08:00",
    fullColor_smart_tit: "智能夜视",
    alarm_time_820: "8:00-20:00",
    noDIYTimeTip: "未选择自定义时间",
    voice_for_wu: "无",
    voice_for_warning: "警报声",
    voice_for_dingdong: "叮咚声",
    voice_for_welcome: "您好，欢迎光临",
    voice_for_area: "您已进入监控区域",
    voice_for_closedoor: "请随手关门，谢谢",
    voice_for_safe: "请注意安全",
    voice_for_stairs: "上下楼梯，请注意安全",
    voice_for_dangerArea: "危险区域，请离开",
    allDay_time: "全天（24小时）",
    potlight_flash: "闪烁",
    potlight_not_bright: "不亮",
    potlight_long_bright: "常亮",
    day_time: "白天（8:00-20:00）",
    night_time: "晚上（20:00-次日8:00）",
    voice_for_custom: "自定义",
    soundLightAlarm: "声光报警",
    only_people_detected: "只检测人形",
    effective_time: "生效时间",
    tip_voice_selected: "提示音选择",
    potlight_alarm_mode: "聚光灯报警模式",
    potlight_contain_time: "持续时间",
    click_warning_tit: "一键警告",
    local_device_connect_wifi: "切换设备WiFi",
    comm_setting_faq: "常见问题",
    angelMoreSetting: "更多设置",
    voice_for_enter_name: "请输入名称",
    max_device_name: "名称不能超过15个字符",
    customer_phone: "联系电话：************（工作时间 9:00-20:00）",
    customer_email: "邮箱：<EMAIL>",
    customer_wx: "微信公众号：创米数联",
    customer_web: "官网：www.imilabglobal.com",
    voice_for_add: "添加提示音",
    voice_for_edit: "编辑提示音",
    imi_save: "保存",
    voice_for_name: "名称",
    imi_input_text_tip: "不支持该字符",
    voice_for_tip_tit_time: "提示音（最长10秒）",
    voice_for_re_record: "重新录制",
    voice_for_click_record: "点击录制",
    nursingTimeSetting: "看护时间设置",
    show_upgrade_app: "请将app升级到最新版本后使用该功能",
    imi_fence_tit: "检测到物体/人形在箭头方向上穿过围栏时，视为围栏检测事件。拖动圆点，调整电子围栏监控区域",
    imi_switch_dection: "切换方向",
    alarm_event_tit: "看家事件",
    move_track: "移动追踪",
    settings_alarm_human_track_title: "人形追踪",
    no_human: "无人出现检测",
    alarm_event_manager: "看家助手管理",
    alarmTimeSetting: "报警时间间隔",
    time_minutes: "分钟",
    settings_alarm_push_time_sub: "报警推送较多时，可拉大间隔，减少干扰。",
    message_push_manager: "报警消息推送管理",
    message_push_phone: "检测到画面变化时推送消息给手机",
    tip_time_minute: "分钟",
    recordType: "记录模式",
    recordType1: "图片",
    recordType2: "视频",
    recordTypeSub: "开通云存储可切换至视频模式",
    alarm_info_set: "警告类型设置",
    alarm_sound_detection: "响声检测",
    detection_sensitivity: "检测灵敏度",
    alarm_info_push: "警告消息推送",
    message_push_switch: "报警消息推送开关",
    preset_opened: "已开启",
    preset_closed: "未开启",
    audio_broadcast: "智能语音播报",
    black_white_vision_title: "黑白夜视",
    settings_light_title: "状态灯",
    Data_usage_warning: "流量保护",
    data_usage_warning_intro: "在移动网络下不会自动播放",
    intelligent_cruise: "智能巡航",
    settings_switch_off: "未开启",
    cruise_all_view: "全景巡航",
    cruise_favorite: "收藏点巡航",
    sleep_set: "休眠设置",
    setttings_infared: "夜视功能设置",
    settings_flip_title: "图像翻转",
    settings_flip_subtitle: "摄像机倒装时，请开启此选项",
    imageSetting: "画面设置",
    setting_picture_setting: "更多画面设置",
    imi_camera_correct_pos: "云台校准",
    setting_reset: "重启设备",
    calibration_to_continue_30: "摄像机校准大概需要30秒,是否继续?",
    calibration_to_continue: "云台校准将修复摄像机在长期转动中造成的云台角度少量偏移的问题。确认后，摄像机将转动一周进行云台校准并自动回到默认初始位置。",
    calibration_failure: "摄像机校准失败",
    setting_reset_msg: "设备的重启的过程需要一些时间，确定重启设备？",
    settings_watermark_title: "时间水印",
    pictureCorrection: "镜头畸变纠正",
    pictureCorrectionSubtitle: "打开后，画面更平整，但会使可视范围缩小",
    wdrMode: "宽动态范围模式(WDR)",
    audio_broadcast_switch: "智能语音播报开关",
    rn_version: "插件版本",
    network_info: "网络信息",
    wifi_signal_0: "设备网络无信号",
    wifi_signal_2: "设备网络情况较差",
    wifi_signal_5: "设备网络情况良好",
    wifi_name: "WiFi名称",
    wifi_strength: "WiFi强度",
    wifi_rssi: "RSSI",
    wifi_loss: "丢包率",
    wifi_mode: "当前模式",
    wifi_mode_type2: "局域网连接",
    wifi_mode_type1: "广域网连接",
    wifi_ip_address: "IP地址",
    wifi_mac_address: "MAC地址",
    settings_light_full_color_title: "微光全彩",
    settings_light_full_color_subtitle: "当环境光线较差时，仍然可以看见彩色图像",
    fullColor_title3: "自动切换",
    fullColor_subTit: "当环境亮度较差时，自动开启夜视功能",
    fullColor_title2: "一直打开",
    fullColor_black_subTit: "始终呈现黑白画面，微光全彩功能不起作用",
    fullColor_title1: "一直关闭",
    fullColor_smart_subTit: "当环境亮度较差时，可能无法看清画面",
    // 双摄设备专用文案
    dual_camera_full_color_title: "全彩夜视",
    dual_camera_full_color_subtitle: "夜间开白光灯，可做夜灯使用，24小时彩色成像",
    dual_camera_black_white_title: "黑白夜视",
    dual_camera_black_white_subtitle: "夜间红外补光，隐蔽性高，画面黑白",
    dual_camera_smart_title: "智能夜视",
    dual_camera_smart_subtitle: "夜间黑白夜视，检测到人形时切换为全彩夜视",
    empty_start_end_tips: "请选择开始时间和结束时间",
    add_time_period: "新增时间段",
    not_set: "未设置",
    plug_timer_repeat: "重复",
    do_once: "执行一次",
    do_everyday: "全天",
    do_weekday: "周一至周五",
    do_weekend: "周末",
    do_custom: "自定义",
    nobody_detect_name: "检测时间段名称",
    date_picker_time_hint: "开始时间不能等于或晚于结束时间",
    nobody_time_no_more_than_10: "无人检测时间不能超过10个!",
    nobody_push_warning: "* 请务必打开imilab Home的推送功能，该时段内无人出现，推送消息到手机。",
    delete_time_warning: "是否删除该时间段？",
    save_the_open: "保存并开启",
    presetAngleSleep: "固定角度休眠",
    Preset_Sleep_subTitle: "开启后，摄像机将转至下方视角休眠",
    preset_sleep_set_holder: "当前未设置休眠角度，\n转动云台开始设置。",
    save_current_angle_sleep: "是否保存并开启当前角度休眠？",
    action_success: "操作成功",
    Preset_Sleep: "固定位置休眠",
    closeStr: "关闭",
    settings_switch_on: "开启",
    voice_for_max: "最多支持5条自定义音频",
    alarm_only_people_detected: "人形检测推送",
    settings_alarm_push: "移动检测推送",
    alarm_loud_push: "异响检测推送",
    fence_detect_push: "围栏检测推送",
    alarm_crying_push: "哭声检测推送",
    setting_record_model_always: "始终录制",
    setting_record_model_close: "关闭录制",
    storageCardHinting: "内存卡正在推出",
    sd_suspended: "存储暂停",
    sdcard_status_normal_new: "存储状态正常",
    sdcard_status_more_new: "剩余空间",
    sd_storage_switch: "存储开关",
    setting_record_model: "录制模式",
    record_quality: "录像清晰度",
    storageCardFormat: "格式化内存卡",
    sd_card_use_hint_062: "内存卡使用提示\n1.支持Class 4以上速度内存卡\n2.内存卡格式为FAT32\n3.请务必使用正品内存卡，对于山寨、劣质和翻新卡不保证兼容性\n4.内存卡容量最大支持256G",
    sdcard_tip1_new: "1.在拔出内存卡时，请先弹出内存卡。",
    sdcard_tip2_new: "2.为了便于您浏览本地存储视频，内存卡视频文件未加密，请妥善保管内存卡。",
    sdcard_out_already: "内存卡已经推出",
    sdcard_status_abnormal: "存储状态异常",
    sdcard_status8: "空间不足，请换卡",
    setting_record_model_always_title: "将录制的内容全部保存到内存卡上",
    setting_record_model_close_title: "关闭录制后摄像机将不会录制视频到内存卡",
    storageCardHint: "确定安全弹出内存卡？",
    setting_record_model_move: "仅录制移动画面",
    sdcard_status_more: "剩余空间",
    sdcard_exit: "推出内存卡",
    sdcard_tip1: "1.在拔出内存卡时，请先推出内存卡。",
    sdcard_tip2: "2.为了便于您浏览本地存储视频，内存卡视频文件未加密，请妥善保管内存卡.",
    setting_record_model_move_title: "检测到画面变化时录像,节省存储空间",
    more_store_setting: "存储管理",
    settings_sdcard_title: "内存卡状态",
    sleep_title: "休眠",
    Preset_Sleep_subTitle2: "开启后，设备每次均执行固定角度休眠",
    timer_sleep_title: "定时休眠",
    upload_grade_success: "升级成功",
    list_item_curr_version: "当前版本",
    list_item_latest_version_now: "当前已是最新版本",
    getError: "获取失败，请稍后",
    upgrade_button: "立即升级",
    list_item_version_status_4: "安装中",
    list_item_latest_version_uploading_title: "更新中...",
    list_item_latest_version_uploading: "更新中，请勿断电，等待更新完成后再使用",
    list_item_latest_version_upload_finish: "更新完毕后，设备端即将重启",
    upload_grade_timeout: "升级超时",
    upload_grade_error: "升级失败",
    list_item_latest_version: "最新版本",
    light_blue_orange_mix_flash_success: "OTA升级成功",
    list_item_latest_version_log: "更新日志",
    comm_config_wifi_title: "选择设备工作WiFi",
    wifiSettingTipsText: "此设备只支持使用2.4GHz Wi-Fi连接使用 \n  Wi-Fi名称仅支持英文字符及数字",
    comm_config_wifi_ssid_hint1: "你目前没有设置Wi-Fi",
    comm_config_wifi_password_hint: "请输入Wi-Fi密码",
    local_device_bind_wifi_failed: "设备绑定失败，请重启设备后重试",
    local_device_bind_wifi_success: "设备绑定成功，即将自动退出插件",
    local_device_bind_wifi_binding: "设备绑定中...",
    wdrFunctionHint: "宽动态范围模式，能让画面昏暗的部分和曝光过度的部分保留更多的细节",
    wdr_before: "正常模式",
    wdr_after: "宽动态范围模式",
    wdrHint: "注意：\n1.在图像黑白的夜视模式下，WDR不起作用。\n2.WDR会影响水印的着色。",
    sdCardRemain: "已用%{code}GB",
    sdCardTotal: "共%{code}GB",
    sdCardLeft: "预计循环录制%{code}天",
    onPlayErrorText: "播放发生异常(%{code})",
    onPlayErrorMaxText: "设备超过最大连接数量(%{code})",
    expiredCountdownHint: "   您的云存储套餐还有%{code}天到期，过期后将无法查看或录制看护视频",
    on_live_play_error: "摄像机打开失败(%{code}),请稍后重试!",
    select_title_3: "已选择%{code}项",
    click_too_fast: "点击太频繁",
    camera_volume:'摄像机外放音量调节',
    humanoid_detection:'人形检测',
    virtual_Fence:'虚拟围栏',
    fence_area:'绘制虚拟围栏',
    detection_area:'检测区域',
    detection_area_edit:'绘制检测区域',
    privacy_area_protection:'区域隐私保护',
    area_privacy_edit:'绘制区域隐私',
    algorithm_desc:'算法说明',
    ai_key_area_desc:'开启隐私区域保护后，选定区域内的画面将被隐藏，保护用户隐私。',
    ai_key_area_attention:'注意：开启隐私区域保护后，其他AI检测功能将无法在隐私区域内正常工作。',
    privacy_area_preview:'隐私区域预览',
    private_open_msg:'开启隐私区域保护后，人形检测、移动检测、虚拟围栏等功能可能会受到影响。',
    tips:'提示',
    drag_to_adjust_area:'拖拽调整隐私区域范围',
    save_privacy_area:'保存隐私区域',
    confirm_save_privacy_area:'确认保存当前的隐私区域设置吗？',
    no_changes_to_save:'没有修改需要保存',
    family_protection: "家人守护",
    family_protection_desc: "开启开关后，设定时段内无人移动，将在时间段末推送消息至手机",
    detect_nobody_in_morning: "早上无人出现",
    nobody_have_lunch: "中午无人在家",
    detect_nobody_in_day: "白天无人在家",
    plug_timer_onetime: "执行一次",
    plug_timer_everyday: "每天",
    setting_monitor_next_day: "次日",
    delete_files: "删除",
    family_delete_timer: "删除定时",
    family_delete_timer_confirm: "要删除选中的定时吗？",
    family_time_setting: "时间段设置",
    family_start_time: "开始时间",
    family_end_time: "结束时间",
    c_set_success: "设置成功",
    c_set_fail: "设置失败",
    c_get_fail: "获取失败",
    idm_empty_tv_device_tips: "没有选中任何项目",
    edit: "编辑",
    cruise_control:'自动巡航',
    enter_Name:'输入名称',
    cruise_time_period:'巡航时间段',
    common_angles:'常用角度',
    all_day:'全天',
    custom_time_period:'自定义时间段',
    custom_repeat:'自定义重复',
    plug_timer_sef_define:'自定义',
    picker_start_time_1:'选择开始时间',
    picker_end_time_1:'选择结束时间',
    workday:'工作日',
    weekend:'周末',   
    every_10_minutes:'每隔10分钟',
    every_30_minutes:'每隔30分钟',
    every_60_minutes:'每隔1小时',
    every_120_minutes:'每隔2小时',
    every_720_minutes:'每隔24小时',
    every_360_degrees:'360°巡航',
    every_360_degrees_subtitle:'360°全方位巡航',
    fixed_point_cruise:'定点巡航',
    fixed_point_cruise_subtitle:'常看位置轮流巡航',
    every_minutes :'每隔{{minute}}分钟巡航',
    every_hours_every_minutes :'每隔{{hour}}小时{{minute}}分钟巡航',
    cruise_time:'巡航时间',
    cruise_frequency:'巡航频率',
    cruise_mode:'巡航模式',
    custom_cruise_frequency:'自定义巡航频率',
    add_favorite:'添加常看位置',
    empty_not_support: '不支持为空',
    name_already_exists: "该名称已存在",
    that_position_exists:'重复添加',
    go_add:'去添加',
    common_angles_less_than_2: '常用角度小于2个，无法启动定点巡航',
    setting_in_progress: '设置中请稍后',
    rename: '重命名',
    add_common_angle: '添加常用角度',
    cruise_min_interval: '巡航间隔最小时间为5分钟',
    limit_reached_v6: '已达6个上限，请删除后再添加',
    add_to_favorites: '添加为常用角度',
    add_ing:'添加中...',
    del_ing:'删除中...',
    add_error:'添加失败',
    cruise_ing:'正在巡航',
    disturb_mode_desc:'当前处于勿扰模式，看家功能已禁用',
    voice_custom:"自定义",
    voice_add:"添加提示音",
    voice_edit:"编辑提示音",
    voice_name:"名称",
    voice_enter_name:"请输入名称",
    voice_tip_tit_time:"提示音（最长10秒）",
    voice_click_record:"点击录制",
    voice_re_record:"重新录制",
    alarm_time: "报警时间",
    alarm_voice: "报警提示音",
    alarm_light: "报警灯光",
    alarm_event_type: "报警事件类型",
    effect_event_type: "触发事件类型",
    picture_change: '画面变动',
    people_move: '有人移动',
    detect_car: '检测到车辆',
    detect_non_vehicle: '检测到非机动车',
    someone_break_into_key_area: '有人闯入重点区域',
    effective_time_set: '有效时间设置',
    // 双摄像头相关
    dual_camera_ptz_area: '球机区域',
    dual_camera_gun_area: '枪机区域',
    dual_camera_ptz_area_edit: '球机区域编辑',
    dual_camera_gun_area_edit: '枪机区域编辑',
    default_ptz_area_alarm: '默认对球机区域报警',
    default_gun_area_alarm: '默认对枪机区域报警',
    go_draw: '去绘制',
    detection_area_display_control: '检测区域显示控制',
    detection_area_display_subtitle: '在直播画面中框出检测区域',
    ai_function_conflict_message: '检测区域功能与其他AI功能冲突，请先关闭其他功能',
    person_enter_fence: '有人进入围栏',
    person_enter_fence_desc: '当有人进入围栏时，拍摄视频并提醒',
    person_leave_fence: '有人离开围栏',
    person_leave_fence_desc: '当有人离开围栏时，拍摄视频并提醒',
    fence_direction_required: '至少选择一个检测的方向',
    show_virtual_fence: '显示虚拟围栏',
    picture_select: '画面选择',
    dual_camera_view: '双摄画面',
    fixed_camera_view: '固定画面',
    ptz_camera_view: '云台画面',
    // 车辆检测
    vehicle_title: '车辆检测',
    vehicle_detection: '检测到车辆',
    vehicle_people_near: '有人靠近车辆',
    vehicle_frame: '车辆画框',
    vehicle_frame_hint: '将检测到的车辆在直播中标记出来',
    non_vehicle_title: '非机动车检测',
    non_vehicle_detection: '检测到非机动车',
    non_vehicle_people_near: '有人靠近非机动车',
    non_vehicle_frame: '非机动车画框',
    non_vehicle_frame_hint: '将检测到的非机动车在直播中标记出来',
    // 回看画面选择
    playback_view_select_title: '画面选择',
    playback_view_select_dual: '双摄画面',
    playback_view_select_qiang: '固定画面',
    playback_view_select_qiu: '云台画面',
    detection_area_draw:'检测区域绘制',
    detection_area_draw_subtitle:'在直播画面中框出检测区域，对所有检测类型生效',
    fence_area_draw:'围栏区域绘制',
    privacy_area_draw:'隐私区域绘制',
    detection_area_draw_page:'检测区域绘制',
    exit_save_confirm:'退出后，当前修改将不会保存，确定要退出吗？',
    exit:'退出',
    name_empty_error:'名称不能为空',
    name_exists_error:'名称已存在',
    set_monitor_time:'请设置监控时间',
    set_start_time:'请设置开始时间',
    set_end_time:'请设置结束时间',
    start_end_time_same:'开始时间和结束时间不能相同',
    monitor_period_exists:'监控时段已存在',
    name_title:'名称',
    start_time:'开始时间',
    end_time:'结束时间',
    discard_changes_title:'要舍弃对该设置的改动吗？',
    discard_changes:'舍弃更改',
    start_play_alarm_tip_title: '确认播放声光警铃',
    start_play_alarm_tip_content: '声光警铃会持续30s，播放中时可再次点击取消播放。',
    start_play: '开始播放',
    never_tip: '不再提示',
    open_white_light_tip_title: '确认打开补光灯',
    open_white_light_tip_content: '补光灯会持续60s，打开过程中可再次点击关闭',
    all: '全部',
    sd_card_play_back: 'SD卡回看',
};
