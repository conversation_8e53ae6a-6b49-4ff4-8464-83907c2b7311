const localeConfig = {
    calendarLocaleConfig: {
        formatAccessibilityLabel: 'dddd d \'of\' MMMM \'of\' yyyy',
        monthNames: [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Ма<PERSON>",
            "Июнь",
            "Июль",
            "Август",
            "Сентя<PERSON>рь",
            "Октябрь",
            "Но<PERSON><PERSON>рь",
            "Декабрь",
        ],
        monthNamesShort: [
            "Январь",
            "Февраль",
            "Март",
            "Апрель",
            "Май",
            "Июнь",
            "Июль",
            "Август",
            "Сентябрь",
            "Октябрь",
            "Ноябрь",
            "Декабрь",
        ],
        dayNames: ['Понедельник', 'Вторник', 'Środa', 'Четверг', 'Пятница', 'Суббота', 'Воскресенье'],
        dayNamesShort: ['Вс.','Пн.', 'Вт.', 'Ср.', 'Чт.', 'Пт.', 'Сб.']
    }
}

export default {
    ...localeConfig,
    Sunday: "Воскресенье",
    Monday: "Понедельник",
    Tuesday: "Вторник",
    Wednesday: "Środa",
    Thursday: "Четверг",
    Friday: "Пятница",
    Saturday: "Суббота",
    SundayShort: "Вс.",
    MondayShort: "Пн.",
    TuesdayShort: "Вт.",
    WednesdayShort: "Ср.",
    ThursdayShort: "Чт.",
    FridayShort: "Пт.",
    SaturdayShort: "Сб.",
    MonthAndDay: "M/D",
    popo_setting_camera_text: "Настройки камеры",
    alarmSettingText: "Настройки наблюдения за жилищем",
    playBackText: "Воспроизведение",
    cameraTestTipsTitle2: "Проверка скорости сети",
    help_callback: "Помощь и обратная связь",
    quality_360: "360P",
    quality_25k_v2: "2.5K",
    sd_360p: "360p",
    hd_1080p: "2k",
    quality_1080: "1080P",
    resolution_sd: "SD",
    resolution_qhd: "QHD",
    quality_1440p: "1440P",
    quality_sd_new: "360P",
    quality_uhd: "2K",
    quality_sd: "SD",
    quality_fhd: "HD",
    quality_common_sd: "480P",
    quality_fhd_3k: "3K HD",
    quality_common_3K: "3K",
    quality_2k: "2K",
    all_events_str: "Все события",
    peopleDetectStr: "Обнаружение силуэта человека",
    bellEventStr: "Событие дверного звонка",
    detectDistanceModalSubStr033: "При настройке чувствительности светодиод мигает оранжевым цветом, если ПИК-датчик обнаруживает движение.",
    doorkeyAreaDetectAlarmSubStr: "При обнаружении человека на ключевом участке дверной звонок подает акустооптический сигнал в течение 10 секунд.",
    doorbellSetting: "Настройки дверного звонка",
    record_files_sdcard: "Мicro SD-карта",
    gatewaySettingTitle: "Настройки концентратора",
    wifiSettingText: "Настройки Wi-Fi",
    gateWayLightEffectText: "Описание светодиодных индикаторов концентратора",
    quality_low: "Сглаживание",
    network_not_connected: "Падключэнне да сеткі ненармальна, праверце падключэнне і паўтарыце спробу",
    device_offline: "Устройство в автономном режиме",
    power_off: "Устройство уже находится в спящем режиме",
    onlyDoInLive: "Перед началом работы откройте экран трансляции",
    imi_speaking_block: "Использование во время звонка невозможно",
    screen_recording: "Во время записи данная функция неактивна",
    saved_system_album: "Сохранено в альбома телефона",
    action_fail: "Сбой операции",
    storage_permission_denied: "Нет разрешения на хранение",
    save_system_album_failed: "Длительность записи слишком мала",
    save_album_failed: "Сбой записи видео",
    commLoadingText: "Идет загрузка. Подождите...",
    error_code_common_retry: "Нажмите, чтобы повторить попытку",
    error_help: "Просмотреть справку",
    camera_calling: "Невозможно использовать во время вызова",
    call_busy_error: "Вызов занят",
    call_busy_tips: "Другое устройство находится в режиме телефонного разговора; повторите попытку позднее",
    call_connect_error: "Сетевая ошибка; повторите попытку позднее",
    net_connect_error: "Сетевая ошибка",
    device_offline_aready: "Устройство работает в автономном режиме,",
    offlineTime: "Время вне сети",
    connect_err: "Не удалось установить соединение",
    connect_error_help_one: "1. Проверьте, правильно ли работает сетевое подключение мобильного телефона и камеры, рекомендуется перезагрузить маршрутизатор и камеру.",
    offline_help_tip_reconect: "повторите подключение",
    dot_for_modal: ".",
    connect_error_help_two: "2. Если пароль Wi-Fi или сеть были изменены, рекомендуется",
    connect_error_help_three: "3. Если вышеуказанные проблемы не могут быть решены, ",
    connect_error_help_four: "4. Если вышеуказанные действия не помогли решить проблему,",
    offline_help_tip_feed_question: "отзыв",
    know_button: "Понятно",
    offline_help_tip_one: "1. Проверьте, правильно ли работает сетевое подключение камеры, рекомендуется перезагрузить маршрутизатор и камеру.",
    offline_help_two: "2. Если источник питания в норме, проверьте световой индикатор:",
    offline_help_two_first: "Синий индикатор медленно/быстро мигает: Проверьте, подключена ли камера к сети. Если имя или пароль беспроводной локальной сети были изменены,",
    offline_help_two_two: "Желтый индикатор быстро мигает: Настройки камеры были сброшены",
    offline_help_tip_bind: "Повторить привязку",
    offline_help_two_three: "Желтый индикатор медленно мигает: Камера обновляется. Дождитесь завершения обновления, прежде чем приступать к работе. Не выключайте питание.",
    offline_three1: "3. Размещайте устройство как можно ближе к маршрутизатору и не допускайте наличия между ними препятствий, например стен. Чем ближе эти два устройства друг к другу, тем выше",
    offline_three2: "значение RSSI сети",
    offline_three3: "устройства, что теоретически приводит к более сильному сигналу сети. Рекомендуется, чтобы значение RSSI было больше -50.",
    offline_help_tip_the_first: "Непрерывно горит желтым:",
    offline_help_tip_connect_service: "Обратитесь в службу клиентской поддержки",
    offline_help_tip_the_thd: " для получения пакета обновления ПО.",
    offline_help_tip_the_sec: "Непрерывно горит синим/мигает синим:",
    offline_help_tip_forth: "4. Проверьте камеру",
    offline_help_tip_rssi: "информация о сети",
    offline_help_tip_forth_first: "значение RSSI, если значение ниже -50, попробуйте переместить ее ближе к маршрутизатору.",
    offline_help_four: "4. Если вышеуказанные проблемы не могут быть решены,",
    offline_help_five: "5. Если вышеуказанные действия не помогли решить проблему,",
    offline_help_tip_the: "3.  Проверьте индикаторы камеры:",
    offline_help_tip_fth: "5. Если вышеуказанные проблемы не могут быть решены, ",
    offline_help_tip_fth_old: "4. Если вышеуказанные проблемы не могут быть решены, ",
    offline_help_tip_green_blink: "Мигающий зеленый: пожалуйста",
    alarm_turn_on_str: "Активируйте домашнее наблюдение",
    fees_for_renewal: "Обновить",
    toolbar_text_resolution: "Разрешение",
    toolbar_text_sound: "Звук",
    toolbar_text_snap: "Моментальный снимок",
    toolbar_text_record: "Запись",
    toolbar_text_fullscreen: "Полный экран",
    select_all: "Выбрать все",
    unselect_all: "Отменить",
    cancel: "Отмена",
    commWaitText: "Пожалуйста, подождите...",
    ok_button: "OK",
    dialog_never_show: "Больше не показывать",
    cruise_seconds: "с",
    am: "AM",
    pm: "PM",
    year: "Г",
    month: "М",
    day: "Д",
    hour: "Ч",
    minute: "М",
    give_away: "Бесплатно",
    cloud_give_price_str: "Стоимость ",
    cloud_give_ok: "Возьми",
    picker_start_time: "Время начала",
    picker_end_time: "Время окончания",
    alarm_direction_custom: "Персонализация временных установок",
    step_preview: "Назад",
    account_next: "Далее",
    stayDetectStr: "Обнаружение движения",
    video_downloading: "Выполняется загрузка...  ",
    noAlarmVideo: "Нет доступных видеозаписей",
    cloud_time_out_str: "Срок действия умного плана уже истек",
    buy_again: "Продлить сейчас",
    cloudExpireShowImageHint: "Приобретите облачное хранилище для облачного резервного копирования видео >",
    network_available_need_check: "Сеть недоступна. Проверьте настройки сети",
    housekeeping_no_event: "На данный момент содержимое отсутствует",
    commLoadingFailText: "Не удалось загрузить, повторите попытку",
    commLoadingMoreDataText: "Загружаются дополнительные данные, подождите...",
    commNoMoreDataText: "Все данные загружены",
    commLoadingClickText: "Нажмите, чтобы обновить",
    abnormal_event_fire: "Ваша дверь обнаружила возгорание",
    abnormal_event_damaged: "Вашу дверь взломали",
    abnormal_event_anti_password: "Оповещение о защите от взлома паролем",
    abnormal_event_anti_fingerprint: "Оповещение о защите от взлома отпечатком пальца",
    abnormal_event_password: "Оповещение о попытке разблокировки паролем",
    abnormal_event_fingerprint: "Оповещение о попытке разблокировки отпечатком пальца",
    abnormal_event_nfc: "Оповещение о попытке разблокировки с помощью NFC",
    abnormal_event_face: "Оповещение о попытке разблокировки лицом",
    abnormal_event_multiple: "Оповещение о попытке разблокировки двойной валидацией",
    abnormal_event_door_time_out: "Дверь не закрыта в течение длительного времени",
    abnormal_event: "Аномальное событие",
    lingerChangeText: "Событие фиксации человека",
    alarm_change_string: "На участке наблюдения зафиксирована смена картинки",
    doorbellPressTitle: "Кто-то нажимает на дверной звонок",
    downloadingDeleteError: "Выполняется загрузка, повторите попытку позднее",
    delete_success: "Удаление успешно выполнено",
    delete_failed: "Удаление не выполнено",
    video_downloaded: "Загрузка завершена",
    save_failed: "Сохранение не выполнено",
    delete: "Удалить",
    chargeGuide: "Подсказка по зарядке",
    category_doorbell: "дверной звонок",
    category_camera: "Камера",
    chargeGuideSubtitle: "Для полной зарядки камеры с помощью блока питания 5 В/2 A потребуется около 5 часов.",
    normalCharge: "Способ зарядки 1",
    hubCharging: "Способ зарядки 2",
    hubConnectHintTitle: "Для обеспечения хорошего сигнала рекомендуется:",
    hubConnectHintSubtitle: "1. Попробуйте установить концентратор выше над землей. \n2. Если концентратор подключен через Ethernet, разместите его на расстоянии около 0,5 м от маршрутизатора. \n3. Разместите концентратор на расстоянии около 0,5 м от стен. \n4. Избегайте размещения концентратора в углах.",
    button_finish: "Готово",
    button_add_device: "Добавить еще устройства",
    cameraTestTips2: "Если сигнал сети на месте установки слабый, это может привести к сбоям в соединении или проблемам с видео",
    noSignalText: "Нет сигнала",
    perfectText: "Идеальный",
    strongText: "Сильный",
    goodText: "Хороший",
    weakText: "Слабый",
    cameraTestStrong: "Текущее местоположение хорошо подходит для установки %{code}",
    cameraTestGood: "Текущее местоположение подходит для установки %{code}",
    cameraTestWeak: "В текущем местоположении сигнал слабый, переместите %{code} ближе к концентратору",
    cameraTestNoSignal: "В текущем местоположении сигнал отсутствует. Расположите %{code} ближе к своему концентратору",
    no_sd_card_go_buy_cloud: "Приобретите Умный план",
    downloadTip: "Включите воспроизведение видео, которое нужно загрузить",
    alarm_download_downloading: "Выполняется загрузка...",
    storage_no_sdcard_please_buy: "Нет карты Micro SD, пожалуйста, вставьте или купите",
    no_sd_card_tips_text: "Для доступа к полной видеозаписи купите Умный план",
    cameraTestWifiSpeedTitle: "Инструкции по установке",
    cameraTestTipsTitle1: "Руководство по установке",
    cameraTestTips1: "Расположите %{code} как можно ближе к концентратору, выполните проверку скорости интернета для вашего телефона и %{code} перед установкой.",
    re_play: "Воспроизведение",
    doorbellIndicatorLightPageTitle: "Описание индикатора дверного звонка",
    doorbellPressLight: "Звонок в наружную дверь",
    doorbellPressSubtitle: "Нажмите на кнопку дверного звонка. Появится индикатор края кнопки дверного звонка с бегущим световым эффектом, и вы сможете услышать звонок в дверь",
    cameraOnSubtitle: "Нажмите и удерживайте кнопку синхронизации в течение 2 секунд до появления синего света и проигрыша мелодии включения питания.",
    cameraOffSubtitle: "Нажмите и удерживайте кнопку синхронизации в течение 8 секунд до однократного мигания красного индикатора и проигрыша мелодии выключения питания.",
    doorbellUsbChargeTitle: "Для зарядки дверного звонка используйте USB-кабель",
    usbChargeSubtitle: "Зарядка начинается после троекратного мигания красного индикатора",
    doorbefullChargeTitle: "Зарядка дверного звонка завершена (полностью заряжен)",
    fullChargeSubtitle: "Непрерывно горит зеленым",
    cameraResetSubTitle: "Нажмите и удерживайте кнопку синхронизации в течение 5 секунд, пока не начнет мигать синий индикатор, затем отпустите кнопку и запустите процесс сброса. Камера перезапустится в процессе сброса.",
    reset_success_text: "Сброс осуществлен успешно",
    resetSuccessSubtitle: "Мигает синим",
    pairedSuccessTitle: "Сопряжение выполнено",
    pairedSuccessSubtitle: "Непрерывно горит синим в течение 5 секунд",
    pairedFailTitle: "Сопряжение не выполнено",
    pairedFailSubtitle: "Светодиодный индикатор 3 раза мигает синим и оранжевым цветами",
    pirSensitivityAdjustSubtitle: "При настройке чувствительности ПИК-датчика индикатор будет мигать оранжевым при каждом обнаружении человека ПИК-датчиком. При длительном обнаружении человека индикатор будет мигать оранжевым непрерывно.",
    pirGetPeopleTitle: "когда камера обнаруживает движение",
    pirGetPeopleSubtitle: "Однократное мигание оранжевого индикатора",
    pirAlwaysGetPeopleTitle: "когда камера постоянно обнаруживает движение",
    pirAlwaysGetPeopleSubtitle: "Оранжевый постоянно мигает",
    gateWayLightPageTitle: "Описание светодиодных индикаторов концентратора",
    light_blue: "Синий индикатор",
    light_blue_flash: "Мигает синим",
    light_blue_flash_case: "Выполняется подключение к сети (не подключено)",
    light_blue_on: "Непрерывно горит синим",
    light_blue_on_case: "Подключение к сети выполнено (подключено)",
    light_orange: "Оранжевый индикатор",
    light_orange_flash: "Мигает оранжевым",
    light_orange_flash_case: "Ожидание подключения (выполняется сопряжение устройства и приложения)",
    light_orange_on: "Непрерывно горит оранжевым",
    light_orange_on_case1: "Загрузка",
    light_orange_on_case3: "Выполняется обновление встроенного ПО",
    light_blue_orange_mix_head: "Синий/",
    light_blue_orange_mix_tail: "Оранжевый индикатор",
    light_blue_orange_mix_flash: "Светодиодный индикатор 3 раза мигает синим и оранжевым цветами",
    light_blue_orange_mix_flash_case: "Ошибка обновления встроенного ПО",
    gateway_reset: "Сброс настроек концентратора",
    gateway_reset_hint1: "В ходе процесса сопряжения концентратора, если индикатор не мигает оранжевым цветом, выполните сброс настроек концентратора.",
    gateway_reset_hint2: "Зажмите и удерживайте кнопку сброса на тыльной стороне концентратора в течение 5 секунд до момента, когда индикатор начнет непрерывно гореть оранжевым, затем отпустите кнопку. Во время сброса настроек произойдет перезагрузка концентратора. Когда индикатор начинает мигать оранжевым, это означает, что сброс выполнен успешно и можно начинать сопряжение.",
    indicatorLightPageTitle: "Описание светодиодных индикаторов камеры",
    cameraOnOffLight: "Включение/выключение питания камеры",
    cameraOnTitle: "Включение питания камеры",
    cameraOffTitle: "Выключение питания камеры",
    cameraChargeTitle: "Зарядка камеры",
    usbChargeTitle: "Для зарядки камеры используйте USB-кабель",
    fullChargeTitle: "Камера полностью заряжена.",
    sunChargeTitle: "Для зарядки камеры используйте солнечную панель",
    cameraResetTitle: "Сброс камеры",
    pirSensitivityAdjustTitle: "Регулировка чувствительности обнаружения движения",
    injectSuccess: "Извлечение выполнено успешно",
    sdcard_format_success: "Форматирование выполнено успешно",
    sdcard_format_fail: "Не удалось отформатировать",
    sdCardFormating: "Форматирование SD-карты",
    injectFailed: "Не удалось извлечь",
    noSdCardTitle: "В настоящее время SD-карта отсутствует",
    injectSdCardTitle: "Безопасное извлечение SD-карты",
    injectSdCardSubtitle: "Извлеките SD-карту безопасным способом, чтобы предотвратить повреждение записанного файла",
    sdCardFormat: "Форматировать SD-карту",
    sdCardDamaged: "Ошибка карты памяти",
    injectSdCardHint: "Подтвердить безопасное извлечение SD-карты?",
    formatTitle: "Подтвердить форматирование SD-карты?",
    formatMessage: "При форматировании содержимое SD-карты будет очищено",
    stayTimeStr: "Режим обнаружения",
    houseDetectedPeopleStr: "При фиксации посторонних лиц перед домом",
    peopleRecordPowerMoreStr: "Запись осуществляется немедленно",
    peopleRecordPowerMoreSubStr: "Записывать при срабатывании детектора движения.  (высокое потребление энергии)",
    peopleRecordPowerCommonStr: "Запись осуществляется через заданное время",
    peopleRecordPowerCommonSubStr: "Записывать, только если кто-то надолго задерживается. (среднее потребление энергии)",
    peopleRecordPowerLessStr: "Выключить",
    peopleRecordPowerLessSubStr: "Обнаружение движения отключено (меньшее потребление энергии)",
    firstSetStayTimeFailed: "Используются настройки по умолчанию. Для настройки перейдите в раздел [Настройки наблюдения за жилищем]",
    commTitleSettingText: "Настройки",
    feature_set: "Настройка функции",
    comm_setting_title: "Общие настройки",
    device_name: "Имя устр-ва",
    check_update: "Обновления",
    shareUser_tip: "Нет разрешения для общего устройства",
    shared_setting: "Устройства с коллективным доступом",
    confirm_deletion_device: "Подтвердить удаление устройства?",
    preset_sleep_set: "Настройка ракурса для спящего режима",
    comm_setting_remove_device: "Удалить устройство",
    update_device_name: "Сменить имя устройства",
    input_name: "Введите имя",
    settings_set_success: "Настройка выполнена успешно",
    play_back_text_all: "Воспроизведение всех видео ",
    wake_up: "Возобн.",
    operationFailed: "Сбой настройки ",
    people_event: "Фиксация чел-ка",
    move_event: "Фиксация движения ",
    alarm_loud_switch: "Фиксация звука",
    no_human_event: "Автоматическое обнаружение",
    fence_detect_switch: "Обнаружение пересечения ограждения",
    cry_event: "Плач",
    keyAreaDetectStr: "Обнаружение зон активности",
    moveEvent: "Обнаружение движения",
    peopleEvent: "Обнаружение человека",
    soundEvent: "Обнаружение громких звуков",
    bottom_house_keeping: "События",
    str_housekeeping_tip_guide_people: "o Фиксация людей в зоне наблюдения",
    str_housekeeping_tip_guide_sound: "o Обнаружен нетипичный звук",
    str_housekeeping_tip_guide_fence: "Обнаружение пересечения ограждения в зоне наблюдения",
    str_housekeeping_tip_guide_nobody: "o В охраняемой зоне никто не появлялся",
    str_housekeeping_tip_guide_move: "o Фиксация изменений экрана в зоне наблюдения",
    str_housekeeping_tip_guide_cry: "o Фиксация плача",
    str_housekeeping_tip_guide_important: "o В ключевой зоне зафиксировано присутствие человека.",
    str_housekeeping_tip_title: "При активации дом. наблюдения камера，сохранит фото оповещений и уведомит вас",
    go_to_open: "Открыть",
    delete_alert: "Подтвердить удаление?",
    delete_title_loading: "Удаление...",
    cloudTip: "Активируйте облачное хранилище для неограниченного хранения данных, обеспечивая более подробную информацию о событиях",
    x_flat_list_no_more_data: "Все данные загружены",
    downLoadTitle: "Загрузка",
    select_tip: "Выбрать файлы",
    delete_title: "Удаление",
    save_success: "Сохранение успешно выполнено",
    buy_cloud_for_info: "Приобретите облачное хранилище и включите облачное резервное копирование",
    download_system_album: "Загрузка выполнена успешно",
    video_download_fail: "Загрузка не выполнена",
    delete_alert_tip: "Это видео содержит",
    delete_alert_tip1: "событие(-я), продолжить удаление?",
    bottom_house_video_keeping: "Подробности видео",
    currentPlay: "Воспроизводится в данный момент",
    loadMsgInfo: "Сообщения",
    loadMsgInfoError: "Загрузка сообщений",
    bottom_video_album: "Альб.",
    select_title_1: "Выполните выделение",
    album_video_play: "Воспроизведение",
    album_video_pause: "Пауза",
    album_video_mute: "Без звука",
    album_video_voice: "Звук",
    album_video_full_screen: "Полный экран",
    video_download_fail_warning: "Выполняется выгрузка видео; повторите попытку позднее",
    bottom_cloud_storage: "Облако IMI",
    change_event_str: "Переключить событие",
    open_setting: "настраивать",
    no_video_data_new: "Нет доступных видеозаписей",
    playback_no_video_data_tip: "Ой! Сегодня доступных видеозаписей нет. Переключитесь на другой день",
    date_format_yyyy_mm_dd: "yyyy/MM/dd",
    panoramicSuccess: "Панорамное изображение создано",
    panoramicError: "Ошибка создания панорамного изображения, повторите позже",
    direction_end_009: "Поворот невозможен",
    isDataUsageTip: "Мобильная сеть, на автопаузе",
    Panoramic_loading: "Создание панорамного изображения…",
    set_onekey_tit: "Установить включение сигнализации по нажатию одной кнопки?",
    set_onekey_msg: "После настройки можно нажать кнопку, чтобы включить световую сигнализацию и звуковое оповещение.",
    set_onekey_sure: "Задать",
    popo_setting_storage_text: "Настройки хранилища",
    sdcard_error_out: "Карта Micro SD извлечена,Пожалуйста, снова вставьте или извлеките карту Micro SD.!",
    audio_permission_denied: "Нет разрешения аудиозаписи",
    panoramicing_tip: "Создание панорамного изображения…",
    camera_guide_for_zoomed_str: "Двойн. кас./сведение для\n увел./уменьш. экрана",
    camera_guide_for_panoramic_str: "Для входа в режим панорамы коснитесь здесь",
    expiredHint: "   Срок действия вашего облачного хранилища истекает сегодня; в дальнейшем вы не сможете просматривать или записывать видео",
    sdcard_tip_cancel: "Понятно.",
    sd_need_format_no_index_massage: "Для записи в нормальном режиме отформатируйте карту SD. Форматировать и очистить карту SD сейчас?",
    sd_need_format_has_old_index_massage: "Программное обеспечение устройства обновлено, для улучшения качества воспроизведения отформатируйте карту SD. Форматировать и очистить карту SD сейчас?",
    is_the_calibration: "Выполнение калибровки...",
    calibration_completed: "Калибровка камеры выполнена успешно",
    netWorkError: "Сетевое подключение прервано",
    operate_time_out: "Время ожидания для операции",
    hotspot_connect_hint: "Выполните подключение к устройству \"imi_xxxxxx\" и вернитесь на этот экран",
    goto_wifi: "Выберите сеть Wi-Fi",
    play_back_text: "Воспр",
    sdcard_status7: "Карта памяти не инициализирована",
    toolbar_text_sleep: "Спящий режим",
    quality_auto: "Автоматически",
    sdcard_format_title_tips: "Отформатируйте SD-карту!",
    sdcard_format_title_tips_content: "Карта памяти не инициализирована, перед нормальным использованием ее необходимо отформатировать. При форматировании все данные на карте памяти будут удалены. Хотите начать форматирование?",
    upgrade_state_content: "На устройстве установлена последняя версия прошивки",
    upgrade_state_content_end: "Обновить сейчас",
    sleepTipTitle: "Спящий режим устройства",
    sleepTipContent: "Камера перестанет работать и записывать видеоинформацию",
    cloud_will_time_out_str: "Срок действия Умного плана скоро истечет",
    cloud_time_out_effect_str: "Услуга умного плана недоступна после истечения срока действия",
    temporarily_not: "Временно нет",
    formatting_btn: "Форматирование",
    storageCardFormating: "Карта Micro SD форматируется...",
    waitFailedTip: "Попробуйте позже",
    targetPushTitle_subtitle: "нажмите, чтобы устранить проблему",
    targetPush_sdcard_format: "Перед использованием SD-карты ее необходимо инициализировать!",
    sdcard_status_error: "Ошибка карты памяти",
    get_success: "Получено успешно",
    Panoramic_tip: "Чтобы управлять обзором камеры, нажмите на значок панорамы",
    Panoramic_title: "Создать панорамное изображение",
    Panoramic_title_reset: "Создать панорамное изображение снова?",
    storage_services: "услугой облачного хранилища",
    storage_services_content: "Какая служба хранения облаков?\n",
    storage_services_title: "Привилегии облачного хранилища",
    imi_cloud_storage_rights_title1: "Поддержка в режиме 24×7",
    imi_cloud_storage_rights_detail1: "Загрузить видеофайл",
    imi_cloud_storage_rights_title2: "Интеллектуальное слежение",
    imi_cloud_storage_rights_detail2: "Ни одной детали не упущено",
    imi_cloud_storage_rights_title3: "Облачное хранилище",
    imi_cloud_storage_rights_detail3: "Воспроизведение в любое время",
    imi_cloud_storage_rights_title4: "Кодированная выгрузка",
    imi_cloud_storage_rights_detail4: "Многоуровневая защита личных данных",
    storage_after: "Позднее",
    imi_cloud_experience_now: "Опыт",
    play_back_tit: "Воспроизведение видео",
    play_back_change_time: "Время переключения",
    alarmText: "События",
    goto_live_view: "Видеотрансляция",
    str_housekeeping_tip_value: "- в зоне наблюдения обнаружены люди\n- в зоне наблюдения обнаружены изменения на экране\n- обнаружен нетипичный звук",
    common_error: "Не удалось загрузить, проверьте статус устройства",
    sdCardName: "Карта SD",
    no_video_data_failed: "Не удалось получить видеофайл",
    retry_connect: "Повторить подключение",
    collapse: "Свернуть",
    noData: "Нет данных, выберите заново",
    playback_no_event: "Нет событий",
    keyArea: "Зона активности",
    change_date: "Переключить дату",
    todayTitle: "Сегодня",
    preDayTitle: "Вчера",
    sdcard_status0: "Хорошо",
    sdcard_status1: "Нет Micro SD-карты",
    sdcard_status6: "Карта памяти заполнена",
    sdcard_status3: "Ошибка",
    sdcard_status4: "Выполнение форматирования",
    sdcard_status5: "Извлеките карту Micro SD",
    sdcard_status9: "Исправление",
    sdcard_status10: "Извлечение",
    sdcard_status2: "Пространство для хранения данных закончилось",
    sdcard_status_normal_new1: "Состояние карты памяти",
    play_back_text_all_title: "Все видео",
    max_download_limit: "Поддерживается только одна загрузка",
    delete_failed_limit: "До 50 операций одновременно",
    delete_connect_failed: "Подключение устройства прервано",
    delete_failed_inPlay: "Воспроизводимые видеоролики нельзя удалить",
    sensitivity_for_high_tit: "Высокая чувствительность",
    sensitivity_for_low_tit: "низкая чувствительность",
    alarm_sensitivity: "Чувствительность тревоги",
    sensitivity_for_high_subtit: "Обнаружение движущихся людей или объектов, любая проблема не будет упущена",
    sensitivity_for_low_subtit: "Вызовите полицию, когда в доме большое движение",
    date_picker_time_title: "Выберите время начала и окончания",
    alarm_time_set_time: "Выбор периода наблюдения",
    time_equal: "Время окончания не может совпадать с временем начала",
    alarm_time_night: "Наблюдение в ночное время",
    alarm_time_all: "Круглосуточное наблюдение",
    alarm_time_day: "Дневное наблюдение",
    alarm_time_set: "Время мониторинга",
    full_color_vision_title: "Цветное ночное видение",
    alarm_time_24: "24 часа",
    alarm_time_info: "Оповещение при обнаружении движения или человека",
    alarm_time_208: "20:00 – 08:00 следующего дня",
    fullColor_smart_tit: "Функция интеллектуального ночного видения",
    alarm_time_820: "8:00 – 20:00",
    noDIYTimeTip: "Персонализируйте временные установки",
    voice_for_wu: "НЕТ",
    voice_for_warning: "Звук сигнализации",
    voice_for_dingdong: "Звук дверного звонка",
    voice_for_welcome: "Здравствуйте! Добро пожаловать",
    voice_for_area: "Вы вошли в зону наблюдения",
    voice_for_closedoor: "Пожалуйста, закройте за собой дверь.  Спасибо!",
    voice_for_safe: "Пожалуйста, соблюдайте осторожность",
    voice_for_stairs: "Пожалуйста, соблюдайте осторожность при подъеме и спуске по лестнице",
    voice_for_dangerArea: "Опасная зона! Пожалуйста, покиньте зону",
    allDay_time: "Круглосуточное наблюдение",
    potlight_flash: "Мигающая световая индикация",
    potlight_not_bright: "Без световой индикации",
    potlight_long_bright: "Световая индикация включена",
    day_time: "Дневное наблюдение",
    night_time: "Ночное наблюдение",
    voice_for_custom: "Пользовательский режим",
    soundLightAlarm: "Акустооптическое оповещение",
    only_people_detected: "Обнаружение только людей",
    effective_time: "Выбрать период наблюдения",
    tip_voice_selected: "Выбрать звук уведомления",
    potlight_alarm_mode: "Настройки лампы сигнализации",
    potlight_contain_time: "Продолжительность",
    click_warning_tit: "Включение сигнализации по нажатию одной кнопки",
    local_device_connect_wifi: "Выберите сеть Wi-Fi",
    comm_setting_faq: "ЧаВо",
    angelMoreSetting: "Дополнительные настройки >>",
    voice_for_enter_name: "Введите имя",
    max_device_name: "Название не должно быть выше 15 букв",
    customer_phone: "Phone Number：************",
    customer_email: "Email: <EMAIL>",
    customer_wx: "WeChat Official Accounts：创米数联",
    customer_web: "Вебсайт: www.imilabglobal.com",
    voice_for_add: "Добавить один",
    voice_for_edit: "Редактировать",
    imi_save: "Сохранить",
    voice_for_name: "имя",
    imi_input_text_tip: "Этот символ не поддерживается",
    voice_for_tip_tit_time: "Выбор звука сигнализации",
    voice_for_re_record: "Перезаписать",
    voice_for_click_record: "Удерживайте и говорите",
    nursingTimeSetting: "Настройка периода наблюдения",
    show_upgrade_app: "Пожалуйста, обновите приложение до последней версии",
    imi_fence_tit: "Если объект или человек пересекает граничную линию в направлении стрелки, это считается событием пересечения ограждения.",
    imi_switch_dection: "Изменение направления стрелки",
    alarm_event_tit: "Событие наблюдения за домом",
    move_track: "Отслеживание движений",
    settings_alarm_human_track_title: "Слежение",
    no_human: "Автоном. фиксация",
    alarm_event_manager: "Управление домашним наблюдением",
    alarmTimeSetting: "Интервал оповещения",
    time_minutes: "мин",
    settings_alarm_push_time_sub: "При наложении оповещений увеличьте интервалы",
    message_push_manager: "Управление уведомлениями",
    message_push_phone: "Отправить на тел. при фиксации движения",
    tip_time_minute: "мин.",
    recordType: "Режим записи",
    recordType1: "Изображение",
    recordType2: "Видео",
    recordTypeSub: "Активируйте облачное хранилище, чтобы переключиться в режим видео",
    alarm_info_set: "Настройки типов оповещений",
    alarm_sound_detection: "Обнаружение громких звуков",
    detection_sensitivity: "Чувствительность обнаружения",
    alarm_info_push: "Отправка тревожных сообщений",
    message_push_switch: "Переключатель отправки тревожных сообщений",
    preset_opened: "Открыл",
    preset_closed: "Закрыть",
    audio_broadcast: "Голосовое напоминание",
    black_white_vision_title: "Черно-белое ночное видение",
    settings_light_title: "Индикаторы статуса",
    Data_usage_warning: "Защита от потока",
    data_usage_warning_intro: "Не воспроизводится автоматически в мобильных сетях",
    intelligent_cruise: "Умный контроль",
    settings_switch_off: "Выкл.",
    cruise_all_view: "развертка",
    cruise_favorite: "Предустановка",
    sleep_set: "Настройки сна",
    setttings_infared: "Настройки ночного видения",
    settings_flip_title: "Поворот изображения",
    settings_flip_subtitle: "Включите эту ф-цию при перевернутом положении камеры",
    imageSetting: "Настройки отображения",
    setting_picture_setting: "Доп. настройки изображения",
    imi_camera_correct_pos: "Калибровка шарнирного держателя",
    setting_reset: "Перезагрузка устройства",
    calibration_to_continue_30: "Выполнение калибровки камеры занимает примерно 30 секунд. Продолжить?",
    calibration_to_continue: "Выполнение калибровки камеры занимает примерно 25 секунд. Продолжить?",
    calibration_failure: "Сбой калибровки камеры",
    setting_reset_msg: "Перезагрузка устройства займет некоторое время. Подтвердить перезагрузку?",
    settings_watermark_title: "Фоновое отображение времени",
    pictureCorrection: "Корректировка искажения объектива",
    pictureCorrectionSubtitle: "При включении искажения уменьшаются, но угол обзора сужается.",
    wdrMode: "Режим широкого динамического диапазона (WDR)",
    audio_broadcast_switch: "Голосовое напоминание",
    rn_version: "№ версии плагина:",
    network_info: "Сведения о сети",
    wifi_signal_0: "Сигнал новой сети Wi-Fi отсутствует",
    wifi_signal_2: "Слабое сетевое соединение устройства.",
    wifi_signal_5: "Хорошее сетевое соединение устройства.",
    wifi_name: "Название сети Wi-Fi",
    wifi_strength: "Сила сигнала Wi-Fi",
    wifi_rssi: "RSSI",
    wifi_loss: "Скорость потери пакетов",
    wifi_mode: "Текущий режим",
    wifi_mode_type2: "Подключение к LAN",
    wifi_mode_type1: "Подключение к WAN",
    wifi_ip_address: "IP-адрес",
    wifi_mac_address: "MAC-адрес",
    settings_light_full_color_title: "Тусклое освещение, полная цветность",
    settings_light_full_color_subtitle: "В условиях недостаточной освещенности цветные изображения все равно видны",
    fullColor_title3: "Автоматическое переключение",
    fullColor_subTit: "Светодиодное освещение ночью, круглосуточный мониторинг с цветным изображением",
    fullColor_title2: "Вкл.",
    fullColor_black_subTit: "При низкой освещенности активируется инфракрасное видение",
    fullColor_title1: "Выкл.",
    fullColor_smart_subTit: "Ночное инфракрасное видение с переключением в полноцветное ночное видение при обнаружении человека",
    empty_start_end_tips: "Пожалуйста, выберите время начала и время окончания",
    add_time_period: "Задать период",
    not_set: "Для установки",
    plug_timer_repeat: "Повтор",
    do_once: "Однократный запуск",
    do_everyday: "Ежедневно",
    do_weekday: "Будни",
    do_weekend: "Выходные",
    do_custom: "Персонализация",
    nobody_detect_name: "Имя диапазона",
    date_picker_time_hint: "Время начала не может быть равно или позже времени окончания",
    nobody_time_no_more_than_10: "Автоном. фиксация Интервал времени не превышает 10 сек!",
    nobody_push_warning: "* Вкл. push-уведомления imilab Home. Событий нет, push-уведомлений нет.",
    delete_time_warning: "Удалить элемент настройки?",
    save_the_open: "Сохранить и включить",
    presetAngleSleep: "Ракурс для спящего режима ",
    Preset_Sleep_subTitle: "Устройство переходит в спящий режим в этом местоположении",
    preset_sleep_set_holder: "В настоящий момент ракурс для спящего режима отсутствует. Нажмите на значок PTZ, чтобы начать настройку",
    save_current_angle_sleep: "Сохранить и включить текущий ракурс для спящего режима?",
    action_success: "Операция выполнена успешно",
    Preset_Sleep: "Местоположение для спящего режима",
    closeStr: "Выкл.",
    settings_switch_on: "Вкл.",
    voice_for_max: "Поддерживается до 5 пользовательских аудиосигналов",
    alarm_only_people_detected: "Обнаружен чел-к",
    settings_alarm_push: "Отпр. cообщ. при фиксации движений",
    alarm_loud_push: "Отпр. сообщ. при фиксации звука",
    fence_detect_push: "Обнаружение пересечения ограждения",
    alarm_crying_push: "Оповестить при фиксация плача",
    setting_record_model_always: "Всегда осуществлять запись",
    setting_record_model_close: "Отключить функцию записи",
    storageCardHinting: "Извлечение карты памяти",
    sd_suspended: "Пауза",
    sdcard_status_normal_new: "Нормальный",
    sdcard_status_more_new: "Оставшееся пространство",
    sd_storage_switch: "Переключатель памяти",
    setting_record_model: "Режим записи",
    record_quality: "Разрешение записи",
    storageCardFormat: "Отформатируйте карту Micro SD",
    sd_card_use_hint_062: "Советы по использованию карты памяти\n1. Устройство поддерживает карты памяти класса скорости 4 и более высокого.\n2. Формат карт памяти: FAT323. Используйте подлинные карты памяти. Совместимость с аналогами, низкокачественными и восстановленными картами не гарантируется.\n4. Максимальный поддерживаемый объем: 256 ГБ",
    sdcard_tip1_new: "1.Извлеките SD-карту перед ее отключением。",
    sdcard_tip2_new: "2.Видео на карте Micro SD не зашифровано, чтобы легко просматривать локальные видео, храните карту Micro SD в безопасном месте.",
    sdcard_out_already: "Карта Micro SD отключена",
    sdcard_status_abnormal: "Аномальный",
    sdcard_status8: "Недостаточно места, замените карту",
    setting_record_model_always_title: "Храните все записи на карте Micro SD",
    setting_record_model_close_title: "Камера не будет записывать и сохранять видео на карту Micro SD после окончания записи",
    storageCardHint: "Вы уверены в желании безопасно извлечь SD-карту?",
    setting_record_model_move: "Записывать только при фиксации движения",
    sdcard_status_more: "Осталось свободного места",
    sdcard_exit: "Извлечь карту Micro SD",
    sdcard_tip1: "1. Перед извлечением SD-карты нажмите на нее",
    sdcard_tip2: "2. Видеофайлы на карте Micro SD не зашифрованы для облегчения их просмотра. Храните карту памяти в надежном месте.",
    setting_record_model_move_title: "Для экономии памяти запись включается при фиксации изменения изображения",
    more_store_setting: "Управление хранилищем",
    settings_sdcard_title: "Состояние микро SD-карты",
    sleep_title: "Спящий режим",
    Preset_Sleep_subTitle2: "Устройство переходит в спящий режим в этом местоположении",
    timer_sleep_title: "Запланированный во времени спящий режим",
    upload_grade_success: "Обновление прошло успешно",
    list_item_curr_version: "Текущая версия",
    list_item_latest_version_now: "Текущая версия является последней",
    getError: "Не удалось получить, повторите попытку позднее",
    upgrade_button: "Обновить сейчас",
    list_item_version_status_4: "Установка выполняется",
    list_item_latest_version_uploading_title: "Выполняется обновление...",
    list_item_latest_version_uploading: "Выполняется обновление; не выключайте и не используйте устройство до завершения обновления",
    list_item_latest_version_upload_finish: "После обновления устройство перезагрузится",
    upload_grade_timeout: "Временной лимит обновления исчерпан",
    upload_grade_error: "Ошибка обновления",
    list_item_latest_version: "Последняя версия",
    light_blue_orange_mix_flash_success: "Обновление OTA прошло успешно",
    list_item_latest_version_log: "Журнал обновлений",
    comm_config_wifi_title: "Выберите рабочий Wi-Fi для устройства",
    wifiSettingTipsText: "Устройство поддерживает только соединения Wi-Fi на частоте 2,4 ГГц. В имени Wi-Fi можно использовать только английские символы и цифры",
    comm_config_wifi_ssid_hint1: "На текущий момент соединения по сети Wi-Fi не установлено",
    comm_config_wifi_password_hint: "Введите пароль сети Wi-Fi",
    local_device_bind_wifi_failed: "Не удалось выполнить сопряжение устройства. Перезагрузите его и повторите попытку",
    local_device_bind_wifi_success: "Устройство сопряжено. Работа плагина будет завершена",
    local_device_bind_wifi_binding: "Сопряжение устройства…",
    wdrFunctionHint: "Затемненные и засвеченные участки видео могут содержать больше деталей при включенном режиме WDR.",
    wdr_before: "Нормальный режим",
    wdr_after: "Режим WDR",
    wdrHint: "Примечание. \n1. Режим WDR не работает в черно-белом режиме ночного видения. \n2. WDR влияет на цвет водяного знака.",
    sdCardRemain: "Занято: %{code} ГБ",
    sdCardTotal: "Объем: %{code} ГБ",
    sdCardLeft: "Предположительное время записи в цикле – %{code} дн.",
    onPlayErrorText: "Ошибка видеотрансляции (%{code})",
    onPlayErrorMaxText: "Устройство превысило максимальное количество подключений (%{code}).",
    expiredCountdownHint: "Срок действия вашего пакета облачного хранилища истечет через %{code} дн. По истечении этого срока вы не сможете просматривать и записывать видео",
    on_live_play_error: "Не удалось открыть камеру (%{code}), повторите попытку позднее!",
    select_title_3: "Выбрано элементов: %{code}",
    click_too_fast: "Слишком много нажатий"
};
